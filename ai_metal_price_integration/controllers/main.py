from odoo import http, _
from odoo.http import request
import json
import logging
import datetime
from odoo.addons.website_sale.controllers.main import WebsiteSale

_logger = logging.getLogger(__name__)

# Direct route for product page to bypass template errors - Disabled due to template not found error
# class CustomWebsiteSale(WebsiteSale):
#     @http.route(['/shop/<model("product.template"):product>'], type='http', auth="public", website=True)
#     def product(self, product, category='', search='', **kwargs):
#         """Direct route for product page to bypass template errors"""
#         # Use our simple product page template directly
#         values = {
#             'product': product,
#             'product_variant': product.product_variant_id,
#             'category': category,
#             'search': search,
#             'website': request.website,
#             'currency_id': request.website.currency_id,
#         }
#         return request.render("ai_metal_price_integration.simple_product_page", values)

class MetalPriceIntegration(http.Controller):

    @http.route('/shop/product/price', type='json', auth='public', website=True)
    def get_product_price(self, product_id):
        """Get current price for a product based on current metal rates"""
        try:
            product_id = int(product_id)
            product = request.env['product.product'].sudo().browse(product_id)

            if not product.exists():
                return {'success': False, 'error': 'Product not found'}

            if not product.is_jewelry:
                return {
                    'success': True,
                    'price': product.lst_price,
                    'formatted_price': request.env['ir.qweb.field.monetary'].value_to_html(
                        product.lst_price, {'display_currency': request.website.currency_id}
                    )
                }

            # Calculate current price based on current metal rates
            current_price = product._calculate_jewelry_price()

            # Get price components for display
            components = product.get_price_components()

            # Log components for debugging
            _logger.info("Price components for product %s: %s", product.name, components)
            if components and 'components' in components:
                margin_components = [c for c in components['components'] if c.get('type') == 'margin']
                _logger.info("Margin components: %s", margin_components)

            return {
                'success': True,
                'price': current_price,
                'formatted_price': request.env['ir.qweb.field.monetary'].value_to_html(
                    current_price, {'display_currency': request.website.currency_id}
                ),
                'components': components
            }

        except Exception as e:
            _logger.error("Error getting product price: %s", str(e))
            return {'success': False, 'error': str(e)}

    @http.route('/shop/cart/add', type='http', auth="public", methods=['POST'], website=True, csrf=False)
    def cart_add(self, product_id=None, add_qty=1, **kw):
        """Override cart add to use current metal prices and create price lock"""
        _logger.info("Cart add called with product_id: %s, add_qty: %s, kw: %s", product_id, add_qty, kw)

        if not product_id:
            _logger.error("No product_id provided in cart add request")
            return request.redirect("/shop/cart")

        # Create a new cursor to avoid transaction conflicts
        new_cr = request.env.registry.cursor()
        env = request.env(cr=new_cr)

        try:
            product_id = int(product_id)
            add_qty = float(add_qty)
            _logger.info("Converted product_id to %s and add_qty to %s", product_id, add_qty)
        except ValueError as e:
            _logger.error("ValueError converting product_id or add_qty: %s", str(e))
            new_cr.close()
            return request.redirect("/shop/cart")

        try:
            # Get the sale order with the original environment
            sale_order = request.website.sale_get_order(force_create=True)
            if not sale_order:
                _logger.error("Failed to get or create sale order")
                new_cr.close()
                return request.redirect("/shop/cart")

            _logger.info("Got sale order: %s", sale_order.id)

            # Get the product
            product = request.env['product.product'].sudo().browse(product_id)
            _logger.info("Found product: %s, is_jewelry: %s", product.name, product.is_jewelry if hasattr(product, 'is_jewelry') else False)

            # Create a new cursor for the actual cart update to avoid transaction conflicts
            with request.env.registry.cursor() as cr:
                env = request.env(cr=cr)

                # Get the sale order in the new transaction
                order = env['sale.order'].sudo().browse(sale_order.id)

                # Get the product in the new transaction
                product_in_tx = env['product.product'].sudo().browse(product_id)

                if product_in_tx.exists() and hasattr(product_in_tx, 'is_jewelry') and product_in_tx.is_jewelry:
                    # Use our custom method to add to cart with current price
                    _logger.info("Adding jewelry product to cart with custom method")
                    line = order.add_to_cart(product_id, add_qty)
                    _logger.info("Added jewelry product to cart: %s", line.id if line else "None")
                else:
                    # Use standard method for non-jewelry products or if product doesn't exist
                    _logger.info("Adding product to cart with standard method")
                    result = order._cart_update(product_id=product_id, add_qty=add_qty, **kw)
                    _logger.info("Added product to cart with standard method: %s", result)

                # Commit the transaction
                cr.commit()

                # Update the session with the order ID
                request.session['sale_order_id'] = order.id

            # Log success
            _logger.info("Product %s added to cart successfully", product_id)

        except Exception as e:
            # Log the error
            _logger.error("Error adding product %s to cart: %s", product_id, str(e))
            import traceback
            _logger.error("Traceback: %s", traceback.format_exc())
            # Return error response for AJAX requests
            if request.httprequest.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return json.dumps({'error': str(e)})
            return request.redirect("/shop/cart")

        # Return success response for AJAX requests
        if request.httprequest.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # Get the last added line
            last_line = sale_order.order_line[-1] if sale_order.order_line else False

            # Prepare the lines data for the notification
            lines = []
            if last_line:
                lines.append({
                    'id': last_line.id,
                    'name': last_line.product_id.name,
                    'product_id': last_line.product_id.id,
                    'quantity': last_line.product_uom_qty,
                    'price_unit': last_line.price_unit,
                    'price_subtotal': last_line.price_subtotal,
                    'image_url': '/web/image/product.product/%s/image_128' % last_line.product_id.id,
                })

            # Add a message for the notification
            message = _("Product added to your cart")

            # For AJAX requests, we need to return a proper JSON response
            # that the showCartNotification function can use
            response_data = {
                'success': True,
                'cart_quantity': sale_order.cart_quantity,
                'amount': sale_order.amount_total,
                'lines': lines,
                'message': message
            }

            # Add notification_info which is expected by the frontend
            response_data['notification_info'] = {
                'title': _('Product added to your cart'),
                'message': message,
                'sticky': False,
                'warning': ''
            }
            _logger.info("Returning AJAX response for cart_add: %s", response_data)
            return json.dumps(response_data)
        return request.redirect("/shop/cart")

    @http.route('/shop/cart/update_json', type='json', auth="public", methods=['POST'], website=True, csrf=False)
    def cart_update_json(self, product_id, line_id=None, add_qty=None, set_qty=None, **kw):
        """Override cart update to use current metal prices and extend price lock"""
        try:
            # Call the standard WebsiteSale controller's cart_update_json method
            from odoo.addons.website_sale.controllers.main import WebsiteSale
            standard_controller = WebsiteSale()

            # Get the result from the standard controller
            result = standard_controller.cart_update_json(
                product_id=product_id,
                line_id=line_id,
                add_qty=add_qty,
                set_qty=set_qty,
                **kw
            )

            # Ensure notification_info is present and has the required properties
            if 'notification_info' not in result:
                result['notification_info'] = {
                    'title': 'Product added to your cart',
                    'message': 'Product added to your cart',
                    'sticky': False,
                    'warning': '',
                    'lines': [],
                    'currency_id': request.website.company_id.currency_id.id
                }
            elif 'lines' not in result['notification_info']:
                result['notification_info']['lines'] = []

            # Return the result
            return result
        except Exception as e:
            _logger.error("Error updating cart: %s", str(e))
            return {'success': False, 'error': str(e)}



    @http.route('/shop/checkout', type='http', auth="public", website=True)
    def checkout(self, **kw):
        """Override checkout to verify prices before proceeding"""
        order = request.website.sale_get_order()

        # Check if there are jewelry products in the order
        has_jewelry = order and any(line.product_id.is_jewelry for line in order.order_line)

        if has_jewelry:
            try:
                # Check if prices are still valid
                if not order.check_price_validity():
                    # Prices were updated, redirect to cart with notification
                    request.session['price_updated'] = True
                    return request.redirect("/shop/cart")
            except Exception as e:
                _logger.error("Error checking price validity: %s", str(e))
                # Continue with checkout even if price check fails

        # Proceed with standard checkout
        return request.website.sale_get_order().with_context(website_sale_checkout_redirection=True).checkout_redirection() or request.redirect('/shop/address')

    @http.route(['/shop/cart'], type='http', auth="public", website=True)
    def cart(self, **post):
        """Override cart page to show price update notifications"""
        # Get the standard controller
        from odoo.addons.website_sale.controllers.main import WebsiteSale
        # Create an instance of the standard controller
        website_sale = WebsiteSale()

        # Check if we need to refresh the rates
        refresh_rates = post.get('refresh_rates') or request.params.get('refresh_rates')
        timer_expired = post.get('timer_expired') or request.params.get('timer_expired')

        if refresh_rates:
            _logger.info("Refreshing rates due to refresh_rates parameter (timer_expired=%s)", timer_expired)
            # Get the current order
            order = request.website.sale_get_order()
            if order:
                try:
                    # Use the existing environment
                    env = request.env

                    # If timer expired, force update all jewelry prices regardless of price lock
                    if timer_expired:
                        _logger.info("Timer expired, forcing update of all jewelry prices")
                        # Force update all jewelry prices
                        for line in order.order_line:
                            if hasattr(line.product_id, 'is_jewelry') and line.product_id.is_jewelry:
                                # Calculate current price
                                try:
                                    current_price = line.product_id._calculate_jewelry_price()
                                    old_price = line.price_unit

                                    # Update the price
                                    if current_price != old_price:
                                        _logger.info("Updating price for %s: %s -> %s",
                                                    line.product_id.name, old_price, current_price)
                                        line.price_unit = current_price

                                        # Create metal rate snapshot
                                        order._create_metal_rate_snapshot(line)
                                except Exception as e:
                                    _logger.error("Error updating price for %s: %s",
                                                line.product_id.name, str(e))

                        # Recalculate order totals
                        if hasattr(order, '_compute_amount_all'):
                            order._compute_amount_all()
                        elif hasattr(order, '_amount_all'):
                            order._amount_all()

                    # Force refresh the price lock
                    price_lock = order.refresh_price_lock(force_refresh=True)
                    _logger.info("Force refreshed price lock due to refresh_rates parameter: %s",
                                price_lock.price_lock_expiry if price_lock else "None")

                    # Add a session flag to show a notification
                    request.session['rates_refreshed'] = True
                    if timer_expired:
                        request.session['timer_expired'] = True
                except Exception as e:
                    _logger.error("Error refreshing price lock: %s", str(e))

        # Call the standard cart method
        try:
            response = website_sale.cart(**post)
        except Exception as e:
            _logger.error("Error in standard cart method: %s", str(e))
            # Create a basic response
            from odoo.http import Response
            response = Response("""
                <html>
                <head><title>Cart</title></head>
                <body>
                    <h1>Cart Error</h1>
                    <p>There was an error loading your cart. Please try again later.</p>
                    <p><a href="/">Return to Home</a></p>
                </body>
                </html>
            """)
            return response

        # Add price lock information to the response
        if hasattr(response, 'qcontext'):
            try:
                # Use the existing environment
                env = request.env

                # Get the order
                order = env['website'].get_current_website().sale_get_order()
                if order:
                    # Check if we need to refresh the price lock
                    refresh_lock = False

                    # NEVER refresh the lock on page refresh (GET request)
                    # Only refresh the lock if cart contents have changed (POST request with specific parameters)
                    if (request.httprequest.method == 'POST' and
                        any(key in post for key in ['add_qty', 'set_qty', 'remove_id']) and
                        not (len(post) == 0 or (len(post) == 1 and 'express' in post))):
                        refresh_lock = True
                        _logger.info("Cart contents changed, will refresh price lock: %s", post)

                    # Refresh the price lock if needed
                    if refresh_lock:
                        _logger.info("Force refreshing price lock for order %s", order.id)
                        # Force refresh the price lock when cart contents change
                        price_lock = order.refresh_price_lock(force_refresh=True)
                    else:
                        # Just get the current price lock status
                        order._compute_price_lock()
                        order._compute_is_price_locked()
                        price_lock = order.price_lock_id

                        # If no price lock exists, create one
                        if not price_lock:
                            _logger.info("No active price lock found, creating one")
                            price_lock = env['jewelry.price.lock'].sudo().create_or_extend_lock(order.id)
                            if not price_lock:
                                _logger.warning("Failed to create price lock via create_or_extend_lock, creating a default one")
                                # Create a default price lock
                                lock_duration = 1  # 1 minute
                                expiry_time = datetime.datetime.now() + datetime.timedelta(minutes=lock_duration)

                                # Create a new lock using ORM instead of direct SQL
                                # This avoids transaction conflicts
                                price_lock_vals = {
                                    'order_id': order.id,
                                    'price_lock_expiry': expiry_time,
                                    'active': True,
                                }
                                price_lock = env['jewelry.price.lock'].sudo().create(price_lock_vals)
                                _logger.info("Created new default price lock for order %s", order.id)
                        else:
                            _logger.info("Using existing price lock: %s", price_lock.price_lock_expiry)

                    # Always add datetime to the context
                    response.qcontext['datetime'] = datetime

                    if price_lock:
                        response.qcontext['price_lock'] = price_lock
                        _logger.info("Added price lock to cart page: %s", price_lock.price_lock_expiry)

                # Check if we need to show a rates refreshed notification
                if request.session.get('rates_refreshed'):
                    response.qcontext['rates_refreshed'] = True
                    # Check if timer expired
                    if request.session.get('timer_expired'):
                        response.qcontext['timer_expired'] = True
                        # Clear the session flag
                        request.session.pop('timer_expired', None)
                    # Clear the session flag
                    request.session.pop('rates_refreshed', None)
            except Exception as e:
                _logger.error("Error adding price lock to cart page: %s", str(e))
                # Don't add price lock to the context if we can't create one
                # But still return the response

        return response

    @http.route('/shop/cart/refresh_rates', type='json', auth="public", website=True)
    def refresh_cart_rates(self, timer_expired=False):
        """Refresh metal rates and prices in the cart"""
        try:
            order = request.website.sale_get_order()
            if not order:
                return {'success': False, 'error': 'No cart found'}

            # Convert timer_expired to boolean if it's a string
            if isinstance(timer_expired, str):
                timer_expired = timer_expired.lower() in ('true', 't', 'yes', 'y', '1')

            _logger.info("Refreshing cart rates (timer_expired=%s)", timer_expired)

            # Use the existing environment
            env = request.env

            # Get current metal rates
            metals = env['jewelry.metal'].sudo().search([('is_current', '=', True)])
            metal_rates = []

            for metal in metals:
                metal_rates.append({
                    'name': metal.name,
                    'buy_price': metal.current_price,
                    'sell_price': metal.selling_price,
                    'currency': env['ir.qweb.field.monetary'].value_to_html(
                        1.0, {'display_currency': metal.currency_id}
                    )[0]
                })

            # If timer expired, force update all jewelry prices regardless of price lock
            if timer_expired:
                _logger.info("Timer expired, forcing update of all jewelry prices in refresh_cart_rates")
                # Force update all jewelry prices
                price_changes = []
                for line in order.order_line:
                    if hasattr(line.product_id, 'is_jewelry') and line.product_id.is_jewelry:
                        # Calculate current price
                        try:
                            current_price = line.product_id._calculate_jewelry_price()
                            old_price = line.price_unit

                            # Update the price
                            if current_price != old_price:
                                _logger.info("Updating price for %s: %s -> %s",
                                            line.product_id.name, old_price, current_price)
                                line.price_unit = current_price

                                # Add to price changes
                                price_changes.append({
                                    'product_id': line.product_id.id,
                                    'product_name': line.product_id.name,
                                    'old_price': old_price,
                                    'new_price': current_price,
                                    'percent_change': ((current_price - old_price) / old_price * 100) if old_price else 0
                                })

                                # Create metal rate snapshot
                                order._create_metal_rate_snapshot(line)
                        except Exception as e:
                            _logger.error("Error updating price for %s: %s",
                                        line.product_id.name, str(e))

                # Recalculate order totals
                if hasattr(order, '_compute_amount_all'):
                    order._compute_amount_all()
                elif hasattr(order, '_amount_all'):
                    order._amount_all()

            # Refresh the price lock with error handling
            try:
                # This will update jewelry prices if needed and create a new price lock if expired
                # If timer_expired is True, we've already updated the prices above
                price_lock = order.refresh_price_lock(force_refresh=timer_expired)
            except Exception as e:
                _logger.warning("Error refreshing price lock in refresh_cart_rates: %s. Using fallback method.", str(e))
                # Deactivate existing price locks using ORM
                existing_locks = env['jewelry.price.lock'].sudo().search([
                    ('order_id', '=', order.id),
                    ('active', '=', True)
                ])
                if existing_locks:
                    existing_locks.write({'active': False})

                # Create a new price lock
                lock_duration = int(env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                expiry_time = datetime.datetime.now() + datetime.timedelta(minutes=lock_duration)

                # Create a new lock using ORM
                price_lock_vals = {
                    'order_id': order.id,
                    'price_lock_expiry': expiry_time,
                    'active': True,
                }
                price_lock = env['jewelry.price.lock'].sudo().create(price_lock_vals)
                _logger.info("Created new price lock (fallback method) in refresh_cart_rates for order %s", order.id)

            # If we didn't set price_changes in the timer_expired block, initialize it
            if not timer_expired:
                price_changes = []

            # Recalculate cart expiry if the module is installed
            if hasattr(order, 'calculate_cart_expiry'):
                order.calculate_cart_expiry()

            # Get the current time for formatting
            now = datetime.datetime.now()

            # Format the expiry time for display
            if price_lock and price_lock.price_lock_expiry:
                time_diff = (price_lock.price_lock_expiry - now).total_seconds()
                hours = int(time_diff // 3600)
                minutes = int((time_diff % 3600) // 60)
                seconds = int(time_diff % 60)
                formatted_time = '{:02d}:{:02d}:{:02d}'.format(hours, minutes, seconds)
            else:
                # Default to 1 minute if no price lock
                formatted_time = '00:01:00'
                # Create a default price lock
                lock_duration = 1  # 1 minute
                expiry_time = now + datetime.timedelta(minutes=lock_duration)
                try:
                    # Deactivate existing price locks using ORM
                    existing_locks = env['jewelry.price.lock'].sudo().search([
                        ('order_id', '=', order.id),
                        ('active', '=', True)
                    ])
                    if existing_locks:
                        existing_locks.write({'active': False})

                    # Create a new lock using ORM
                    price_lock_vals = {
                        'order_id': order.id,
                        'price_lock_expiry': expiry_time,
                        'active': True,
                    }
                    price_lock = env['jewelry.price.lock'].sudo().create(price_lock_vals)
                    _logger.info("Created new default price lock in refresh_cart_rates for order %s", order.id)
                except Exception as e:
                    _logger.error("Failed to create default price lock in refresh_cart_rates: %s", str(e))
                    # Return a default response
                    return {
                        'success': True,
                        'price_changes': [],
                        'metal_rates': metal_rates,
                        'cart_total': order.amount_total,
                        'formatted_total': env['ir.qweb.field.monetary'].value_to_html(
                            order.amount_total, {'display_currency': order.currency_id}
                        ),
                        'price_lock_expiry': expiry_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'formatted_time': formatted_time,
                        'order_lines': []
                    }

            # Get order lines data
            order_lines = []
            for line in order.order_line:
                order_lines.append({
                    'id': line.id,
                    'product_id': line.product_id.id,
                    'product_name': line.product_id.name,
                    'price_unit': line.price_unit,
                    'price_subtotal': line.price_subtotal,
                    'quantity': line.product_uom_qty,
                    'currency_symbol': env.company.currency_id.symbol
                })

            return {
                'success': True,
                'price_changes': price_changes,
                'metal_rates': metal_rates,
                'cart_total': order.amount_total,
                'formatted_total': env['ir.qweb.field.monetary'].value_to_html(
                    order.amount_total, {'display_currency': order.currency_id}
                ),
                'price_lock_expiry': price_lock.price_lock_expiry.strftime('%Y-%m-%d %H:%M:%S'),
                'formatted_time': formatted_time,
                'order_lines': order_lines
            }

        except Exception as e:
            _logger.error("Error refreshing cart rates: %s", str(e))
            import traceback
            _logger.error("Traceback: %s", traceback.format_exc())
            return {'success': False, 'error': str(e)}

    @http.route('/shop/cart/refresh_price_lock', type='json', auth="public", website=True)
    def refresh_price_lock(self, force_refresh=False):
        """Refresh price lock without reloading the page

        Args:
            force_refresh: If True, always create a new price lock even if the current one is valid
        """
        try:
            # Convert force_refresh to boolean if it's a string
            if isinstance(force_refresh, str):
                force_refresh = force_refresh.lower() in ('true', 't', 'yes', 'y', '1')

            _logger.info("Refreshing price lock with force_refresh=%s", force_refresh)

            # Get the current order
            order = request.website.sale_get_order()
            if not order:
                return {'success': False, 'error': 'No order found'}

            # Use the existing environment
            env = request.env

            # Refresh the price lock
            # This will update jewelry prices if needed and create a new price lock if expired
            # or if force_refresh is True
            price_lock = order.refresh_price_lock(force_refresh=force_refresh)

            # Ensure we have a valid price lock
            now = datetime.datetime.now()

            if not price_lock:
                _logger.warning("No price lock returned from refresh_price_lock, creating a default one")
                # Create a default price lock
                try:
                    # Get the configured lock duration (default 120 minutes = 2 hours)
                    lock_duration = int(request.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                    expiry_time = now + datetime.timedelta(minutes=lock_duration)

                    # Deactivate existing price locks using ORM
                    existing_locks = env['jewelry.price.lock'].sudo().search([
                        ('order_id', '=', order.id),
                        ('active', '=', True)
                    ])
                    if existing_locks:
                        existing_locks.write({'active': False})

                    # Create a new lock using ORM
                    price_lock_vals = {
                        'order_id': order.id,
                        'price_lock_expiry': expiry_time,
                        'active': True,
                    }
                    price_lock = env['jewelry.price.lock'].sudo().create(price_lock_vals)
                    _logger.info("Created new default price lock in refresh_price_lock for order %s", order.id)
                except Exception as e:
                    _logger.error("Failed to create default price lock in refresh_price_lock: %s", str(e))
                    # Return a default response
                    return {
                        'success': True,
                        'order_total': order.amount_total,
                        'formatted_total': env['ir.qweb.field.monetary'].value_to_html(
                            order.amount_total, {'display_currency': order.currency_id}
                        ),
                        'price_lock_expiry': now.strftime('%Y-%m-%d %H:%M:%S'),
                        'formatted_time': '00:01:00',
                        'order_lines': [],
                        'metal_rates': [],
                        'price_changes': []
                    }

            # Ensure the expiry time is in the future
            expiry_time = price_lock.price_lock_expiry

            # If the expiry time is in the past or too close to now, use the configured duration
            if expiry_time <= now or (expiry_time - now).total_seconds() < 5:
                # Get the configured lock duration (default 120 minutes = 2 hours)
                lock_duration = int(request.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                _logger.info("Expiry time is in the past or too close, using configured duration: %s minutes", lock_duration)
                expiry_time = now + datetime.timedelta(minutes=lock_duration)
                price_lock.write({'price_lock_expiry': expiry_time})

            # Calculate time difference for display
            time_diff = (expiry_time - now).total_seconds()
            hours = int(time_diff // 3600)
            minutes = int((time_diff % 3600) // 60)
            seconds = int(time_diff % 60)
            formatted_time = '{:02d}:{:02d}:{:02d}'.format(hours, minutes, seconds)

            # Recalculate the order total after updating line prices
            # In Odoo 17, we need to use _compute_amount_all instead of _amount_all
            if hasattr(order, '_compute_amount_all'):
                order._compute_amount_all()
            elif hasattr(order, '_amount_all'):
                order._amount_all()
            else:
                # Fallback: manually trigger the compute methods
                order.invalidate_recordset(['amount_untaxed', 'amount_tax', 'amount_total'])

            # Get the order data to return
            order_total = order.amount_total
            formatted_total = env['ir.qweb.field.monetary'].value_to_html(
                order_total, {'display_currency': order.currency_id}
            )
            price_lock_expiry = expiry_time.strftime('%Y-%m-%d %H:%M:%S')
            _logger.info("Returning price lock with expiry: %s, formatted: %s", price_lock_expiry, formatted_time)

            # Get order lines data and track price changes
            order_lines = []
            price_changes = []

            # Only update jewelry prices if the price lock has expired
            # This ensures prices remain locked during the price lock period

            # Check if the price lock has expired
            if expiry_time <= now:
                _logger.info("Price lock has expired, updating jewelry prices")

                # Update jewelry prices only if the price lock has expired
                for line in order.order_line:
                    if hasattr(line.product_id, 'is_jewelry') and line.product_id.is_jewelry:
                        # Store old prices for comparison
                        old_price_unit = line.price_unit
                        old_price_subtotal = line.price_subtotal

                        # Update the price from current metal rates
                        if hasattr(line.product_id.product_tmpl_id, 'update_price_from_metals'):
                            line.product_id.product_tmpl_id.update_price_from_metals()
                            # Update the line price
                            line._compute_amount()

                            # Check if price changed
                            if old_price_unit != line.price_unit:
                                price_changes.append({
                                    'product_id': line.product_id.id,
                                    'product_name': line.product_id.name,
                                    'old_price': old_price_unit,
                                    'new_price': line.price_unit,
                                    'old_subtotal': old_price_subtotal,
                                    'new_subtotal': line.price_subtotal,
                                    'percent_change': ((line.price_unit - old_price_unit) / old_price_unit * 100) if old_price_unit else 0
                                })
            else:
                _logger.info("Price lock is still valid, not updating jewelry prices")

            # Now collect all order lines data
            for line in order.order_line:
                order_lines.append({
                    'id': line.id,
                    'product_id': line.product_id.id,
                    'product_name': line.product_id.name,
                    'price_unit': line.price_unit,
                    'price_subtotal': line.price_subtotal,
                    'quantity': line.product_uom_qty,
                    'currency_symbol': env.company.currency_id.symbol
                })

            # Get current metal rates
            metals = env['jewelry.metal'].sudo().search([('is_current', '=', True)])
            metal_rates = []

            for metal in metals:
                metal_rates.append({
                    'name': metal.name,
                    'buy_price': metal.current_price,
                    'sell_price': metal.selling_price,
                    'currency': env['ir.qweb.field.monetary'].value_to_html(
                        1.0, {'display_currency': metal.currency_id}
                    )[0]
                })

            # Return success with updated order info and formatted time
            return {
                'success': True,
                'order_total': order_total,
                'formatted_total': formatted_total,
                'price_lock_expiry': price_lock_expiry,
                'formatted_time': formatted_time,
                'order_lines': order_lines,
                'metal_rates': metal_rates,
                'price_changes': price_changes
            }

        except Exception as e:
            _logger.error("Error refreshing price lock: %s", str(e))
            return {'success': False, 'error': str(e)}

    @http.route('/shop/is_jewelry_product', type='json', auth="public", website=True)
    def is_jewelry_product(self, product_id=None):
        """Check if a product is a jewelry product"""
        if not product_id:
            return {'is_jewelry': False}

        try:
            # Convert product_id to integer
            product_id = int(product_id)

            # Get the product template
            product_template = request.env['product.template'].sudo().browse(product_id)
            if not product_template.exists():
                return {'is_jewelry': False}

            # Check if any variant has is_jewelry=True
            is_jewelry = False
            for variant in product_template.product_variant_ids:
                if hasattr(variant, 'is_jewelry') and variant.is_jewelry:
                    is_jewelry = True
                    break

            # Also check if the product requires realtime price computation
            requires_realtime = product_template.requires_realtime_price if hasattr(product_template, 'requires_realtime_price') else False

            return {
                'is_jewelry': is_jewelry,
                'requires_realtime_price': requires_realtime
            }

        except Exception as e:
            _logger.error("Error checking if product is jewelry: %s", str(e))
            return {'is_jewelry': False}

    @http.route('/shop/product/refresh_price', type='http', auth="public", website=True, methods=['POST'], csrf=False)
    def refresh_product_price(self, product_id=None, **kw):
        """Refresh product price without reloading the page"""
        if not product_id:
            response = request.make_response(json.dumps({'success': False, 'error': 'No product ID provided'}))
            response.headers['Content-Type'] = 'application/json'
            return response

        try:
            # Convert product_id to integer
            product_id = int(product_id)

            # Get the product
            product = request.env['product.template'].sudo().browse(product_id)
            if not product.exists():
                response = request.make_response(json.dumps({'success': False, 'error': 'Product not found'}))
                response.headers['Content-Type'] = 'application/json'
                return response

            _logger.info("Refreshing price for product: %s (ID: %s)", product.name, product_id)

            # Check if it's a jewelry product by checking variants
            is_jewelry = False
            for variant in product.product_variant_ids:
                if hasattr(variant, 'is_jewelry') and variant.is_jewelry:
                    is_jewelry = True
                    break

            # Also check if it requires realtime price computation
            requires_realtime = product.requires_realtime_price if hasattr(product, 'requires_realtime_price') else False

            if not (is_jewelry or requires_realtime):
                response = request.make_response(json.dumps({'success': False, 'error': 'Price refresh is only available for jewelry products or products requiring realtime price computation'}))
                response.headers['Content-Type'] = 'application/json'
                return response

            # Get the old price for comparison
            old_price = product.list_price

            # Update the product price based on current metal rates
            if hasattr(product, 'update_price_from_metals'):
                product.update_price_from_metals()

            # Get the new price
            new_price = product.list_price

            # Calculate price change percentage
            price_change = None
            if old_price != new_price:
                percent_change = ((new_price - old_price) / old_price) * 100 if old_price else 0
                price_change = {
                    'old_price': old_price,
                    'new_price': new_price,
                    'percent_change': percent_change
                }

            # Get price components if available
            price_components = None
            if hasattr(product, 'get_price_components'):
                price_components = product.get_price_components()

            # Format prices for display
            currency_id = product.currency_id or request.env.company.currency_id
            formatted_price = request.env['ir.qweb.field.monetary'].value_to_html(
                new_price, {'display_currency': currency_id}
            )

            formatted_list_price = None
            if product.list_price != product.price:
                formatted_list_price = request.env['ir.qweb.field.monetary'].value_to_html(
                    product.list_price, {'display_currency': currency_id}
                )

            # Return success with updated product info
            response_data = {
                'success': True,
                'product_id': product_id,
                'price': new_price,
                'formatted_price': formatted_price,
                'list_price': product.list_price,
                'formatted_list_price': formatted_list_price,
                'price_change': price_change,
                'price_components': price_components
            }

            response = request.make_response(json.dumps(response_data))
            response.headers['Content-Type'] = 'application/json'
            return response

        except Exception as e:
            _logger.error("Error refreshing product price: %s", str(e))
            import traceback
            _logger.error("Traceback: %s", traceback.format_exc())
            response = request.make_response(json.dumps({'success': False, 'error': str(e)}))
            response.headers['Content-Type'] = 'application/json'
            return response
