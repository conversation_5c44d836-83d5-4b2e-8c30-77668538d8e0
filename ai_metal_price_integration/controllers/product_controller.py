from odoo import http, _
from odoo.http import request
import logging

_logger = logging.getLogger(__name__)

class ProductController(http.Controller):
    @http.route(['/shop/product/<model("product.template"):product>'], type='http', auth="public", website=True)
    def product(self, product, category='', search='', **kwargs):
        """Simple product page that avoids template errors"""
        values = {
            'product': product,
            'product_variant': product.product_variant_id,
            'category': category,
            'search': search,
            'website': request.website,
            'currency_id': request.website.currency_id,
        }
        return request.render("ai_metal_price_integration.simple_product_page", values)
