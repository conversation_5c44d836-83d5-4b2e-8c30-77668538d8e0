<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Cron job to clean up expired price locks -->
        <record id="ir_cron_cleanup_expired_locks" model="ir.cron">
            <field name="name">Clean Up Expired Price Locks</field>
            <field name="model_id" ref="model_jewelry_price_lock"/>
            <field name="state">code</field>
            <field name="code">model._cron_cleanup_expired_locks()</field>
            <field name="interval_number">15</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>
