<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add jewelry indicator and price refresh elements to product page -->
    <template id="product_jewelry_indicator" inherit_id="website_sale.product" active="True">
        <xpath expr="//section[@id='product_detail']" position="attributes">
            <attribute name="t-att-data-product-id">product.id</attribute>
        </xpath>

        <xpath expr="//section[@id='product_detail']" position="inside">
            <!-- Add a hidden div that will be used by the JavaScript to identify jewelry products -->
            <div class="jewelry-indicator-container d-none"
                 t-att-data-product-id="product.id"></div>
        </xpath>

        <!-- Add CSS for price refresh -->
        <xpath expr="//section[@id='product_detail']" position="before">
            <style>
                .product-price-refresh-timer {
                    font-size: 0.9em;
                    color: #666;
                    margin-top: 5px;
                }

                .refresh-product-price {
                    cursor: pointer;
                    margin-left: 5px;
                    color: #007bff;
                }

                .refresh-product-price:hover {
                    color: #0056b3;
                }

                .oe_price.refreshing {
                    opacity: 0.7;
                }

                .oe_price.price-updated {
                    animation: highlight 2s;
                }

                @keyframes highlight {
                    0% { background-color: transparent; }
                    20% { background-color: rgba(255, 255, 0, 0.3); }
                    100% { background-color: transparent; }
                }
            </style>
        </xpath>
    </template>
</odoo>
