<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add metal rates widget to cart page -->
    <template id="cart_metal_rates" inherit_id="website_sale.cart">
        <xpath expr="//h3[contains(text(), 'Order overview')]" position="after">
            <t t-set="has_jewelry" t-value="website_sale_order and any(line.product_id.is_jewelry for line in website_sale_order.order_line)"/>
            <t t-if="has_jewelry">
                <div class="card mb-3 metal-rates-widget">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fa fa-line-chart me-2"></i> Current Metal Rates
                            <small class="text-muted ms-2">
                                (Last updated: <span class="last-refreshed-time">now</span>)
                            </small>
                        </h5>
                        <button class="btn btn-sm btn-outline-secondary refresh-metal-rates">
                            <i class="fa fa-refresh"></i> Refresh Rates
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row metal-rates-container">
                            <t t-foreach="request.env['jewelry.metal'].sudo().search([('is_current', '=', True)])" t-as="metal">
                                <div class="col-md-4 mb-3 metal-rate-item" t-att-data-metal="metal.name">
                                    <div class="d-flex align-items-center">
                                        <t t-if="metal.image">
                                            <img t-att-src="'data:image/png;base64,%s' % metal.image" class="me-2" style="width: 30px; height: 30px;"/>
                                        </t>
                                        <div>
                                            <div class="fw-bold"><t t-esc="metal.name"/></div>
                                            <div>
                                                <span class="text-muted">Buy:</span>
                                                <span class="buy-price" t-esc="metal.current_price" t-options="{'widget': 'monetary', 'display_currency': metal.currency_id}"/> / g
                                            </div>
                                            <div>
                                                <span class="text-muted">Sell:</span>
                                                <span class="sell-price" t-esc="metal.selling_price" t-options="{'widget': 'monetary', 'display_currency': metal.currency_id}"/> / g
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                        <div class="text-muted small mt-2">
                            <i class="fa fa-info-circle"></i> These rates are used to calculate the prices of jewelry items in your cart.
                            <t t-if="price_lock">
                                <span class="ms-2">Prices are locked for the duration shown in the timer above.</span>
                            </t>
                            <t t-else="">
                                <span class="ms-2">Rates update when market prices change.</span>
                            </t>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>
    </template>
</odoo>
