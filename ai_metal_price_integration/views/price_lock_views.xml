<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Price Lock views -->
    <record id="view_jewelry_price_lock_list" model="ir.ui.view">
        <field name="name">jewelry.price.lock.list</field>
        <field name="model">jewelry.price.lock</field>
        <field name="arch" type="xml">
            <tree string="Price Locks">
                <field name="order_id"/>
                <field name="price_lock_expiry"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_jewelry_price_lock_form" model="ir.ui.view">
        <field name="name">jewelry.price.lock.form</field>
        <field name="model">jewelry.price.lock</field>
        <field name="arch" type="xml">
            <form string="Price Lock">
                <sheet>
                    <group>
                        <group>
                            <field name="order_id"/>
                            <field name="price_lock_expiry"/>
                        </group>
                        <group>
                            <field name="active"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_jewelry_price_locks" model="ir.actions.act_window">
        <field name="name">Price Locks</field>
        <field name="res_model">jewelry.price.lock</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem id="menu_jewelry_price_locks"
              name="Price Locks"
              parent="sale.menu_sale_config"
              action="action_jewelry_price_locks"
              sequence="20"/>
</odoo>
