<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add price lock information to cart page -->
    <template id="cart_lines_price_lock" inherit_id="website_sale.cart">
        <xpath expr="//h3[contains(text(), 'Order overview')]" position="before">
            <t t-if="website_sale_order">
                <!-- Use the price_lock from the context if available, otherwise try to get it directly -->
                <t t-set="price_lock" t-value="price_lock if price_lock else request.env['jewelry.price.lock'].sudo().get_active_lock(website_sale_order.id)" />

                <!-- Show notification when rates are refreshed -->
                <t t-if="rates_refreshed">
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <t t-if="timer_expired">
                            <strong>Price Lock Expired!</strong> Metal rates have been refreshed and prices have been updated to current market rates.
                        </t>
                        <t t-else="">
                            <strong>Prices Updated!</strong> Metal rates have been refreshed and prices have been updated.
                        </t>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </t>

                <!-- Show price lock timer -->
                <div class="alert alert-info price-lock-timer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fa fa-lock me-2"></i>
                            <strong>Price Lock Active:</strong>
                            <span class="ms-2">Your prices are protected from market fluctuations</span>
                        </div>
                        <div>
                            <span class="me-2">Time remaining:</span>
                            <t t-set="default_expiry" t-value="(datetime.datetime.now() + datetime.timedelta(minutes=1)).isoformat() if datetime else ''"/>
                            <strong id="price-lock-timer-value" t-att-data-expiry="default_expiry" class="badge bg-primary">
                                <i class="fa fa-clock-o me-1"></i>
                                00:01:00
                            </strong>
                        </div>
                    </div>
                    <div class="small text-muted mt-2">
                        <i class="fa fa-info-circle me-1"></i>
                        Prices will be updated automatically when the timer expires. Complete your purchase before the timer expires to lock in current rates.
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <!-- Price volatility notice has been removed to fix 500 error -->

    <!-- Price components display is handled by the ai_product_price_breakup module -->

    <!-- Price lock display on checkout and payment pages is currently disabled
         to avoid conflicts with other modules. This will be re-enabled in a future update. -->

    <!-- Cart animation fix - simplified to avoid conflicts -->
    <template id="cart_animation_fix" inherit_id="website.layout">
        <xpath expr="//head" position="inside">
            <script type="text/javascript">
                // Simple helper to ensure cart notifications work properly
                document.addEventListener('DOMContentLoaded', function() {
                    // Add a global error handler for cart operations
                    window.addEventListener('error', function(event) {
                        // Check if the error is related to cart operations
                        if (event.message &amp;&amp; (
                            event.message.includes('cart') ||
                            event.message.includes('notification') ||
                            event.message.includes('undefined')
                        )) {
                            console.log('Caught error in cart operation:', event.message);
                            // Prevent the error from breaking the page
                            event.preventDefault();
                        }
                    });
                });
            </script>
        </xpath>
    </template>

    <!-- Add price lock timer script -->
    <template id="price_lock_timer_script" inherit_id="website.layout">
        <xpath expr="//head" position="inside">
            <script type="text/javascript" src="/ai_metal_price_integration/static/src/js/timer_script.js"></script>
        </xpath>
    </template>

</odoo>
