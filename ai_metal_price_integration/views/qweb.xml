<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="website_sale_stock_product_availability" name="Product Availability" inherit_id="website_sale.product" active="False">
        <xpath expr="//div[@id='product_details']" position="inside">
            <div class="availability_messages">
                <t t-if="product_type == 'product'">
                    <div t-if="not free_qty and not product_template.allow_out_of_stock_order" class="text-danger">
                        <i class="fa fa-times-circle"></i> Temporarily out of stock
                    </div>
                    <div t-elif="free_qty &lt;= 0 and product_template.allow_out_of_stock_order" class="text-warning">
                        <i class="fa fa-clock-o"></i> Out of stock, available for order
                    </div>
                    <div t-elif="free_qty &lt;= product_template.show_availability_threshold" class="text-warning">
                        <i class="fa fa-clock-o"></i> <t t-esc="free_qty"/> units available
                    </div>
                    <div t-else="" class="text-success">
                        <i class="fa fa-check-circle"></i> In stock
                    </div>
                </t>
            </div>
        </xpath>
    </template>
</odoo>
