<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="simple_product_page" name="Simple Product Page">
        <t t-call="website.layout">
            <div id="wrap" class="js_sale">
                <div class="container oe_website_sale">
                    <div class="row mt-4">
                        <div class="col-lg-6 col-xl-7">
                            <!-- Product image -->
                            <div class="product-image">
                                <img t-att-src="'/web/image/product.template/%s/image_1024' % product.id" class="img img-fluid" alt="Product Image"/>
                            </div>
                        </div>
                        <div class="col-lg-6 col-xl-5">
                            <!-- Product details -->
                            <h1 t-field="product.name" class="mb-3"/>
                            
                            <!-- Product price -->
                            <div class="product_price mt-3">
                                <h4 class="oe_price_h4">
                                    <span t-field="product.list_price" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                </h4>
                            </div>
                            
                            <!-- Add to cart form -->
                            <form action="/shop/cart/update" method="POST" class="js_add_cart_variants mt-4">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <div class="js_product">
                                    <input type="hidden" class="product_id" name="product_id" t-att-value="product.product_variant_id.id"/>
                                    <input type="hidden" class="product_template_id" name="product_template_id" t-att-value="product.id"/>
                                    
                                    <!-- Quantity selector -->
                                    <div class="css_quantity input-group">
                                        <div class="input-group-prepend">
                                            <button type="button" class="btn btn-secondary js_add_cart_json d-none d-md-inline-block" aria-label="Remove one" title="Remove one">
                                                <i class="fa fa-minus"></i>
                                            </button>
                                        </div>
                                        <input type="text" class="js_quantity form-control quantity" name="add_qty" value="1"/>
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-secondary js_add_cart_json d-none d-md-inline-block" aria-label="Add one" title="Add one">
                                                <i class="fa fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Add to cart button -->
                                    <div class="mt-3">
                                        <button type="submit" class="btn btn-primary btn-lg mt-2 js_check_product a-submit">
                                            <i class="fa fa-shopping-cart"></i> Add to Cart
                                        </button>
                                    </div>
                                </div>
                            </form>
                            
                            <!-- Product description -->
                            <div class="mt-4">
                                <h4>Description</h4>
                                <div t-field="product.description_sale" class="text-muted"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
