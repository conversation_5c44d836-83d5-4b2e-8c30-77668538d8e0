<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add timer script to the head -->
    <template id="frontend_layout_override" inherit_id="web.frontend_layout">
        <xpath expr="//head" position="inside">
            <!-- Timer script is now included via assets in the manifest -->
            <style>
                .price-lock-container {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 0.25rem;
                    padding: 1rem;
                    margin-bottom: 1rem;
                }
                .price-lock-info {
                    display: flex;
                    align-items: center;
                }
                .price-lock-icon {
                    font-size: 1.5rem;
                    color: #28a745;
                    margin-right: 1rem;
                }
                .price-lock-timer {
                    display: flex;
                    align-items: center;
                    font-size: 1.2rem;
                    font-weight: bold;
                }
                .price-lock-timer i {
                    margin-right: 0.5rem;
                    color: #dc3545;
                }
                #price-lock-timer-value {
                    font-family: monospace;
                    background-color: #f1f1f1;
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.25rem;
                }
                .price-updated {
                    animation: highlight 2s;
                }
                @keyframes highlight {
                    0% { background-color: #ffff99; }
                    100% { background-color: transparent; }
                }
            </style>
        </xpath>
    </template>
</odoo>
