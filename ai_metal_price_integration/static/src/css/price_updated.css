/* Price updated highlight effect */
.price-updated {
    animation: highlight-price 2s;
}

@keyframes highlight-price {
    0% { background-color: transparent; }
    20% { background-color: rgba(255, 255, 0, 0.3); }
    100% { background-color: transparent; }
}

/* Refreshing animation */
.refreshing {
    opacity: 0.7;
    transition: opacity 0.3s;
}

/* Make sure the price refresh timer is visible */
.product-price-refresh-timer {
    display: block !important;
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.product-price-refresh-timer .fa-refresh {
    cursor: pointer;
    margin-left: 5px;
}

.product-price-refresh-timer .fa-refresh:hover {
    color: #007bff;
}

.product-price-refresh-timer .fa-spin {
    color: #007bff;
}
