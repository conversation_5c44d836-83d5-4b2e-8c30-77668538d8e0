/* Price lock container */
.price-lock-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    border: 1px solid #28a745;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Price lock info */
.price-lock-info {
    display: flex;
    align-items: center;
}

.price-lock-icon {
    font-size: 1.75rem;
    color: #28a745;
    margin-right: 1rem;
}

/* Price lock timer */
.price-lock-timer {
    font-weight: bold;
    font-size: 1.25rem;
    padding: 0.5rem 1rem;
    background-color: #f1f8f1;
    border-radius: 0.25rem;
    border: 1px solid #d4edda;
}

/* Timer colors */
.price-lock-timer .text-success {
    color: #28a745 !important;
}

.price-lock-timer .text-warning {
    color: #ffc107 !important;
}

.price-lock-timer .text-danger {
    color: #dc3545 !important;
}

/* Price updated highlight effect */
.price-updated {
    animation: highlight-price 2s;
}

@keyframes highlight-price {
    0% { background-color: transparent; }
    20% { background-color: rgba(255, 255, 0, 0.3); }
    100% { background-color: transparent; }
}
