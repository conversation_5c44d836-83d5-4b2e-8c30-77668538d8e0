<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <!-- These templates are required by OWL components -->
    
    <!-- OverlayContainer template -->
    <t t-name="web.OverlayContainer">
        <div class="o_overlay_container">
            <t t-slot="default"/>
        </div>
    </t>
    
    <!-- UploadProgressToast template -->
    <t t-name="web_editor.UploadProgressToast">
        <div class="o_upload_progress_toast">
            <div class="o_upload_progress_toast_header">
                <span class="o_upload_progress_toast_title">Uploading...</span>
                <button class="o_upload_progress_toast_close" t-on-click="close">×</button>
            </div>
            <div class="o_upload_progress_toast_body">
                <div class="o_upload_progress_toast_progress_bar_container">
                    <div class="o_upload_progress_toast_progress_bar" t-att-style="'width: ' + progress + '%'"/>
                </div>
                <div class="o_upload_progress_toast_progress_text">
                    <t t-esc="progress"/>%
                </div>
            </div>
        </div>
    </t>
    
    <!-- Product availability template -->
    <t t-name="website_sale_stock.product_availability">
        <div t-if="product_type == 'product'" class="availability_message_div">
            <div t-if="!free_qty and !virtual_available and !allow_out_of_stock_order" class="text-danger">
                <i class="fa fa-times-circle"></i> Temporarily out of stock
            </div>
            <div t-elif="!free_qty and !virtual_available and allow_out_of_stock_order" class="text-warning">
                <i class="fa fa-clock-o"></i> Out of stock, available for order
            </div>
            <div t-elif="free_qty &lt;= 0 and virtual_available &gt; 0" class="text-warning">
                <i class="fa fa-clock-o"></i> Available in <t t-esc="virtual_available_formatted"/>
            </div>
            <div t-elif="free_qty &gt; 0" class="text-success">
                <i class="fa fa-check"></i> In stock
            </div>
        </div>
    </t>
</templates>
