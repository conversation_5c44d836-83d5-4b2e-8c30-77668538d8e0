<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <!-- Template for product availability -->
    <t t-name="website_sale_stock.product_availability">
        <div class="availability_messages">
            <t t-if="product_type == 'product'">
                <div t-if="!free_qty and !product_template.allow_out_of_stock_order" class="text-danger">
                    <i class="fa fa-times-circle"></i> Temporarily out of stock
                </div>
                <div t-elif="free_qty &lt;= 0 and product_template.allow_out_of_stock_order" class="text-warning">
                    <i class="fa fa-clock-o"></i> Out of stock, available for order
                </div>
                <div t-elif="free_qty &lt;= product_template.show_availability_threshold" class="text-warning">
                    <i class="fa fa-clock-o"></i> <t t-esc="free_qty"/> units available
                </div>
                <div t-else="" class="text-success">
                    <i class="fa fa-check-circle"></i> In stock
                </div>
            </t>
        </div>
    </t>

    <!-- Template for overlay container -->
    <t t-name="web.OverlayContainer">
        <div class="o_overlay_container">
            <t t-slot="default"/>
        </div>
    </t>

    <!-- Template for upload progress toast -->
    <t t-name="web_editor.UploadProgressToast">
        <div class="o_upload_progress_toast">
            <div class="o_upload_progress_toast_header">
                <span class="o_upload_progress_toast_title">Uploading...</span>
                <button class="o_upload_progress_toast_close" t-on-click="close">×</button>
            </div>
            <div class="o_upload_progress_toast_body">
                <div class="o_upload_progress_toast_progress_bar_container">
                    <div class="o_upload_progress_toast_progress_bar" t-att-style="'width: ' + progress + '%'"/>
                </div>
                <div class="o_upload_progress_toast_progress_text">
                    <t t-esc="progress"/>%
                </div>
            </div>
        </div>
    </t>
</templates>
