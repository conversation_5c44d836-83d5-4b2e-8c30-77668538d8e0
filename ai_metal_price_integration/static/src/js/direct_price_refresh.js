// Direct Price Refresh
// This script directly refreshes product prices without relying on complex widget systems

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    console.log("Direct Price Refresh loaded");

    // Get the product ID from the URL or form
    function getProductId() {
        // Try to get from the form first
        const productForm = document.querySelector('form[action="/shop/cart/update"]');
        if (productForm) {
            const productInput = productForm.querySelector('input[name="product_id"]');
            if (productInput) {
                return parseInt(productInput.value, 10);
            }
        }

        // Try to get from the URL
        const url = window.location.href;
        const match = url.match(/\/shop\/([^\/]+)-(\d+)/);
        if (match && match[2]) {
            return parseInt(match[2], 10);
        }

        // Try to get from data attribute
        const productElement = document.querySelector('[data-product-id]');
        if (productElement) {
            return parseInt(productElement.getAttribute('data-product-id'), 10);
        }

        return null;
    }

    // Check if this is a product page
    function isProductPage() {
        return document.querySelector('form[action="/shop/cart/update"]') !== null;
    }

    // Refresh the product price
    function refreshProductPrice() {
        const productId = getProductId();
        if (!productId) {
            console.log("No product ID found, skipping price refresh");
            return;
        }

        console.log("Refreshing price for product ID:", productId);

        // Add refreshing class to price elements
        const priceElements = document.querySelectorAll('.oe_price, .oe_currency_value');
        priceElements.forEach(el => el.classList.add('refreshing'));

        // Make the AJAX call
        fetch('/shop/product/refresh_price', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'product_id=' + productId
        })
        .then(response => response.json())
        .then(data => {
            console.log("Price refresh response:", data);

            if (data.success) {
                // Update the main price
                const mainPriceElements = document.querySelectorAll('.oe_price .oe_currency_value');
                mainPriceElements.forEach(el => {
                    el.innerHTML = data.formatted_price;
                    el.closest('.oe_price').classList.add('price-updated');
                    setTimeout(() => {
                        el.closest('.oe_price').classList.remove('price-updated');
                    }, 2000);
                });

                // Update any other price elements
                const otherPriceElements = document.querySelectorAll('.product_price .oe_currency_value:not(.oe_price .oe_currency_value)');
                otherPriceElements.forEach(el => {
                    el.innerHTML = data.formatted_price;
                    el.classList.add('price-updated');
                    setTimeout(() => {
                        el.classList.remove('price-updated');
                    }, 2000);
                });

                // Update price breakup if available
                if (data.price_components) {
                    // Try to use the global updatePriceBreakup function if available
                    if (typeof updatePriceBreakup === 'function') {
                        try {
                            updatePriceBreakup(data.price_components);
                            console.log("Used global updatePriceBreakup function");
                        } catch (e) {
                            console.error("Error using global updatePriceBreakup function:", e);
                            // Fall back to manual update
                            updatePriceBreakupManually(data.price_components);
                        }
                    } else {
                        // Fall back to manual update
                        updatePriceBreakupManually(data.price_components);
                    }
                }

                // Try to update Angular scope if available
                updateAngularPriceDisplay(data);
            } else {
                console.error("Failed to refresh price:", data.error);
            }

            // Remove refreshing class
            priceElements.forEach(el => el.classList.remove('refreshing'));
        })
        .catch(error => {
            console.error("Error refreshing price:", error);
            // Remove refreshing class
            priceElements.forEach(el => el.classList.remove('refreshing'));
        });
    }

    // Function to manually update price breakup
    function updatePriceBreakupManually(components) {
        console.log("Attempting to update price breakup manually");

        // Try to find the price breakup container
        const breakupContainer = document.querySelector('.price-breakup-container, .price-components, #priceBreakupModal .modal-body');
        if (!breakupContainer) {
            console.log("Price breakup container not found");
            return;
        }

        console.log("Found price breakup container:", breakupContainer);

        // Find all component rows
        const componentRows = breakupContainer.querySelectorAll('tr, .component-row');
        if (componentRows.length === 0) {
            console.log("No component rows found in breakup container");
            return;
        }

        console.log("Found", componentRows.length, "component rows");

        // Try to match components by name
        for (const component of components) {
            for (const row of componentRows) {
                const nameCell = row.querySelector('td:first-child, .component-name');
                const valueCell = row.querySelector('td:last-child, .component-value');

                if (nameCell && valueCell) {
                    const name = nameCell.textContent.trim().toLowerCase();
                    if (name.includes(component.name.toLowerCase())) {
                        console.log("Updating component", component.name, "from", valueCell.textContent, "to", component.value);
                        valueCell.textContent = component.value.toFixed(2);
                        valueCell.classList.add('price-updated');
                        setTimeout(() => {
                            valueCell.classList.remove('price-updated');
                        }, 2000);
                    }
                }
            }
        }
    }

    // Function to update Angular-based price display
    function updateAngularPriceDisplay(data) {
        console.log("Attempting to update Angular-based price display");

        // Check if Angular is available
        if (typeof angular === 'undefined') {
            console.log("Angular not found");
            return;
        }

        try {
            // Try to find the Angular scope
            const element = document.querySelector('[ng-controller]');
            if (!element) {
                console.log("No Angular controller found");
                return;
            }

            const scope = angular.element(element).scope();
            if (!scope) {
                console.log("No Angular scope found");
                return;
            }

            console.log("Found Angular scope:", scope);

            // Update the price in the scope
            if (data.price) {
                scope.price = data.price;
            }

            // Update price components if available
            if (data.price_components) {
                scope.priceComponents = data.price_components;
            }

            // Apply the changes
            scope.$apply();
            console.log("Angular scope updated");
        } catch (e) {
            console.error("Error updating Angular price display:", e);
        }
    }

    // Only run on product pages
    if (isProductPage()) {
        // Initial refresh
        setTimeout(refreshProductPrice, 1000);

        // Set up periodic refresh - more frequent updates
        setInterval(refreshProductPrice, 15000); // Every 15 seconds
    }
});
