// Enhanced Price Lock Timer with auto-refresh and page reload
console.log("Price Lock Timer v5.0 loaded");

// Global variables
window.PriceLockTimer = {
    timerElement: null,
    expiryTime: null,
    timerInterval: null,
    lastRefreshTime: 0,
    lastStorageUpdate: 0,
    refreshing: false,

    // Initialize the timer
    init: function() {
        console.log("Initializing Price Lock Timer");

        // Check if we're reloading due to timer expiration
        if (localStorage.getItem('priceTimerExpired') === 'true') {
            console.log("Page reloaded due to timer expiration, refreshing rates");
            localStorage.removeItem('priceTimerExpired');

            // Force refresh the rates via AJAX
            this.forceRefreshRates();
        }

        // Find the timer element
        this.timerElement = document.getElementById('price-lock-timer-value');
        if (!this.timerElement) {
            console.log("Price lock timer element not found");
            return;
        }

        // Get the expiry time from localStorage first (for page refreshes)
        var storedExpiryTime = localStorage.getItem('priceLockExpiryTime');
        var expiryTimeStr;

        if (storedExpiryTime) {
            console.log("Found stored expiry time:", storedExpiryTime);
            expiryTimeStr = storedExpiryTime;
        } else {
            // If not in localStorage, get it from the data attribute
            expiryTimeStr = this.timerElement.getAttribute('data-expiry');
            console.log("Using expiry time from data attribute:", expiryTimeStr);
        }

        if (!expiryTimeStr) {
            console.log("No expiry time found");
            return;
        }

        // Parse the expiry time
        this.expiryTime = new Date(expiryTimeStr);
        console.log("Initial expiry time:", this.expiryTime);

        // Check if the expiry time is valid
        var now = new Date();
        if (this.expiryTime <= now) {
            console.log("Expiry time is in the past, setting default expiry time");
            // Set a default expiry time (1 minute from now)
            this.expiryTime = new Date(now.getTime() + 60 * 1000);

            // Update the data attribute and localStorage
            this.timerElement.setAttribute('data-expiry', this.expiryTime.toISOString());
            localStorage.setItem('priceLockExpiryTime', this.expiryTime.toISOString());
        } else {
            // Store the valid expiry time in localStorage
            localStorage.setItem('priceLockExpiryTime', this.expiryTime.toISOString());
        }

        // Start the timer
        this.startTimer();

        // Listen for cart updates
        this.setupCartUpdateListeners();
    },

    // Force refresh the rates via AJAX
    forceRefreshRates: function() {
        console.log("Force refreshing rates");

        // Make an AJAX call to refresh the rates
        fetch('/shop/cart/refresh_rates', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                timer_expired: true
            })
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            console.log("Rates refreshed:", data);

            // If there are price changes, show a notification
            if (data.price_changes && data.price_changes.length > 0) {
                console.log("Prices have changed:", data.price_changes);

                // Show a notification
                var notification = document.createElement('div');
                notification.className = 'alert alert-warning alert-dismissible fade show';
                notification.innerHTML = '<strong>Prices Updated!</strong> Metal rates have changed and prices have been updated.';

                // Add a close button
                var closeButton = document.createElement('button');
                closeButton.type = 'button';
                closeButton.className = 'btn-close';
                closeButton.setAttribute('data-bs-dismiss', 'alert');
                closeButton.setAttribute('aria-label', 'Close');
                notification.appendChild(closeButton);

                // Add the notification to the page
                var container = document.querySelector('.oe_website_sale');
                if (container) {
                    container.insertBefore(notification, container.firstChild);
                }
            }

            // Reload the page to show the updated prices
            window.location.reload();
        })
        .catch(function(error) {
            console.error("Error refreshing rates:", error);

            // Reload the page anyway
            window.location.reload();
        });
    },

    // Set up listeners for cart updates
    setupCartUpdateListeners: function() {
        // Listen for form submissions that update the cart
        var cartForms = document.querySelectorAll('form[action*="/shop/cart/update"]');
        for (var i = 0; i < cartForms.length; i++) {
            cartForms[i].addEventListener('submit', function() {
                console.log("Cart form submitted, will refresh timer");
                // Set a flag to refresh the timer after the page reloads
                localStorage.setItem('refreshPriceLock', 'true');
            });
        }

        // Listen for AJAX cart updates
        document.addEventListener('click', function(event) {
            var target = event.target;
            // Check if the clicked element is a cart update button
            if (target.matches('.js_add_cart_json, .js_update_cart_json, a[href*="/shop/cart/update"]')) {
                console.log("Cart update button clicked, will refresh timer");
                // Set a flag to refresh the timer after the AJAX request completes
                localStorage.setItem('refreshPriceLock', 'true');
            }
        });

        // Check if we need to refresh the timer (after page load)
        if (localStorage.getItem('refreshPriceLock') === 'true') {
            console.log("Found refresh flag, refreshing price lock");
            localStorage.removeItem('refreshPriceLock');
            this.refreshPriceLock();
        }
    },

    // Start the timer
    startTimer: function() {
        // Clear any existing interval
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }

        // Update immediately
        this.updateTimer();

        // Set interval to update every second
        this.timerInterval = setInterval(function() {
            PriceLockTimer.updateTimer();
        }, 1000);
    },

    // Update the timer display
    updateTimer: function() {
        if (!this.timerElement || !this.expiryTime) {
            return;
        }

        // If we're already refreshing, don't update the timer
        if (this.refreshing) {
            return;
        }

        // Calculate the time difference
        var now = new Date();
        var timeDiff = Math.max(0, Math.floor((this.expiryTime - now) / 1000));

        // If the timer has expired
        if (timeDiff <= 0) {
            // Only refresh if we haven't refreshed recently (within the last 5 seconds)
            var currentTime = now.getTime();
            if (currentTime - this.lastRefreshTime > 5000) {
                this.refreshing = true;
                this.timerElement.textContent = 'Updating...';

                console.log("Timer expired, refreshing price lock and reloading page");
                this.lastRefreshTime = currentTime;

                // Set a flag to indicate we're reloading due to timer expiration
                localStorage.setItem('priceTimerExpired', 'true');

                // Clear the stored expiry time since it's expired
                localStorage.removeItem('priceLockExpiryTime');

                // Reload the page with a parameter to force price updates
                window.location.href = '/shop/cart?refresh_rates=1&timer_expired=1&t=' + new Date().getTime();
                return;
            } else {
                console.log("Skipping refresh - too soon since last refresh");
                // Set a new expiry time 30 seconds from now to avoid getting stuck
                this.expiryTime = new Date(now.getTime() + 30 * 1000);

                // Update localStorage with the new expiry time
                localStorage.setItem('priceLockExpiryTime', this.expiryTime.toISOString());
            }
            return;
        }

        // Update localStorage with the current expiry time (every 10 seconds to avoid excessive writes)
        if (Math.floor(now.getTime() / 10000) !== Math.floor(this.lastStorageUpdate / 10000)) {
            localStorage.setItem('priceLockExpiryTime', this.expiryTime.toISOString());
            this.lastStorageUpdate = now.getTime();
        }

        // Calculate hours, minutes, seconds
        var hours = Math.floor(timeDiff / 3600);
        var minutes = Math.floor((timeDiff % 3600) / 60);
        var seconds = timeDiff % 60;

        // Format time components
        var timeString =
            (hours < 10 ? '0' + hours : hours) + ':' +
            (minutes < 10 ? '0' + minutes : minutes) + ':' +
            (seconds < 10 ? '0' + seconds : seconds);

        // Update timer display
        this.timerElement.textContent = timeString;
    },

    // Refresh the price lock via AJAX
    refreshPriceLock: function(forceRefresh) {
        var self = this;

        // Make an AJAX call to refresh the price lock
        fetch('/shop/cart/refresh_price_lock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                force_refresh: forceRefresh === true
            })
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            console.log("Price lock refreshed:", data);

            if (data.success) {
                // Update the timer with the new expiry time
                if (data.price_lock_expiry) {
                    self.expiryTime = new Date(data.price_lock_expiry);
                    console.log("New expiry time:", self.expiryTime);

                    // Update the data attribute and localStorage
                    if (self.timerElement) {
                        self.timerElement.setAttribute('data-expiry', self.expiryTime.toISOString());
                        localStorage.setItem('priceLockExpiryTime', self.expiryTime.toISOString());
                    }
                }

                // If there are price changes, show a notification
                if (data.price_changes && data.price_changes.length > 0) {
                    console.log("Prices have changed:", data.price_changes);

                    // Show a notification
                    var notification = document.createElement('div');
                    notification.className = 'alert alert-warning alert-dismissible fade show';
                    notification.innerHTML = '<strong>Prices Updated!</strong> Metal rates have changed and prices have been updated.';

                    // Add a close button
                    var closeButton = document.createElement('button');
                    closeButton.type = 'button';
                    closeButton.className = 'btn-close';
                    closeButton.setAttribute('data-bs-dismiss', 'alert');
                    closeButton.setAttribute('aria-label', 'Close');
                    notification.appendChild(closeButton);

                    // Add the notification to the page
                    var container = document.querySelector('.oe_website_sale');
                    if (container) {
                        container.insertBefore(notification, container.firstChild);
                    }
                }

                // Reset the refreshing flag
                self.refreshing = false;

                // Update the timer immediately
                self.updateTimer();
            } else {
                console.error("Failed to refresh price lock:", data.error || "Unknown error");

                // Set a default expiry time (1 minute from now)
                self.expiryTime = new Date(new Date().getTime() + 60 * 1000);

                // Update localStorage with the new expiry time
                localStorage.setItem('priceLockExpiryTime', self.expiryTime.toISOString());

                self.refreshing = false;
                self.updateTimer();
            }
        })
        .catch(function(error) {
            console.error("Error refreshing price lock:", error);

            // Set a default expiry time (1 minute from now)
            self.expiryTime = new Date(new Date().getTime() + 60 * 1000);

            // Update localStorage with the new expiry time
            localStorage.setItem('priceLockExpiryTime', self.expiryTime.toISOString());

            self.refreshing = false;
            self.updateTimer();
        });
    }
};

// Initialize the timer when the DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        PriceLockTimer.init();
    });
} else {
    PriceLockTimer.init();
}
