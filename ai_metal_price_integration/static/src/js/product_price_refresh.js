// Product Price Auto-Refresh
// This script automatically refreshes product prices on the product description page
// without reloading the page for products that require real-time computation
//
// Version 3.0 - Consolidated from multiple scripts and optimized for Odoo 17

// Use vanilla JavaScript to avoid dependency issues
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    console.log("Product Price Refresh v3.0 loaded");

    // Simple translation function
    function _t(text) {
        return text; // In a real implementation, this would translate the text
    }

    // ProductPriceRefresh class
    class ProductPriceRefresh {
        constructor() {
            this.refreshInterval = null;
            this.countdownInterval = null;
            this.productId = this.getProductId();
            console.log("Product ID detected:", this.productId);

            this.isJewelry = this.isJewelryProduct();
            console.log("Is jewelry product:", this.isJewelry);

            this.lastRefreshTime = 0;
            this.refreshing = false;

            // Set up event listeners
            this.setupEventListeners();

            // Force refresh on load to ensure we have the latest price
            if (this.productId) {
                console.log("Performing initial price refresh");
                setTimeout(() => {
                    this.refreshProductPrice(false);
                }, 1000);
            }

            // Initialize refresh timer for jewelry products
            if (this.productId) {
                console.log("Setting up auto-refresh for product:", this.productId);
                this.initRefreshTimer();

                // Set up auto-refresh every 30 seconds
                const self = this;
                this.refreshInterval = setInterval(function() {
                    console.log("Auto-refreshing price");
                    self.refreshProductPrice(false);
                }, 30 * 1000); // 30 seconds
            }
        }

        // Set up event listeners
        setupEventListeners() {
            const self = this;

            // Listen for clicks on the refresh button
            document.addEventListener('click', function(event) {
                if (event.target.matches('.refresh-product-price') ||
                    event.target.closest('.refresh-product-price')) {
                    event.preventDefault();
                    self.refreshProductPrice(true);
                }
            });
        }

        // Initialize the refresh timer
        initRefreshTimer() {
            // Create timer elements if they don't exist
            if (!document.querySelector('.product-price-refresh-timer')) {
                const priceEl = document.querySelector('.oe_price');
                if (priceEl) {
                    // Create container
                    const timerContainer = document.createElement('div');
                    timerContainer.className = 'product-price-refresh-timer mt-2';

                    // Create timer display
                    const timerDisplay = document.createElement('span');
                    timerDisplay.id = 'product-price-timer-value';
                    timerDisplay.textContent = '01:00';

                    // Create refresh icon
                    const refreshIcon = document.createElement('i');
                    refreshIcon.className = 'fa fa-refresh refresh-product-price ml-2';
                    refreshIcon.title = _t('Refresh Price');

                    // Assemble elements
                    timerContainer.appendChild(document.createTextNode(_t('Price updates in: ')));
                    timerContainer.appendChild(timerDisplay);
                    timerContainer.appendChild(refreshIcon);

                    // Add to DOM
                    priceEl.parentNode.insertBefore(timerContainer, priceEl.nextSibling);

                    // Start countdown
                    this.startCountdown();
                }
            }
        }

        // Start the countdown timer
        startCountdown() {
            const timerElement = document.getElementById('product-price-timer-value');
            if (!timerElement) return;

            let seconds = 60;

            // Clear existing interval if any
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
            }

            // Update timer display
            this.updateCountdown(timerElement, seconds);

            // Start countdown
            const self = this;
            this.countdownInterval = setInterval(function() {
                seconds--;

                if (seconds <= 0) {
                    // Time's up, refresh price
                    clearInterval(self.countdownInterval);
                    self.refreshProductPrice(false);

                    // Restart countdown after a short delay
                    setTimeout(function() {
                        self.startCountdown();
                    }, 1000);
                } else {
                    // Update timer display
                    self.updateCountdown(timerElement, seconds);
                }
            }, 1000);
        }

        // Update the countdown timer display
        updateCountdown(element, seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;

            element.textContent = (minutes < 10 ? '0' + minutes : minutes) + ':' +
                                 (secs < 10 ? '0' + secs : secs);
        }

        // Get the product ID from the form
        getProductId() {
            const productForm = document.querySelector('form[action="/shop/cart/update"]');
            if (productForm) {
                const productInput = productForm.querySelector('input[name="product_id"]');
                if (productInput) {
                    return parseInt(productInput.value, 10);
                }
            }
            return null;
        }

        // Check if the current product is a jewelry product
        isJewelryProduct() {
            console.log("Checking if product is jewelry");

            // Check if the product has the requires_realtime_price attribute
            const requiresRealtime = document.querySelector('[data-requires-realtime-price="true"]');
            if (requiresRealtime) {
                console.log("Product has data-requires-realtime-price attribute");
                return true;
            }

            // Check for jewelry indicator
            const jewelryIndicator = document.querySelector('.jewelry-indicator-container');
            if (jewelryIndicator) {
                console.log("Product has jewelry-indicator-container");
                return true;
            }

            // Check for gold coin in the URL or title
            const url = window.location.href.toLowerCase();
            const title = document.title.toLowerCase();
            if (url.includes('gold-coin') || url.includes('silver-coin') ||
                title.includes('gold coin') || title.includes('silver coin')) {
                console.log("Product appears to be a coin based on URL/title");
                return true;
            }

            // Check product category or breadcrumbs for jewelry indicators
            const breadcrumbs = document.querySelectorAll('.breadcrumb .breadcrumb-item');
            for (let i = 0; i < breadcrumbs.length; i++) {
                const text = breadcrumbs[i].textContent.toLowerCase();
                if (text.includes('jewelry') || text.includes('gold') || text.includes('silver')) {
                    console.log("Product is in jewelry/gold/silver category");
                    return true;
                }
            }

            // Check for data attribute that explicitly marks the product as not requiring real-time pricing
            const noRealtimePrice = document.querySelector('[data-requires-realtime-price="false"]');
            if (noRealtimePrice) {
                console.log("Product explicitly marked as not requiring real-time pricing");
                return false;
            }

            console.log("Product does not appear to be jewelry");
            return false;
        }

        // Refresh the product price
        refreshProductPrice(showNotification) {
            console.log("refreshProductPrice called, showNotification:", showNotification);

            // Prevent multiple refreshes
            if (this.refreshing) {
                console.log("Already refreshing, skipping");
                return;
            }

            // Prevent refreshing too frequently
            const currentTime = new Date().getTime();
            if (currentTime - this.lastRefreshTime < 5000) {
                console.log("Skipping refresh - too soon since last refresh");
                return;
            }

            console.log("Starting price refresh for product ID:", this.productId);
            this.refreshing = true;
            this.lastRefreshTime = currentTime;

            // Add refreshing class to price element
            const priceEl = document.querySelector('.oe_price');
            if (priceEl) {
                console.log("Adding refreshing class to price element");
                priceEl.classList.add('refreshing');
            } else {
                console.log("Price element not found");
            }

            // Add spin class to refresh icon
            const refreshIcon = document.querySelector('.refresh-product-price');
            if (refreshIcon) {
                console.log("Adding spin class to refresh icon");
                refreshIcon.classList.add('fa-spin');
            } else {
                console.log("Refresh icon not found");
            }

            // Make AJAX call to refresh price using fetch API
            const self = this;
            console.log("Making fetch call to refresh price for product ID:", this.productId);

            // Create form data
            const formData = new FormData();
            formData.append('product_id', this.productId);

            fetch('/shop/product/refresh_price', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
                return response.json();
            })
            .then(data => {
                console.log("Price refresh response:", data);

                // Use the response directly
                if (data && data.success) {
                    console.log("Price refresh successful");

                    // Update price display
                    self.updateProductPrice(data);

                    // Restart countdown
                    self.startCountdown();

                    // Show notification if requested and price changed
                    if (showNotification && data.price_change) {
                        console.log("Showing price change notification");
                        self.showPriceChangeNotification(data.price_change);
                    }
                } else {
                    console.error("Failed to refresh product price:", data ? data.error : "Unknown error");
                    if (showNotification) {
                        alert(_t('Error: ') + (data && data.error ? data.error : _t('Failed to refresh product price')));
                    }
                }
            })
            .catch(error => {
                console.error("Error refreshing price:", error);

                if (showNotification) {
                    alert(_t('Error: Failed to refresh product price'));
                }
            })
            .finally(() => {
                // Remove refreshing indicators
                if (priceEl) {
                    console.log("Removing refreshing class from price element");
                    priceEl.classList.remove('refreshing');
                }
                if (refreshIcon) {
                    console.log("Removing spin class from refresh icon");
                    refreshIcon.classList.remove('fa-spin');
                }

                self.refreshing = false;
            });
        }

        // Update the product price display
        updateProductPrice(result) {
            console.log("Updating product price display with result:", result);

            // Update main price
            const priceEl = document.querySelector('.oe_price .oe_currency_value');
            if (priceEl && result.formatted_price) {
                console.log("Updating main price from", priceEl.innerHTML, "to", result.formatted_price);
                priceEl.innerHTML = result.formatted_price;
                priceEl.closest('.oe_price').classList.add('price-updated');

                // Remove highlight after a delay
                setTimeout(function() {
                    priceEl.closest('.oe_price').classList.remove('price-updated');
                }, 2000);
            } else {
                console.log("Main price element not found or formatted_price not provided");
                if (!priceEl) console.log("Price element not found");
                if (!result.formatted_price) console.log("formatted_price not provided in result");
            }

            // Update list price if available
            const listPriceEl = document.querySelector('.oe_default_price .oe_currency_value');
            if (listPriceEl && result.formatted_list_price) {
                console.log("Updating list price from", listPriceEl.innerHTML, "to", result.formatted_list_price);
                listPriceEl.innerHTML = result.formatted_list_price;
            } else if (listPriceEl) {
                console.log("List price element found but formatted_list_price not provided");
            } else {
                console.log("List price element not found");
            }

            // Update price breakup if available
            if (result.price_components && typeof updatePriceBreakup === 'function') {
                console.log("Updating price breakup with components:", result.price_components);
                try {
                    updatePriceBreakup(result.price_components);
                    console.log("Price breakup updated successfully");
                } catch (e) {
                    console.error("Error updating price breakup:", e);
                }
            } else {
                if (!result.price_components) console.log("price_components not provided in result");
                if (typeof updatePriceBreakup !== 'function') console.log("updatePriceBreakup function not found");

                // Try to find and update the price breakup manually
                this.updatePriceBreakupManually(result);
            }

            // Force update of any other price elements on the page
            const allPriceElements = document.querySelectorAll('.product_price .oe_currency_value, .product-price .oe_currency_value');
            if (allPriceElements.length > 0) {
                console.log("Found", allPriceElements.length, "additional price elements to update");
                allPriceElements.forEach(function(el) {
                    if (el !== priceEl && el !== listPriceEl && result.formatted_price) {
                        console.log("Updating additional price element from", el.innerHTML, "to", result.formatted_price);
                        el.innerHTML = result.formatted_price;
                    }
                });
            }

            // Angular integration removed - no longer needed
        }

        // Update price breakup manually if the updatePriceBreakup function is not available
        updatePriceBreakupManually(result) {
            console.log("Attempting to update price breakup manually");

            // Try to find the price breakup container
            const breakupContainer = document.querySelector('.price-breakup-container, .price-components, #priceBreakupModal .modal-body');
            if (!breakupContainer) {
                console.log("Price breakup container not found");
                return;
            }

            console.log("Found price breakup container:", breakupContainer);

            // If we have price components, try to update them
            if (result.price_components) {
                // Find all component rows
                const componentRows = breakupContainer.querySelectorAll('tr, .component-row');
                if (componentRows.length === 0) {
                    console.log("No component rows found in breakup container");
                    return;
                }

                console.log("Found", componentRows.length, "component rows");

                // Try to match components by name
                for (const component of result.price_components) {
                    for (const row of componentRows) {
                        const nameCell = row.querySelector('td:first-child, .component-name');
                        const valueCell = row.querySelector('td:last-child, .component-value');

                        if (nameCell && valueCell) {
                            const name = nameCell.textContent.trim().toLowerCase();
                            if (name.includes(component.name.toLowerCase())) {
                                console.log("Updating component", component.name, "from", valueCell.textContent, "to", component.value);
                                valueCell.textContent = component.value.toFixed(2);
                                valueCell.classList.add('price-updated');
                                setTimeout(() => {
                                    valueCell.classList.remove('price-updated');
                                }, 2000);
                            }
                        }
                    }
                }
            }
        }

        // This method was previously used for Angular.js integration
        // It has been removed as it's not needed in Odoo 17
        updateAngularPriceDisplay(result) {
            // Intentionally left empty - Angular.js integration removed
            return;
        }

        // Show a notification about the price change
        showPriceChangeNotification(priceChange) {
            const percentChange = priceChange.percent_change.toFixed(2);
            const direction = percentChange > 0 ? _t('increased') : _t('decreased');

            const message = _t('Price has been updated: ') + direction + ' ' + _t('by') + ' ' +
                         Math.abs(percentChange) + '% (' + _t('from') + ' ' +
                         priceChange.old_price.toFixed(2) + ' ' + _t('to') + ' ' +
                         priceChange.new_price.toFixed(2) + ')';

            alert(_t('Price Update') + '\n\n' + message);
        }

        // Clean up
        destroy() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
            }
        }
    }

    // Initialize the price refresh functionality
    const priceRefresh = new ProductPriceRefresh();

    // Store the instance for potential future use
    window.ProductPriceRefresh = priceRefresh;
});
