{
    'name': 'Metal Price Integration',
    'version': '********.0',
    'summary': 'On-demand price calculation for jewelry products with metal price integration',
    'description': """
        This module implements secure on-demand price calculation for jewelry products:
        - Server-side price calculation based on current metal rates
        - Price locking mechanism for active shopping sessions
        - Secure price verification during checkout
        - Price change notifications for customers
        - Audit trail for metal rates used in price calculations
    """,
    'category': 'Website/eCommerce',
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'product',
        'website_sale',
        'jewelry_management',
        'sale',
        'website_sale_stock',
        'web_editor',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/cron.xml',
        'data/system_parameters.xml',
        'views/price_lock_views.xml',
        'views/sale_order_views.xml',
        'views/website_templates.xml',
        'views/metal_rates_widget.xml',
        'views/frontend_layout_override.xml',
        'views/product_template.xml',
        'views/simple_product_page.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'ai_metal_price_integration/static/src/js/timer_script.js',
            'ai_metal_price_integration/static/src/js/product_price_refresh.js',
            'ai_metal_price_integration/static/src/css/price_lock_timer.css',
            'ai_metal_price_integration/static/src/css/price_updated.css',
        ],
        'web.assets_qweb': [
            'ai_metal_price_integration/static/src/xml/owl_templates.xml',
        ],
    },
    'demo': [],
    'installable': True,
    'application': False,
    'auto_install': False,
}
