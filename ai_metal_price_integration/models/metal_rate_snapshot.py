from odoo import models, fields, api

class SaleOrderMetalRate(models.Model):
    _name = 'sale.order.metal.rate'
    _description = 'Metal Rate Snapshot for Sale Order'
    
    order_id = fields.Many2one('sale.order', string="Sale Order", required=True, ondelete='cascade')
    order_line_id = fields.Many2one('sale.order.line', string="Order Line", ondelete='cascade')
    metal_id = fields.Many2one('jewelry.metal', string="Metal", required=True)
    rate = fields.Float(string="Rate", required=True, help="Price per gram at the time of snapshot")
    weight = fields.Float(string="Weight (grams)", required=True)
    date = fields.Datetime(string="Snapshot Date", required=True, default=fields.Datetime.now)
    value = fields.Float(string="Value", compute='_compute_value', store=True)
    
    @api.depends('rate', 'weight')
    def _compute_value(self):
        for record in self:
            record.value = record.rate * record.weight
