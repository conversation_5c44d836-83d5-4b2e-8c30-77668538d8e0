from odoo import models, fields, api, _
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class JewelryPriceLock(models.Model):
    _name = 'jewelry.price.lock'
    _description = 'Temporary Price Lock for Jewelry Products'

    order_id = fields.Many2one('sale.order', string="Sale Order", required=True, ondelete='cascade')
    price_lock_expiry = fields.Datetime(string="Price Lock Expiry", required=True)
    active = fields.Boolean(string="Active", default=True)

    # Remove the SQL constraint and handle it in code instead
    # _sql_constraints = [
    #     ('unique_order', 'unique(order_id, active)', 'Only one active price lock per order is allowed!')
    # ]

    @api.model
    def create(self, vals):
        """Create a new price lock"""
        # Set default expiry if not provided
        if 'price_lock_expiry' not in vals:
            lock_duration = int(self.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
            vals['price_lock_expiry'] = fields.Datetime.now() + timedelta(minutes=lock_duration)

        # Use ORM to deactivate existing locks
        if vals.get('order_id') and vals.get('active', True):
            try:
                # Find existing active locks for this order
                existing_locks = self.search([
                    ('order_id', '=', vals.get('order_id')),
                    ('active', '=', True)
                ])

                if existing_locks:
                    # Deactivate existing locks
                    existing_locks.write({'active': False})
            except Exception as e:
                _logger.error("Error deactivating existing price locks: %s", str(e))

                try:
                    # Try a more aggressive approach - delete all locks for this order
                    existing_locks = self.search([
                        ('order_id', '=', vals.get('order_id'))
                    ])
                    if existing_locks:
                        existing_locks.unlink()
                except Exception as e2:
                    _logger.error("Error deleting existing price locks: %s", str(e2))

        # Try to create the new lock
        try:
            return super(JewelryPriceLock, self).create(vals)
        except Exception as e:
            _logger.error("Error creating price lock: %s", str(e))
            # If creation fails, try a different approach using ORM
            try:
                # Make sure all existing locks are deactivated
                existing_locks = self.search([
                    ('order_id', '=', vals.get('order_id'))
                ])
                if existing_locks:
                    existing_locks.write({'active': False})

                # Create a new lock using create with sudo
                new_lock = self.sudo().create({
                    'order_id': vals.get('order_id'),
                    'price_lock_expiry': vals.get('price_lock_expiry'),
                    'active': True
                })

                return new_lock
            except Exception as e2:
                _logger.error("Error creating price lock via SQL: %s", str(e2))
                self.env.cr.rollback()
                # Return an empty recordset
                return self.env['jewelry.price.lock']

    def is_valid(self):
        """Check if price lock is still valid"""
        self.ensure_one()
        return self.active and fields.Datetime.now() <= self.price_lock_expiry

    @api.model
    def _cron_cleanup_expired_locks(self):
        """Cron job to clean up expired price locks"""
        expired_locks = self.search([
            ('active', '=', True),
            ('price_lock_expiry', '<', fields.Datetime.now())
        ])

        if expired_locks:
            expired_locks.write({'active': False})
            _logger.info("Deactivated %s expired price locks", len(expired_locks))

        return True

    @api.model
    def get_active_lock(self, order_id):
        """Get the active price lock for an order if it exists"""
        lock = self.search([
            ('order_id', '=', order_id),
            ('active', '=', True)
        ], limit=1)

        if lock and lock.is_valid():
            return lock
        return False

    @api.model
    def create_or_extend_lock(self, order_id):
        """Create a new lock or extend an existing one"""
        # First try to get an existing lock without any transaction
        lock = None
        try:
            lock = self.get_active_lock(order_id)
        except Exception as e:
            _logger.warning("Error getting active lock for order %s: %s", order_id, str(e))

        if lock:
            try:
                # Check if the lock is expired or about to expire (less than 10 seconds left)
                now = fields.Datetime.now()
                time_left = (lock.price_lock_expiry - now).total_seconds()

                if time_left < 10:  # Only extend if less than 10 seconds left
                    # Extend existing lock
                    lock_duration = int(self.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                    lock.price_lock_expiry = now + timedelta(minutes=lock_duration)
                    _logger.info("Extended price lock for order %s, new expiry: %s", order_id, lock.price_lock_expiry)
                else:
                    _logger.info("Using existing price lock for order %s, expiry: %s, time left: %s seconds",
                                order_id, lock.price_lock_expiry, time_left)

                return lock
            except Exception as e:
                _logger.warning("Error extending price lock for order %s: %s", order_id, str(e))
                # Continue to create a new lock

        # No valid lock found or error extending, create a new one
        # Use a completely separate transaction
        try:
            # Start a new cursor
            with self.env.registry.cursor() as new_cr:
                # Create a new environment with the new cursor
                env = self.env(cr=new_cr)

                # First deactivate any existing locks using ORM
                existing_locks = env['jewelry.price.lock'].sudo().search([
                    ('order_id', '=', order_id),
                    ('active', '=', True)
                ])
                if existing_locks:
                    existing_locks.write({'active': False})

                # Create a new lock
                lock_duration = int(env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                expiry_time = fields.Datetime.now() + timedelta(minutes=lock_duration)

                # Create the new lock using ORM
                new_lock = env['jewelry.price.lock'].sudo().create({
                    'order_id': order_id,
                    'price_lock_expiry': expiry_time,
                    'active': True
                })

                # Commit the transaction
                env.cr.commit()
                _logger.info("Created new price lock for order %s, expiry: %s", order_id, expiry_time)
                return new_lock
        except Exception as e:
            _logger.error("Failed to create price lock for order %s: %s", order_id, str(e))

            # Try one more approach with the current cursor
            try:
                # Rollback any failed transaction
                self.env.cr.rollback()

                # Delete all locks for this order using ORM
                existing_locks = self.env['jewelry.price.lock'].sudo().search([
                    ('order_id', '=', order_id)
                ])
                if existing_locks:
                    existing_locks.unlink()

                # Create a new lock
                lock_duration = int(self.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                expiry_time = fields.Datetime.now() + timedelta(minutes=lock_duration)

                # Create the new lock using ORM
                new_lock = self.env['jewelry.price.lock'].sudo().create({
                    'order_id': order_id,
                    'price_lock_expiry': expiry_time,
                    'active': True
                })
                _logger.info("Created new price lock (last resort method) for order %s, expiry: %s", order_id, expiry_time)
                return new_lock
            except Exception as e2:
                _logger.error("Failed to create price lock (last resort method) for order %s: %s", order_id, str(e2))
                return False
