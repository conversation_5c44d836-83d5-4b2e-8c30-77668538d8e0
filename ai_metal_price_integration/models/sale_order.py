from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from datetime import timedelta

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    price_lock_id = fields.Many2one('jewelry.price.lock', string="Price Lock",
                                   compute='_compute_price_lock', store=True)
    price_lock_expiry = fields.Datetime(related='price_lock_id.price_lock_expiry',
                                      string="Price Lock Expiry", readonly=True, store=False)
    is_price_locked = fields.Boolean(compute='_compute_is_price_locked',
                                   string="Is Price Locked", store=False)
    metal_rate_snapshot_ids = fields.One2many('sale.order.metal.rate', 'order_id',
                                             string="Metal Rate Snapshots")

    @api.depends('order_line', 'order_line.price_unit')
    def _compute_price_lock(self):
        """Compute the current price lock for the order"""
        for order in self:
            try:
                order.price_lock_id = self.env['jewelry.price.lock'].sudo().get_active_lock(order.id)
            except Exception as e:
                _logger.error("Error computing price_lock_id for order %s: %s", order.id, str(e))
                order.price_lock_id = False

    @api.depends('price_lock_id', 'price_lock_expiry')
    def _compute_is_price_locked(self):
        """Compute whether the order has a valid price lock"""
        now = fields.Datetime.now()
        for order in self:
            try:
                order.is_price_locked = order.price_lock_id and order.price_lock_expiry and order.price_lock_expiry > now
            except Exception as e:
                _logger.error("Error computing is_price_locked for order %s: %s", order.id, str(e))
                order.is_price_locked = False

    def check_price_validity(self):
        """Check if prices are still valid or need updating"""
        self.ensure_one()

        # Compute the price lock status
        self._compute_price_lock()
        self._compute_is_price_locked()

        if not self.is_price_locked:
            _logger.info("No active price lock or price lock expired for order %s, updating prices", self.id)
            # No active lock or lock expired, update prices
            self._update_jewelry_prices()
            # Create a new lock
            self.refresh_price_lock()
            return False

        _logger.info("Order %s has a valid price lock until %s", self.id, self.price_lock_expiry)
        return True

    def _update_jewelry_prices(self):
        """Update prices for all jewelry items in cart"""
        self.ensure_one()
        price_changes = []

        # Check if the order has a valid price lock
        if self.is_price_locked:
            _logger.info("Order %s has a valid price lock until %s, not updating jewelry prices",
                         self.id, self.price_lock_expiry)
            return price_changes

        _logger.info("Updating jewelry prices for order %s", self.id)

        for line in self.order_line:
            if hasattr(line.product_id, 'is_jewelry') and line.product_id.is_jewelry:
                current_price = line.product_id._calculate_jewelry_price()
                old_price = line.price_unit

                # Calculate percent change
                percent_change = 0
                if old_price:
                    percent_change = ((current_price - old_price) / old_price) * 100

                price_changes.append({
                    'line_id': line.id,
                    'product_id': line.product_id.id,
                    'product_name': line.product_id.name,
                    'old_price': old_price,
                    'new_price': current_price,
                    'percent_change': percent_change
                })

                # Update price
                line.price_unit = current_price

                # Create metal rate snapshot
                self._create_metal_rate_snapshot(line)

        return price_changes

    def _create_metal_rate_snapshot(self, order_line):
        """Create snapshots of current metal rates used for pricing"""
        if not order_line.product_id.is_jewelry:
            return

        # Delete existing snapshots for this line
        self.env['sale.order.metal.rate'].search([
            ('order_id', '=', self.id),
            ('order_line_id', '=', order_line.id)
        ]).unlink()

        # Create new snapshots
        for metal_weight in order_line.product_id.metal_weights:
            current_metal = self.env['jewelry.metal'].search([
                ('name', '=', metal_weight.metal_id.name),
                ('is_current', '=', True)
            ], limit=1)

            if current_metal:
                self.env['sale.order.metal.rate'].create({
                    'order_id': self.id,
                    'order_line_id': order_line.id,
                    'metal_id': metal_weight.metal_id.id,
                    'rate': current_metal.current_price,
                    'weight': metal_weight.weight,
                    'date': fields.Datetime.now(),
                })

    def action_confirm(self):
        """Override to verify prices before confirming order"""
        for order in self:
            # Check if there are jewelry products in the order
            has_jewelry = any(line.product_id.is_jewelry for line in order.order_line)

            if has_jewelry:
                # Verify prices
                for line in order.order_line:
                    if line.product_id.is_jewelry:
                        # Calculate current price
                        current_price = line.product_id._calculate_jewelry_price()

                        # Check for significant price difference (e.g., more than 5%)
                        if abs(line.price_unit - current_price) / current_price > 0.05:
                            raise UserError(_(
                                "The price of %s has changed significantly since it was added to your cart. "
                                "Please review your order and confirm the new prices before proceeding."
                            ) % line.product_id.name)

                # Create final metal rate snapshots
                for line in order.order_line:
                    if line.product_id.is_jewelry:
                        order._create_metal_rate_snapshot(line)

        return super(SaleOrder, self).action_confirm()

    def refresh_price_lock(self, force_refresh=False):
        """Refresh the price lock for this order

        Args:
            force_refresh: If True, always create a new price lock even if the current one is valid
        """
        self.ensure_one()

        # Recompute price lock status
        self._compute_price_lock()
        self._compute_is_price_locked()

        # Check if the price lock has expired or force refresh is requested
        if not self.is_price_locked or force_refresh:
            # Update jewelry prices before creating a new lock
            self._update_jewelry_prices()

            try:
                # Create a new price lock
                price_lock = self.env['jewelry.price.lock'].sudo().create_or_extend_lock(self.id)

                if not price_lock:
                    _logger.warning("Failed to create price lock for order %s, creating a default one", self.id)
                    # Create a default price lock
                    lock_duration = int(self.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                    expiry_time = fields.Datetime.now() + timedelta(minutes=lock_duration)
                    price_lock = self.env['jewelry.price.lock'].sudo().create({
                        'order_id': self.id,
                        'price_lock_expiry': expiry_time
                    })

                _logger.info("Created new price lock for order %s, expiry: %s", self.id, price_lock.price_lock_expiry)
                return price_lock
            except Exception as e:
                _logger.error("Error creating price lock for order %s: %s", self.id, str(e))
                # Create a default price lock using ORM methods
                try:
                    # Deactivate existing price locks
                    existing_locks = self.env['jewelry.price.lock'].sudo().search([
                        ('order_id', '=', self.id),
                        ('active', '=', True)
                    ])
                    if existing_locks:
                        existing_locks.write({'active': False})

                    # Create a new price lock
                    lock_duration = int(self.env['ir.config_parameter'].sudo().get_param('ai_metal_price_integration.lock_duration_minutes', '120'))
                    expiry_time = fields.Datetime.now() + timedelta(minutes=lock_duration)

                    price_lock = self.env['jewelry.price.lock'].sudo().create({
                        'order_id': self.id,
                        'price_lock_expiry': expiry_time,
                        'active': True
                    })
                    _logger.info("Created new price lock (fallback method) for order %s", self.id)
                    return price_lock
                except Exception as e2:
                    _logger.error("Failed to create price lock (fallback method) for order %s: %s", self.id, str(e2))
                    return False

        # Price lock is still valid, just return it
        _logger.info("Using existing price lock for order %s, expiry: %s", self.id, self.price_lock_expiry)
        return self.price_lock_id

    def add_to_cart(self, product_id, quantity=1.0):
        """Add product to cart with current price and create/extend price lock"""
        self.ensure_one()

        try:
            product = self.env['product.product'].browse(product_id)

            # If it's a jewelry product, calculate current price
            if hasattr(product, 'is_jewelry') and product.is_jewelry:
                current_price = product._calculate_jewelry_price()
            else:
                current_price = product.lst_price

            # Check if the product is already in the cart
            existing_line = self.order_line.filtered(lambda l: l.product_id.id == product_id)
            if existing_line:
                # Update existing line
                existing_line.write({
                    'product_uom_qty': existing_line.product_uom_qty + quantity,
                    'price_unit': current_price,
                })
                line = existing_line
            else:
                # Create order line with current price
                line = self.env['sale.order.line'].create({
                    'order_id': self.id,
                    'product_id': product_id,
                    'product_uom_qty': quantity,
                    'price_unit': current_price,
                })

            # Refresh the price lock
            self.refresh_price_lock()

            # Create metal rate snapshot if it's a jewelry product
            if hasattr(product, 'is_jewelry') and product.is_jewelry:
                self._create_metal_rate_snapshot(line)

            # Commit the transaction to avoid conflicts
            self.env.cr.commit()

            return line
        except Exception as e:
            # Rollback in case of error
            self.env.cr.rollback()
            _logger.error("Error adding product %s to cart: %s", product_id, str(e))
            return False
