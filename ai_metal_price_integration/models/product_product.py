from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class ProductProduct(models.Model):
    _inherit = 'product.product'

    # Add fields from ai_dynamic_cart_expiry to fix backend errors
    requires_realtime_price = fields.Boolean(
        string="Requires Realtime Price Computation",
        help="If checked, the product price will be computed in real-time based on current rates"
    )

    enable_cart_expiry = fields.Boolean(
        string="Enable Cart Expiry",
        help="If checked, the product will have a time limit in the cart"
    )

    cart_expiry_hours = fields.Float(
        string="Cart Expiry Hours",
        default=2.0,
        help="Number of hours before the product expires in the cart"
    )

    def _calculate_jewelry_price(self):
        """Calculate jewelry price on-demand using current metal rates"""
        self.ensure_one()

        if not self.is_jewelry:
            return self.lst_price

        total_cost = 0

        # Calculate metal cost
        for metal_weight in self.metal_weights:
            current_metal_price = self.env['jewelry.metal'].search([
                ('name', '=', metal_weight.metal_id.name),
                ('is_current', '=', True)
            ], limit=1)

            if current_metal_price:
                total_cost += metal_weight.weight * current_metal_price.current_price

        # Calculate diamond cost
        for diamond_weight in self.diamond_weights:
            current_diamond_price = self.env['jewelry.diamond.rate'].search([
                ('carat_from', '<=', diamond_weight.weight),
                ('carat_to', '>', diamond_weight.weight),
                ('cut', '=', diamond_weight.cut),
                ('color', '=', diamond_weight.color),
                ('clarity', '=', diamond_weight.clarity),
                ('is_current', '=', True)
            ], limit=1)

            if current_diamond_price:
                total_cost += diamond_weight.weight * current_diamond_price.price

        # Add labor cost
        total_cost += self.labor_cost

        # Apply margins
        if self.fixed_margin:
            total_cost += self.fixed_margin
        if self.percentage_margin:
            total_cost *= (1 + self.percentage_margin / 100)

        # Calculate taxes (GST)
        # Note: We don't add taxes here as Odoo will handle this during checkout
        # The price we return is the base price without taxes

        _logger.debug('Calculated on-demand price for %s: %s', self.name, total_cost)

        return total_cost

    def get_price_components(self):
        """Get detailed price components for display including GST"""
        self.ensure_one()

        if not self.is_jewelry:
            return False

        components = []
        metal_cost = 0
        diamond_cost = 0

        # Calculate metal components
        for metal_weight in self.metal_weights:
            current_metal_price = self.env['jewelry.metal'].search([
                ('name', '=', metal_weight.metal_id.name),
                ('is_current', '=', True)
            ], limit=1)

            if current_metal_price:
                value = metal_weight.weight * current_metal_price.current_price
                components.append({
                    'type': 'metal',
                    'name': metal_weight.metal_id.name,
                    'weight': metal_weight.weight,
                    'rate': current_metal_price.current_price,
                    'value': value
                })
                metal_cost += value

        # Calculate diamond components
        for diamond_weight in self.diamond_weights:
            current_diamond_price = self.env['jewelry.diamond.rate'].search([
                ('carat_from', '<=', diamond_weight.weight),
                ('carat_to', '>', diamond_weight.weight),
                ('cut', '=', diamond_weight.cut),
                ('color', '=', diamond_weight.color),
                ('clarity', '=', diamond_weight.clarity),
                ('is_current', '=', True)
            ], limit=1)

            if current_diamond_price:
                value = diamond_weight.weight * current_diamond_price.price
                components.append({
                    'type': 'diamond',
                    'name': f"Diamond ({diamond_weight.cut}, {diamond_weight.color}, {diamond_weight.clarity})",
                    'weight': diamond_weight.weight,
                    'rate': current_diamond_price.price,
                    'value': value
                })
                diamond_cost += value

        # Add labor cost
        if self.labor_cost:
            components.append({
                'type': 'labor',
                'name': 'Making Charges',
                'value': self.labor_cost
            })

        # Calculate subtotal before margins
        subtotal = metal_cost + diamond_cost + self.labor_cost

        # Add margins
        margin_value = 0

        # Always include fixed margin if it exists (even if it's 0)
        fixed_margin = self.fixed_margin or 0.0
        margin_value += fixed_margin
        components.append({
            'type': 'margin',
            'name': 'Fixed Margin',
            'value': fixed_margin
        })
        _logger.info("Added fixed margin component: %s", fixed_margin)

        # Add percentage margin if it exists
        if self.percentage_margin:
            percent_value = subtotal * (self.percentage_margin / 100)
            margin_value += percent_value
            components.append({
                'type': 'margin',
                'name': f'Percentage Margin ({self.percentage_margin}%)',
                'value': percent_value
            })
            _logger.info("Added percentage margin component: %s", percent_value)

        # Calculate base total
        base_total = subtotal + margin_value

        # Calculate taxes (GST)
        taxes = self.taxes_id.filtered(lambda t: t.company_id == self.env.company)
        tax_amount = 0
        tax_details = []

        if taxes:
            tax_results = taxes.compute_all(
                base_total,
                self.env.company.currency_id,
                1.0,
                product=self,
                partner=self.env['res.partner']
            )

            # Add each tax as a component
            for tax in tax_results['taxes']:
                tax_amount += tax['amount']
                tax_obj = self.env['account.tax'].browse(tax['id'])

                # Determine if it's CGST, SGST, or IGST based on tax name
                tax_type = 'GST'
                if 'CGST' in tax_obj.name:
                    tax_type = 'CGST'
                elif 'SGST' in tax_obj.name:
                    tax_type = 'SGST'
                elif 'IGST' in tax_obj.name:
                    tax_type = 'IGST'

                components.append({
                    'type': 'tax',
                    'name': f"{tax_type} ({tax_obj.amount}%)",
                    'value': tax['amount']
                })

                tax_details.append({
                    'id': tax['id'],
                    'name': tax_obj.name,
                    'amount': tax['amount'],
                    'base': tax['base'],
                    'type': tax_type
                })

        # Calculate total with taxes
        total_with_taxes = base_total + tax_amount

        return {
            'components': components,
            'metal_cost': metal_cost,
            'diamond_cost': diamond_cost,
            'labor_cost': self.labor_cost,
            'margin': margin_value,
            'subtotal': base_total,
            'taxes': tax_amount,
            'tax_details': tax_details,
            'total': total_with_taxes
        }
