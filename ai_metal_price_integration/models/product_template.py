from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Add fields from ai_dynamic_cart_expiry to fix backend errors
    requires_realtime_price = fields.Boolean(
        string="Requires Realtime Price Computation",
        help="If checked, the product price will be computed in real-time based on current rates"
    )

    enable_cart_expiry = fields.Boolean(
        string="Enable Cart Expiry",
        help="If checked, the product will have a time limit in the cart"
    )

    cart_expiry_hours = fields.Float(
        string="Cart Expiry Hours",
        default=2.0,
        help="Number of hours before the product expires in the cart"
    )
