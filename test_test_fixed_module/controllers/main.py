from odoo import http
from odoo.http import request
import json


class TestFixedModuleController(http.Controller):

    @http.route('/test_fixed_module', type='http', auth='public', website=True)
    def index(self, **kwargs):
        """Main page for Test Fixed Module"""
        return request.render('test_test_fixed_module.index', {})

    @http.route('/legal-services-complete', type='http', auth='public', website=True)
    def legal_services_complete_form(self, **kwargs):
        """Form page for Complete Legal Services Application"""
        return request.render('test_test_fixed_module.legal_services_complete_website_form', {})

    @http.route('/legal-services-complete/submit', type='http', auth='public', website=True, methods=['POST'], csrf=False)
    def legal_services_complete_submit(self, **kwargs):
        """Submit Complete Legal Services Application form"""
        try:
            # Create record from form data
            legal_services_complete_model = request.env['ovakil_legal_services_complete.legal_services_complete'].sudo()
            values = {}

            # Extract and process form fields
            if 'client_name' in kwargs and kwargs['client_name']:
                values['client_name'] = kwargs['client_name']
            if 'client_email' in kwargs and kwargs['client_email']:
                values['client_email'] = kwargs['client_email']
            if 'client_phone' in kwargs and kwargs['client_phone']:
                values['client_phone'] = kwargs['client_phone']
            if 'service_type' in kwargs and kwargs['service_type']:
                values['service_type'] = kwargs['service_type']
            if 'urgency_level' in kwargs and kwargs['urgency_level']:
                values['urgency_level'] = kwargs['urgency_level']
            if 'case_details' in kwargs and kwargs['case_details']:
                values['case_details'] = kwargs['case_details']
            if 'assigned_lawyer' in kwargs and kwargs['assigned_lawyer']:
                try:
                    values['assigned_lawyer'] = int(kwargs['assigned_lawyer'])
                except (ValueError, TypeError):
                    pass  # Skip invalid values
            if 'document_lines' in kwargs and kwargs['document_lines']:
                values['document_lines'] = kwargs['document_lines']
            if 'consultation_notes' in kwargs and kwargs['consultation_notes']:
                values['consultation_notes'] = kwargs['consultation_notes']
            if 'payment_history' in kwargs and kwargs['payment_history']:
                values['payment_history'] = kwargs['payment_history']
            if 'case_updates' in kwargs and kwargs['case_updates']:
                values['case_updates'] = kwargs['case_updates']

            # Handle missing selection fields with defaults
            if 'service_type' not in values or not values.get('service_type'):
                if 'rating' in 'service_type'.lower():
                    values['service_type'] = '3'  # Default to 3 stars
                elif 'priority' in 'service_type'.lower():
                    values['service_type'] = 'medium'  # Default to medium priority
                elif 'status' in 'service_type'.lower() or 'state' in 'service_type'.lower():
                    values['service_type'] = 'draft'  # Default to draft status
                else:
                    values['service_type'] = 'option1'  # Default to first option
            if 'urgency_level' not in values or not values.get('urgency_level'):
                if 'rating' in 'urgency_level'.lower():
                    values['urgency_level'] = '3'  # Default to 3 stars
                elif 'priority' in 'urgency_level'.lower():
                    values['urgency_level'] = 'medium'  # Default to medium priority
                elif 'status' in 'urgency_level'.lower() or 'state' in 'urgency_level'.lower():
                    values['urgency_level'] = 'draft'  # Default to draft status
                else:
                    values['urgency_level'] = 'option1'  # Default to first option

            # Add required name field if not present
            if 'name' in kwargs:
                values['name'] = kwargs['name']
            elif 'client_name' in values:
                values['name'] = values['client_name']
            else:
                values['name'] = 'Anonymous' 

            # Create the record
            record = legal_services_complete_model.create(values)

            return request.render('test_test_fixed_module.legal_services_complete_success', {
                'record': record
            })
        except Exception as e:
            # Return simple error response
            return request.make_response(
                f'<html><body><h1>Error</h1><p>Form submission failed: {str(e)}</p><a href="/legal-services-complete">Back to Form</a></body></html>',
                status=500
            )
