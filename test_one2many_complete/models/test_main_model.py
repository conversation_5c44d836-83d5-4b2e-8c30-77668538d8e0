from odoo import api, fields, models, _


class TestLineModel(models.Model):
    """Model for test lines"""
    _name = 'test.line.model'
    _description = 'Test Line Model'
    _order = 'sequence, id'

    name = fields.Char(string='Name', required=True)
    description = fields.Text(string='Description')
    sequence = fields.Integer(string='Sequence', default=10)
    test_main_id = fields.Many2one('test.main.model', 
                                  string='Test Main', ondelete='cascade')
    active = fields.Boolean(string='Active', default=True)


class TestNoteModel(models.Model):
    """Model for test notes"""
    _name = 'test.note.model'
    _description = 'Test Note Model'
    _order = 'create_date desc'

    name = fields.Char(string='Subject', required=True)
    note = fields.Text(string='Note', required=True)
    test_main_id = fields.Many2one('test.main.model', 
                                  string='Test Main', ondelete='cascade')
    user_id = fields.Many2one('res.users', string='Created By', default=lambda self: self.env.user)
    date = fields.Datetime(string='Date', default=fields.Datetime.now)


class TestMainModel(models.Model):
    """Main test model with One2many fields"""
    _name = 'test.main.model'
    _description = 'Test Main Model'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(string='Name', required=True, tracking=True)
    description = fields.Text(string='Description')
    test_lines = fields.One2many('test.line.model', 'test_main_id', string='Test Lines')
    test_notes = fields.One2many('test.note.model', 'test_main_id', string='Test Notes')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('done', 'Done')
    ], string='State', default='draft', tracking=True)
    active = fields.Boolean(string='Active', default=True)
