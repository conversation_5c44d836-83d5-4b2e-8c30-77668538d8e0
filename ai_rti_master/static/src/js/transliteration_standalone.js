/**
 * RTI Form Transliteration (Standalone Version)
 *
 * This script adds transliteration support to the RTI form fields,
 * allowing users to type in English and have their input automatically
 * transliterated to Hindi or Gujarati based on the selected language.
 */

(function() {
    'use strict';

    // Language codes for transliteration
    const LANGUAGE_CODES = {
        'hi': 'hindi',    // Hindi
        'gu': 'gujarati', // Gujarati
        'en': null        // English (no transliteration)
    };

    // Extract language code from full code (e.g., 'hi_IN' -> 'hi')
    function getShortLangCode(fullCode) {
        return fullCode ? fullCode.split('_')[0].toLowerCase() : 'en';
    }

    // Fields that should be transliterated
    // We'll dynamically find all text input fields in the form
    const TRANSLITERABLE_FIELDS = [
        // Personal information fields
        'x_first_name',
        'x_middle_name',
        'x_surname',
        // Note: x_mobile_number and x_whatsapp_number excluded (numbers only)
        // Note: x_email excluded (should remain in English)

        // Address fields
        'x_street',
        'x_flat_no',
        'x_landmark',
        // Note: x_pincode excluded (numbers only)

        // Department fields
        'x_manual_department_name',
        'x_manual_department_address',

        // RTI details
        'x_rti_details'
    ];

    // Fields that should NOT be transliterated (numbers, emails, etc.)
    const NON_TRANSLITERABLE_FIELDS = [
        'x_mobile_number',
        'x_whatsapp_number',
        'x_email',
        'x_pincode'
    ];

    // Function to find all text input fields in the form
    function findAllTextInputFields() {
        const form = document.getElementById('rtiForm');
        if (!form) return [];

        // Get all input fields of type text, email, tel, and textareas
        const textInputs = form.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], textarea');

        // Convert to array and get IDs, excluding non-transliterable fields
        return Array.from(textInputs)
            .filter(input => input.id) // Only include elements with IDs
            .filter(input => !NON_TRANSLITERABLE_FIELDS.includes(input.id)) // Exclude non-transliterable fields
            .map(input => input.id);
    }

    // Create a transliteration class
    class RtiTransliteration {
        constructor() {
            console.log('Initializing RtiTransliteration');
            this._initTransliteration();
            this._setupEventListeners();
        }

        /**
         * Set up event listeners
         *
         * @private
         */
        _setupEventListeners() {
            console.log('Setting up event listeners');
            const toggleBtn = document.getElementById('toggleTransliteration');
            if (toggleBtn) {
                console.log('Found toggle button, adding click listener');
                // Remove any existing listeners first to prevent duplicates
                toggleBtn.removeEventListener('click', this._onToggleTransliteration.bind(this));
                // Add the click listener
                toggleBtn.addEventListener('click', this._onToggleTransliteration.bind(this));

                // Set initial data-enabled attribute if not already set
                if (!toggleBtn.hasAttribute('data-enabled')) {
                    toggleBtn.setAttribute('data-enabled', toggleBtn.checked ? 'true' : 'false');
                }
            }

            // Add input listeners to transliterable fields
            document.querySelectorAll('.transliterable').forEach(field => {
                field.addEventListener('input', this._onInputTransliterable.bind(this));
            });
        }

        /**
         * Initialize transliteration
         *
         * @private
         */
        _initTransliteration() {
            console.log('Initializing transliteration');

            // Find any new text input fields that might have been added dynamically
            const allTextFields = findAllTextInputFields();
            console.log('Found text input fields during initialization:', allTextFields);

            // Add transliterable class to any new fields
            allTextFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && !field.classList.contains('transliterable')) {
                    console.log('Adding transliterable class to new field:', fieldName);
                    field.classList.add('transliterable');
                }
            });

            // Get the language code
            const langCode = this._getLanguageCode();
            console.log('Language code for initialization:', langCode);

            // Check if we should enable transliteration by default
            const isIndianLanguage = langCode === 'hi' || langCode === 'gu';
            const toggleBtn = document.getElementById('toggleTransliteration');
            const shouldEnableTransliteration = isIndianLanguage && toggleBtn && toggleBtn.checked;

            if (shouldEnableTransliteration) {
                console.log('Enabling transliteration by default for', langCode);
                // Try to use AI4Bharat directly first for better reliability
                this._enableAI4BharatTransliteration(langCode);
            }

            // Load Google Input Tools as a backup option
            this._loadTransliterationAPI();
        }

        /**
         * Get language code from window object
         *
         * @private
         * @returns {string} Language code (hi, gu, en)
         */
        _getLanguageCode() {
            // Get language code from window object (set in the template)
            if (window.language_code) {
                console.log('Language code from window:', window.language_code);
                const shortCode = getShortLangCode(window.language_code);
                console.log('Short language code:', shortCode);
                return shortCode;
            }
            console.log('No language code found in window object');
            return 'en';
        }

        /**
         * Load Google Input Tools API for transliteration
         *
         * @private
         */
        _loadTransliterationAPI() {
            if (window.google && window.google.elements && window.google.elements.transliteration) {
                console.log('Google Transliteration API already loaded');
                return; // Already loaded
            }

            console.log('Loading Google Transliteration API');

            try {
                // Use the modern approach as per the documentation
                // https://developers.google.com/transliterate/v1/getting_started
                const script = document.createElement('script');
                script.src = 'https://www.google.com/jsapi';
                script.async = true;
                script.onload = () => {
                    console.log('Google jsapi loaded');

                    // Load the transliteration API
                    if (window.google && typeof window.google.load === 'function') {
                        try {
                            // Load the transliteration API
                            window.google.load('elements', '1', {
                                packages: 'transliteration',
                                callback: () => {
                                    console.log('Google Transliteration API loaded successfully');
                                    // Check if transliteration is enabled
                                    const toggleBtn = document.getElementById('toggleTransliteration');
                                    if (toggleBtn && toggleBtn.checked) {
                                        const langCode = this._getLanguageCode();
                                        this._enableTransliteration(langCode);
                                    }
                                }
                            });
                        } catch (error) {
                            console.error('Error loading Google transliteration package:', error);
                            this._fallbackToAI4Bharat();
                        }
                    } else {
                        console.error('Google API loaded but google.load is not available');
                        this._fallbackToAI4Bharat();
                    }
                };

                script.onerror = () => {
                    console.error('Failed to load Google jsapi');
                    this._fallbackToAI4Bharat();
                };

                document.head.appendChild(script);
            } catch (error) {
                console.error('Error setting up Google Transliteration API:', error);
                this._fallbackToAI4Bharat();
            }
        }

        /**
         * Fallback to AI4Bharat transliteration when Google API fails
         *
         * @private
         */
        _fallbackToAI4Bharat() {
            console.log('Falling back to AI4Bharat transliteration');
            const langCode = this._getLanguageCode();
            if (LANGUAGE_CODES[langCode]) {
                this._enableAI4BharatTransliteration(langCode);
            }
        }

        /**
         * Handle toggle transliteration button click
         *
         * @private
         * @param {Event} ev - Click event
         */
        _onToggleTransliteration(ev) {
            console.log('Toggle transliteration clicked');

            // Prevent default behavior to avoid any form submission
            ev.preventDefault();
            ev.stopPropagation();

            // Get the toggle button
            const toggleBtn = ev.currentTarget || document.getElementById('toggleTransliteration');
            console.log('Toggle button:', toggleBtn);

            // Get the current data-enabled state (before the click)
            const currentState = toggleBtn.getAttribute('data-enabled') === 'true';
            console.log('Current data-enabled state:', currentState);

            // Toggle the state (opposite of current state)
            const newState = !currentState;
            console.log('New state will be:', newState);

            // Update the checkbox to match our new state
            toggleBtn.checked = newState;

            // Update the data attribute
            toggleBtn.setAttribute('data-enabled', newState ? 'true' : 'false');

            // Save the state in localStorage for persistence across page loads
            try {
                localStorage.setItem('transliteration_enabled', newState ? 'true' : 'false');
                console.log('Saved transliteration state to localStorage:', newState);
            } catch (e) {
                console.warn('Could not save transliteration state to localStorage:', e);
            }

            // Set a global flag for other methods to check
            window.transliterationEnabled = newState;

            if (newState) {
                console.log('Enabling transliteration');
                // Enable transliteration
                const langCode = this._getLanguageCode();
                console.log('Using language code for transliteration:', langCode);
                this._enableTransliteration(langCode);

                if (toggleBtn.tagName === 'BUTTON') {
                    toggleBtn.innerHTML = '<i class="fa fa-language"></i> Disable Transliteration';
                    toggleBtn.classList.remove('btn-outline-primary');
                    toggleBtn.classList.add('btn-primary');
                }
            } else {
                console.log('Disabling transliteration');
                // Disable transliteration
                this._disableTransliteration();

                if (toggleBtn.tagName === 'BUTTON') {
                    toggleBtn.innerHTML = '<i class="fa fa-language"></i> Enable Transliteration';
                    toggleBtn.classList.remove('btn-primary');
                    toggleBtn.classList.add('btn-outline-primary');
                }
            }
        }

        /**
         * Enable transliteration for input fields
         *
         * @private
         * @param {string} langCode - Language code (hi, gu)
         */
        _enableTransliteration(langCode) {
            console.log('Enabling transliteration for language code:', langCode);

            if (!LANGUAGE_CODES[langCode]) {
                console.warn('Unsupported language code:', langCode);
                return; // Unsupported language
            }

            const language = LANGUAGE_CODES[langCode];
            console.log('Using language:', language);

            // First, make sure all fields are properly set up for transliteration
            this._setupAllFieldsForTransliteration(langCode);

            // Initialize Google Transliteration
            if (window.google && window.google.elements && window.google.elements.transliteration) {
                console.log('Google Transliteration API is available, using it');
                try {
                    // Get all transliterable fields
                    const fields = document.querySelectorAll('.transliterable');
                    console.log('Transliterable fields found:', fields.length);

                    if (fields.length === 0) {
                        console.warn('No transliterable fields found');
                        return;
                    }

                    const fieldIds = Array.from(fields).map(field => field.id).filter(id => id);
                    console.log('Field IDs for transliteration:', fieldIds);

                    if (fieldIds.length === 0) {
                        console.warn('No valid field IDs found for transliteration');
                        this._enableAI4BharatTransliteration(langCode);
                        return;
                    }

                    // Create options for transliteration control
                    // According to https://developers.google.com/transliterate/v1/getting_started
                    const options = {
                        sourceLanguage: 'en',
                        destinationLanguage: [language],
                        shortcutKey: 'ctrl+g',
                        transliterationEnabled: true
                    };
                    console.log('Creating transliteration control with options:', options);

                    try {
                        // Create the transliteration control
                        const control = new google.elements.transliteration.TransliterationControl(options);

                        // Enable transliteration for these fields
                        control.makeTransliteratable(fieldIds);
                        console.log('Google transliteration enabled for fields');

                        // Store control for later use
                        this.transliterationControl = control;

                        // Add a visual indicator that transliteration is active
                        fields.forEach(field => {
                            field.classList.add('transliteration-active');
                            field.setAttribute('data-transliteration-method', 'google');
                            field.setAttribute('data-transliteration-enabled', 'true');
                            field.setAttribute('data-lang-code', langCode);
                        });

                        // Add a state change listener
                        control.addEventListener(
                            google.elements.transliteration.TransliterationControl.EventType.STATE_CHANGED,
                            () => {
                                console.log('Transliteration state changed');
                            }
                        );
                    } catch (error) {
                        console.error('Error creating Google transliteration control:', error);
                        this._enableAI4BharatTransliteration(langCode);
                    }
                } catch (error) {
                    console.error('Error initializing Google transliteration:', error);
                    // Fallback to AI4Bharat API if Google API initialization fails
                    this._enableAI4BharatTransliteration(langCode);
                }
            } else {
                console.log('Google Transliteration API not available, falling back to AI4Bharat');
                // Fallback to AI4Bharat API if Google API is not available
                this._enableAI4BharatTransliteration(langCode);
            }

            // Add active class to transliterable fields if not using Google API
            // (Google API already adds the class in its own section)
            if (!window.google || !window.google.elements || !window.google.elements.transliteration) {
                document.querySelectorAll('.transliterable').forEach(function (field) {
                    field.classList.add('transliteration-active');
                    field.setAttribute('data-transliteration-enabled', 'true');
                    field.setAttribute('data-lang-code', langCode);
                });
                console.log('Added transliteration-active class to fields (AI4Bharat mode)');
            }
        }

        /**
         * Set up all fields for transliteration
         *
         * @private
         * @param {string} langCode - Language code (hi, gu)
         */
        _setupAllFieldsForTransliteration(langCode) {
            console.log('Setting up all fields for transliteration');

            // Find all transliterable fields again to make sure we have the latest
            const allTextFields = findAllTextInputFields();
            console.log('Found text input fields:', allTextFields);

            // Make sure all fields have the transliterable class (excluding non-transliterable fields)
            allTextFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field && !NON_TRANSLITERABLE_FIELDS.includes(fieldId)) {
                    console.log('Setting up field for transliteration:', fieldId);
                    if (!field.classList.contains('transliterable')) {
                        field.classList.add('transliterable');
                    }

                    // Set up the field for transliteration
                    field.classList.add('transliteration-active');
                    field.setAttribute('data-transliteration-enabled', 'true');
                    field.setAttribute('data-lang-code', langCode);
                    field.setAttribute('data-transliteration-method', 'ai4bharat');

                    // Add input event listener if not already added
                    const boundHandler = this._onInputTransliterable.bind(this);
                    // Remove any existing listeners to avoid duplicates
                    field.removeEventListener('input', boundHandler);
                    // Add the listener
                    field.addEventListener('input', boundHandler);
                }
            });

            // Also set up any fields that might have been missed (excluding non-transliterable fields)
            document.querySelectorAll('input[type="text"], textarea').forEach(field => {
                if (!field.classList.contains('transliterable') && field.id && !NON_TRANSLITERABLE_FIELDS.includes(field.id)) {
                    console.log('Adding transliterable class to additional field:', field.id);
                    field.classList.add('transliterable');
                    field.classList.add('transliteration-active');
                    field.setAttribute('data-transliteration-enabled', 'true');
                    field.setAttribute('data-lang-code', langCode);
                    field.setAttribute('data-transliteration-method', 'ai4bharat');

                    // Add input event listener
                    const boundHandler = this._onInputTransliterable.bind(this);
                    field.removeEventListener('input', boundHandler);
                    field.addEventListener('input', boundHandler);
                }
            });

            console.log('All fields set up for transliteration');
        }

        /**
         * Enable transliteration using AI4Bharat API (fallback)
         *
         * @private
         * @param {string} langCode - Language code (hi, gu)
         */
        _enableAI4BharatTransliteration(langCode) {
            console.log('Enabling AI4Bharat transliteration for language:', langCode);

            // First, make sure all fields are properly set up for transliteration
            this._setupAllFieldsForTransliteration(langCode);

            // Add specific AI4Bharat attributes to all transliterable fields
            const fields = document.querySelectorAll('.transliterable');
            console.log('Found', fields.length, 'transliterable fields for AI4Bharat');

            fields.forEach(field => {
                console.log('Setting up AI4Bharat transliteration for field:', field.id);
                field.setAttribute('data-transliteration-method', 'ai4bharat');
            });

            console.log('AI4Bharat transliteration setup complete');
        }

        /**
         * Disable transliteration for input fields
         *
         * @private
         */
        _disableTransliteration() {
            console.log('Disabling transliteration');
            // Disable Google Transliteration if available
            if (this.transliterationControl) {
                try {
                    this.transliterationControl.disableTransliteration();
                    console.log('Google transliteration disabled');
                } catch (error) {
                    console.error('Error disabling Google transliteration:', error);
                }
                this.transliterationControl = null;
            }

            // Remove event listeners and classes
            document.querySelectorAll('.transliterable').forEach(function (field) {
                field.classList.remove('transliteration-active');
                field.setAttribute('data-transliteration-enabled', 'false');
            });
            console.log('Removed transliteration-active class from fields');
        }

        /**
         * Handle input in transliterable fields
         * This is used for the AI4Bharat API fallback
         *
         * @private
         * @param {Event} ev - Input event
         */
        _onInputTransliterable(ev) {
            console.log('Input detected in transliterable field');
            const field = ev.target;
            console.log('Field ID:', field.id, 'Current value:', field.value);

            // Check if transliteration is globally enabled
            const toggleBtn = document.getElementById('toggleTransliteration');
            const isTransliterationEnabled = toggleBtn && (toggleBtn.getAttribute('data-enabled') === 'true' || window.transliterationEnabled === true);

            console.log('Global transliteration enabled:', isTransliterationEnabled);

            // Only process if transliteration is globally enabled
            if (!isTransliterationEnabled) {
                console.log('Transliteration not enabled globally, skipping');
                return;
            }

            // Get the current language code
            const langCode = this._getLanguageCode();
            console.log('Using language code for transliteration:', langCode);

            // Skip if language is not supported
            if (!LANGUAGE_CODES[langCode]) {
                console.log('Language not supported for transliteration:', langCode);
                return;
            }

            // Always ensure this field is properly set up for transliteration
            console.log('Ensuring field is set up for transliteration');
            field.setAttribute('data-transliteration-enabled', 'true');
            field.setAttribute('data-lang-code', langCode);
            field.setAttribute('data-transliteration-method', 'ai4bharat');
            field.classList.add('transliteration-active');
            field.classList.add('transliterable');

            // Get the text from the field
            const text = field.value;

            // Skip empty text
            if (!text || text.trim() === '') {
                console.log('Empty text, skipping transliteration');
                return;
            }

            // Only transliterate if there's text and it ends with a space or punctuation
            if (text.endsWith(' ') || /[.,!?;:]$/.test(text)) {
                console.log('Text ends with space or punctuation, transliterating:', text);
                this._transliterateText(text, langCode, function(result) {
                    console.log('Transliteration result:', result);
                    if (result) {
                        field.value = result;
                        console.log('Field value updated to:', result);
                    }
                });
            } else {
                console.log('Text does not end with space or punctuation, not transliterating yet');
            }
        }

        /**
         * Transliterate text using AI4Bharat API
         *
         * @private
         * @param {string} text - Text to transliterate
         * @param {string} langCode - Language code (hi, gu)
         * @param {function} callback - Callback function with result
         */
        _transliterateText(text, langCode, callback) {
            console.log('Transliterating text:', text, 'to language:', langCode);
            // Use server-side endpoint for transliteration
            // This will call the AI4Bharat API or other transliteration service

            // Extract the language code (hi, gu) from the full code (hi_IN, gu_IN)
            const shortLangCode = getShortLangCode(langCode);
            console.log('Using short language code for API call:', shortLangCode);

            // Call the server-side endpoint
            $.ajax({
                url: '/rti/transliterate',
                type: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        text: text,
                        target_lang: shortLangCode
                    }
                }),
                success: function(response) {
                    console.log('Transliteration API response:', response);
                    if (response.result) {
                        callback(response.result.result || text);
                    } else {
                        console.warn('No result in transliteration response');
                        callback(text);
                    }
                },
                error: function(error) {
                    console.error('Error calling transliteration API:', error);
                    callback(text); // Fallback to original text on error
                }
            });
        }
    }

    // Expose the class globally so it can be accessed from other scripts
    window.RtiTransliteration = RtiTransliteration;

    // Initialize when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing transliteration');

        // Get the current language code
        const langCode = window.language_code ? getShortLangCode(window.language_code) : 'en';
        console.log('Current language code:', langCode);

        // Check if the language is Hindi or Gujarati
        const isIndianLanguage = langCode === 'hi' || langCode === 'gu';
        console.log('Is Indian language (Hindi/Gujarati):', isIndianLanguage);

        // Initialize global state - enable by default for Hindi and Gujarati
        window.transliterationEnabled = isIndianLanguage;

        // Get all text input fields from the form
        const allTextFields = findAllTextInputFields();
        console.log('Found text input fields:', allTextFields);

        // Combine predefined fields with dynamically found fields
        const allFields = [...new Set([...TRANSLITERABLE_FIELDS, ...allTextFields])];
        console.log('All transliterable fields:', allFields);

        // Add transliterable class to fields (excluding non-transliterable fields)
        allFields.forEach(function (fieldName) {
            const field = document.getElementById(fieldName);
            if (field && !NON_TRANSLITERABLE_FIELDS.includes(fieldName)) {
                console.log('Adding transliterable class to field:', fieldName);
                field.classList.add('transliterable');
            }
        });

        // Initialize the transliteration
        window.rtiTransliteration = new RtiTransliteration();

        // Check if we have a saved state in localStorage
        try {
            const savedState = localStorage.getItem('transliteration_enabled');
            console.log('Retrieved saved transliteration state:', savedState);

            if (savedState !== null) {
                // User has a saved preference, use that
                const enableTransliteration = savedState === 'true';

                // Get the toggle button
                const toggleBtn = document.getElementById('toggleTransliteration');
                if (toggleBtn) {
                    // Set the checkbox state
                    toggleBtn.checked = enableTransliteration;
                    toggleBtn.setAttribute('data-enabled', enableTransliteration ? 'true' : 'false');

                    // Set global flag
                    window.transliterationEnabled = enableTransliteration;

                    if (enableTransliteration) {
                        // Enable transliteration
                        window.rtiTransliteration._enableTransliteration(langCode);
                        console.log('Restored transliteration state to enabled from user preference');
                    } else {
                        console.log('Transliteration disabled based on user preference');
                    }
                }
            } else {
                // No saved preference, use default based on language
                const toggleBtn = document.getElementById('toggleTransliteration');
                if (toggleBtn) {
                    // For Hindi and Gujarati, enable by default
                    if (isIndianLanguage) {
                        toggleBtn.checked = true;
                        toggleBtn.setAttribute('data-enabled', 'true');
                        window.transliterationEnabled = true;

                        // Enable transliteration
                        window.rtiTransliteration._enableTransliteration(langCode);
                        console.log('Enabled transliteration by default for', langCode);

                        // Save this state as the user's preference
                        localStorage.setItem('transliteration_enabled', 'true');
                    } else {
                        // For other languages, disable by default
                        toggleBtn.checked = false;
                        toggleBtn.setAttribute('data-enabled', 'false');
                        window.transliterationEnabled = false;
                        console.log('Transliteration disabled by default for non-Indian language');
                    }
                }
            }
        } catch (e) {
            console.warn('Could not retrieve transliteration state from localStorage:', e);

            // Fallback to default behavior based on language
            if (isIndianLanguage) {
                const toggleBtn = document.getElementById('toggleTransliteration');
                if (toggleBtn) {
                    toggleBtn.checked = true;
                    toggleBtn.setAttribute('data-enabled', 'true');
                    window.transliterationEnabled = true;
                    window.rtiTransliteration._enableTransliteration(langCode);
                    console.log('Enabled transliteration by default for', langCode, '(fallback)');
                }
            }
        }

        // Add a debug message to help troubleshoot
        console.log('Transliteration initialization complete. Global state:', window.transliterationEnabled);

        // Set up a MutationObserver to detect when new fields are added to the form
        const formObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    console.log('Form changed, checking for new fields');
                    // Re-setup all fields for transliteration
                    if (window.rtiTransliteration && window.transliterationEnabled) {
                        window.rtiTransliteration._setupAllFieldsForTransliteration(langCode);
                    }
                }
            });
        });

        // Start observing the form for changes
        const form = document.querySelector('form');
        if (form) {
            formObserver.observe(form, { childList: true, subtree: true });
            console.log('Form observer started');
        }

        // Set up a timer to periodically check and setup all fields
        // This helps with fields that are added dynamically or after pre-filling
        if (isIndianLanguage) {
            setInterval(function() {
                if (window.rtiTransliteration && window.transliterationEnabled) {
                    console.log('Periodic check: re-setting up all fields for transliteration');
                    window.rtiTransliteration._setupAllFieldsForTransliteration(langCode);
                }
            }, 2000); // Check every 2 seconds
        }
    });
})();
