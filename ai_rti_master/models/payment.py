from odoo import models, fields, api

class RtiPayment(models.Model):
    _name = 'rti.payment'
    _description = 'RTI Payment'
    _rec_name = 'reference'
    _order = 'create_date desc'

    reference = fields.Char('Reference', readonly=True)
    rti_id = fields.Many2one('ai_rti_master', string='RTI Application', required=True, ondelete='cascade')
    amount = fields.Float('Amount', required=True)
    addon_ids = fields.Many2many('product.template', string='Selected Addons', domain=[('is_rti_addon', '=', True)])
    state = fields.Selection([
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', required=True)
    payment_date = fields.Datetime('Payment Date', readonly=True)
    transaction_id = fields.Char('Transaction ID', readonly=True)
    payment_method = fields.Char('Payment Method', readonly=True)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get('reference'):
                vals['reference'] = self.env['ir.sequence'].next_by_code('rti.payment.sequence')
        return super().create(vals_list)

    def action_cancel(self):
        self.write({'state': 'cancelled'})
