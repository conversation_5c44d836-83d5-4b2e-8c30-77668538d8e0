import base64
from datetime import datetime
from odoo import http, _, fields
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.exceptions import AccessError, MissingError
from odoo.tools import consteq
import logging
import werkzeug

_logger = logging.getLogger(__name__)

class RtiPortal(CustomerPortal):

    def _prepare_portal_layout_values(self):
        values = super()._prepare_portal_layout_values()
        values['rti_count'] = request.env['ai_rti_master'].search_count([])
        return values

    def _get_rti_domain(self, search=None):
        domain = []
        if search:
            domain += [
                '|', '|', '|',
                ('name', 'ilike', search),
                ('x_first_name', 'ilike', search),
                ('x_surname', 'ilike', search),
                ('x_rti_details', 'ilike', search)
            ]
        return domain

    @http.route(['/my/rti', '/my/rti/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_rti(self, page=1, sortby=None, search=None, search_in='all', **kw):
        values = self._prepare_portal_layout_values()
        RTI = request.env['ai_rti_master']

        searchbar_sortings = {
            'date': {'label': _('Newest'), 'order': 'create_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'state': {'label': _('Status'), 'order': 'state'},
        }

        searchbar_inputs = {
            'all': {'input': 'all', 'label': _('Search in All')},
            'name': {'input': 'name', 'label': _('Search in Reference')},
            'applicant': {'input': 'applicant', 'label': _('Search in Applicant')},
            'content': {'input': 'content', 'label': _('Search in Content')},
        }

        if not sortby:
            sortby = 'date'
        sort_order = searchbar_sortings[sortby]['order']

        domain = self._get_rti_domain(search=search)

        # count for pager
        rti_count = RTI.search_count(domain)

        # pager
        pager = portal_pager(
            url="/my/rti",
            url_args={'sortby': sortby},
            total=rti_count,
            page=page,
            step=self._items_per_page
        )

        # content according to pager and archive selected
        rtis = RTI.search(
            domain,
            order=sort_order,
            limit=self._items_per_page,
            offset=pager['offset']
        )

        values.update({
            'applications': rtis,
            'page_name': 'rti',
            'pager': pager,
            'default_url': '/my/rti',
            'searchbar_sortings': searchbar_sortings,
            'sortby': sortby,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search,
        })
        return request.render("ai_rti_master.portal_my_rti", values)

    @http.route(['/my/rti/<int:rti_id>'], type='http', auth="user", website=True)
    def portal_my_rti_application(self, rti_id, **kw):
        try:
            rti_sudo = self._document_check_access('ai_rti_master', rti_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        addons = request.env['product.template'].sudo().search([('is_rti_addon', '=', True)])
        selected_addon_quantities = rti_sudo.product_template_addon_quantity_ids
        selected_addon_ids = selected_addon_quantities.mapped('addon_id').ids
        amount = sum(selected_addon_quantities.mapped(lambda x: x.addon_id.list_price * x.quantity)) or 10.0  # Base RTI fee

        values = {
            'page_name': 'rti_detail',
            'rti': rti_sudo,
            'addons': addons,
            'selected_addon_ids': selected_addon_ids,
            'amount': amount,
            'user': request.env.user
        }

        # Add history if present
        if hasattr(rti_sudo, 'history_ids'):
            values['history'] = rti_sudo.history_ids.sorted('create_date', reverse=True)

        return request.render("ai_rti_master.portal_my_rti_application", values)

    @http.route(['/my/rti/<int:rti_id>/payment'], type='http', auth="user", website=True)
    def portal_rti_payment(self, rti_id, **post):
        try:
            rti_sudo = self._document_check_access('ai_rti_master', rti_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Process form data for addons if submitted
        selected_addons = []
        addon_quantities = []
        total_amount = 20.0  # Base RTI fee

        if post and post.get('submitted'):
            # Handle delivery option (radio button)
            if 'delivery_option' in post and post['delivery_option']:
                delivery_addon_id = int(post['delivery_option'])
                delivery_addon = request.env['product.template'].sudo().browse(delivery_addon_id)
                if delivery_addon.exists():
                    quantity = 1
                    if hasattr(delivery_addon, 'quantity_per_dept') and delivery_addon.quantity_per_dept:
                        qty_key = f'qty_{delivery_addon_id}'
                        quantity = int(post.get(qty_key, 1))

                    selected_addons.append(delivery_addon)
                    addon_quantities.append((delivery_addon, quantity))
                    total_amount += delivery_addon.list_price * quantity

            # Handle additional services (checkboxes)
            for key, value in post.items():
                if key.startswith('addon_'):
                    try:
                        addon_id = int(key.split('_')[1])
                        addon = request.env['product.template'].sudo().browse(addon_id)
                        if addon.exists():
                            quantity = 1
                            if hasattr(addon, 'quantity_per_dept') and addon.quantity_per_dept:
                                qty_key = f'qty_{addon_id}'
                                quantity = int(post.get(qty_key, 1))

                            selected_addons.append(addon)
                            addon_quantities.append((addon, quantity))
                            total_amount += addon.list_price * quantity
                    except Exception as e:
                        _logger.error(f"Error processing addon {key}: {str(e)}")
        else:
            # If no form submitted, use existing addon quantities
            selected_addon_quantities = rti_sudo.product_template_addon_quantity_ids
            total_amount = sum(selected_addon_quantities.mapped(lambda x: x.addon_id.list_price * x.quantity)) or 20.0  # Base RTI fee

        amount = total_amount

        # Get Razorpay provider (in either enabled or test state)
        razorpay_provider = request.env['payment.provider'].sudo().search([
            ('code', '=', 'razorpay'),
            ('state', 'in', ['enabled', 'test']),  # Include test mode providers
            ('company_id', 'in', [False, request.env.company.id]),
        ], limit=1)

        if not razorpay_provider:
            return request.render("ai_rti_master.payment_provider_not_found", {
                'rti': rti_sudo,
            })

        # Get available providers (including Razorpay)
        providers_sudo = request.env['payment.provider'].sudo().search([
            ('state', 'in', ['enabled', 'test']),  # Include test mode providers
            ('company_id', 'in', [False, request.env.company.id]),
        ])

        # Get saved payment methods (tokens)
        payment_tokens = request.env['payment.token'].sudo().search([
            ('partner_id', '=', request.env.user.partner_id.id),
            ('provider_id', 'in', providers_sudo.ids),
            ('active', '=', True),
        ])

        # Group tokens by provider
        tokens_by_provider = {}
        for provider in providers_sudo:
            provider_tokens = payment_tokens.filtered(lambda token: token.provider_id.id == provider.id)
            tokens_by_provider[provider] = provider_tokens

        # Prepare reference with prefix
        reference = f'RTI-{rti_sudo.id}-{fields.Datetime.now().strftime("%Y%m%d%H%M%S")}'

        # Get payment method for Razorpay
        payment_method = request.env['payment.method'].sudo().search([
            ('code', '=', 'razorpay'),
            ('provider_ids', 'in', razorpay_provider.id)
        ], limit=1)

        # If no specific payment method found, get any method for this provider
        if not payment_method:
            payment_method = request.env['payment.method'].sudo().search([
                ('provider_ids', 'in', razorpay_provider.id)
            ], limit=1)

        # Create transaction for payment session
        tx_values = {
            'reference': reference,
            'provider_id': razorpay_provider.id,
            'amount': amount,
            'currency_id': request.env.company.currency_id.id,
            'partner_id': request.env.user.partner_id.id,
            'operation': 'online_direct',  # For direct online payment
            'landing_route': '/my/rti',  # Where to redirect after payment
        }

        # Add payment_method_id if available
        if payment_method:
            tx_values['payment_method_id'] = payment_method.id

        # Try to create transaction
        try:
            tx_sudo = request.env['payment.transaction'].sudo().create(tx_values)
        except Exception as e:
            _logger.error(f"Error creating transaction: {str(e)}")
            # Fallback: try to get default payment method
            default_method = request.env['payment.method'].sudo().search([], limit=1)
            if default_method:
                tx_values['payment_method_id'] = default_method.id
                tx_sudo = request.env['payment.transaction'].sudo().create(tx_values)
            else:
                return request.render("ai_rti_master.payment_error", {
                    'error': "Could not create payment transaction. Please contact support."
                })

        # Get Razorpay key from provider
        razorpay_key = razorpay_provider.razorpay_key if hasattr(razorpay_provider, 'razorpay_key') else ''
        if not razorpay_key and hasattr(razorpay_provider, 'get_base_url'):
            # For test mode, use test key
            razorpay_key = 'rzp_test_1DP5mmOlF5G5ag'

        # Render custom Razorpay payment page
        return request.render("ai_rti_master.razorpay_payment", {
            'rti': rti_sudo,
            'amount': amount,
            'amount_in_paise': int(amount * 100),  # Razorpay expects amount in paise
            'razorpay_key': razorpay_key,
            'tx_id': tx_sudo.id
        })

    @http.route(['/payment/rti/confirm/<int:rti_id>/<int:tx_id>'], type='http', auth='public', website=True)
    def payment_confirmation(self, rti_id, tx_id, **post):
        try:
            rti_sudo = self._document_check_access('ai_rti_master', rti_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        tx_sudo = request.env['payment.transaction'].sudo().browse(tx_id)

        if not tx_sudo or tx_sudo.rti_id != rti_id:
            raise werkzeug.exceptions.NotFound()

        if tx_sudo.state == 'done':
            rti_sudo.write({
                'payment_status': 'paid',
                'payment_transaction_id': tx_sudo.id,
            })

            # Get or create payment record
            payment = request.env['rti.payment'].sudo().search([('transaction_id', '=', tx_sudo.reference)], limit=1)
            if not payment:
                # Create payment record if it doesn't exist
                payment = request.env['rti.payment'].sudo().create({
                    'reference': tx_sudo.reference,
                    'rti_id': rti_id,
                    'amount': tx_sudo.amount,
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'transaction_id': tx_sudo.reference,
                    'payment_method': 'razorpay'
                })
            else:
                payment.write({
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'payment_method': 'razorpay'
                })

            # Return success page
            return request.render("ai_rti_master.payment_success", {
                'payment': payment,
                'rti': rti_sudo
            })
        elif tx_sudo.state == 'pending':
            return request.redirect(f'/my/rti/{rti_id}?payment_pending=true')
        else:
            return request.redirect(f'/my/rti/{rti_id}?error=payment_failed')

    @http.route(['/my/rti/create'], type='http', auth="user", website=True)
    def portal_create_rti(self, **kw):
        values = {
            'page_name': 'create_rti',
            'states': request.env['x_rti_state'].search([]),
            'addon_types': request.env['product.template'].sudo().search([('is_rti_addon', '=', True)]),
        }
        return request.render("ai_rti_master.portal_create_rti", values)

    @http.route(['/my/rti/submit'], type='http', auth="user", website=True)
    def portal_submit_rti(self, **post):
            # Create RTI Application
        vals = {
            'x_state': int(post.get('x_state')),
            'x_language': int(post.get('x_language')),
            'x_department_category': post.get('x_department_category'),
            'x_department_level': post.get('x_department_level'),
            'x_department_ids': [(6, 0, [int(x) for x in post.getlist('x_department_ids[]')])],
            'x_first_name': post.get('x_first_name'),
            'x_middle_name': post.get('x_middle_name'),
            'x_surname': post.get('x_surname'),
            'x_mobile_number': post.get('x_mobile_number'),
            'x_email': post.get('x_email'),
            'x_street': post.get('x_street'),
            'x_flat_no': post.get('x_flat_no') or '',  # For backward compatibility
            'x_landmark': post.get('x_landmark'),
            'x_pincode': post.get('x_pincode'),
            'x_rti_details': post.get('x_rti_details'),
            'addon_ids': [(6, 0, [int(x) for x in post.getlist('addon_ids[]')])],
            'state': 'submitted',
        }
        rti = request.env['ai_rti_master'].sudo().create(vals)
        return request.redirect('/my/rti/%s' % rti.id)



    @http.route(['/my/rti/<int:rti_id>/pdf'], type='http', auth="user", website=True)
    def portal_get_rti_pdf(self, rti_id, **kw):
        try:
            rti_sudo = request.env['ai_rti_master'].sudo().browse(rti_id)
            if not rti_sudo:
                return request.redirect('/my/rti')

            # Allow PDF download if payment is completed
            if rti_sudo.payment_status != 'paid':
                return request.redirect('/my/rti/%s' % rti_id)

            # Use Odoo's default report download URL
            report_action = request.env.ref('ai_rti_master.action_report_rti')
            if not report_action:
                return request.redirect('/my/rti/%s?error=report_not_found' % rti_id)

            # Redirect to the built-in Odoo report controller
            return werkzeug.utils.redirect('/report/pdf/%s/%s' % (report_action.report_name, rti_id))

        except Exception as e:
            _logger.error(f"Error generating RTI PDF for ID {rti_id}: {str(e)}")
            return request.redirect('/my/rti/%s?error=pdf_generation_failed' % rti_id)

    @http.route(['/rti/state/<int:state_id>/languages'], type='json', auth="public", website=True)
    def get_state_languages(self, state_id, **kw):
        state = request.env['x_rti_state'].sudo().browse(state_id)
        languages = []
        for lang in state.language_ids:
            languages.append({
                'id': lang.id,
                'name': lang.name,
                'code': lang.code,
                'default': state.default_language_id.id == lang.id
            })
        return languages

    @http.route(['/rti/departments'], type='json', auth="public", website=True)
    def get_departments(self, state_id, category, level, **kw):
        domain = [
            ('state_id', '=', int(state_id)),
            ('category', '=', category),
            ('level', '=', level)
        ]
        departments = request.env['rti.department'].sudo().search_read(domain, ['id', 'name'])
        return departments

    @http.route(['/rti/payment/verify'], type='http', auth="public", website=True, methods=['POST'])
    def payment_verify(self, **post):
        try:
            # Get payment details from post
            razorpay_payment_id = post.get('razorpay_payment_id')
            rti_id = int(post.get('rti_id'))
            tx_id = int(post.get('tx_id'))

            if not razorpay_payment_id or not rti_id or not tx_id:
                return request.render("ai_rti_master.payment_error", {
                    'error': "Missing payment information"
                })

            # Get RTI and transaction
            rti = request.env['ai_rti_master'].sudo().browse(rti_id)
            tx = request.env['payment.transaction'].sudo().browse(tx_id)

            if not rti.exists() or not tx.exists():
                return request.render("ai_rti_master.payment_error", {
                    'error': "Invalid RTI or transaction"
                })

            # Update transaction with Razorpay payment ID
            tx.sudo().write({
                'provider_reference': razorpay_payment_id,
                'state': 'done'
            })

            # Get or create payment record
            payment = request.env['rti.payment'].sudo().search([('transaction_id', '=', tx.reference)], limit=1)
            if payment:
                payment.write({
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'payment_method': 'razorpay'
                })
            else:
                # Create payment record if it doesn't exist
                payment = request.env['rti.payment'].sudo().create({
                    'reference': tx.reference,
                    'rti_id': rti.id,
                    'amount': tx.amount,
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'transaction_id': tx.reference,
                    'payment_method': 'razorpay'
                })

            # Update RTI payment status
            rti.sudo().write({
                'payment_status': 'paid',
                'payment_transaction_id': tx.id
            })

            _logger.info(f"Payment verification successful - Payment ID: {payment.id}, Amount: {payment.amount}, Date: {payment.payment_date}")

            # Return success page with complete data
            return request.render("ai_rti_master.payment_success", {
                'payment': payment,
                'rti': rti,
                'transaction': tx,
                'razorpay_payment_id': razorpay_payment_id
            })

        except Exception as e:
            _logger.error(f"Error verifying Razorpay payment: {str(e)}")
            return request.render("ai_rti_master.payment_error", {
                'error': f"Payment verification failed: {str(e)}"
            })
