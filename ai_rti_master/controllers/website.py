from odoo import http, _, fields
from odoo.http import request
from odoo.exceptions import ValidationError
import base64
import logging
import json

_logger = logging.getLogger(__name__)

class RtiWebsite(http.Controller):

    def _get_department_ids(self, form_data):
        """Helper method to extract and process department IDs from form data"""
        try:
            # Get all department IDs from the form
            dept_ids = form_data.getlist('x_department_ids')
            _logger.info(f"Raw department IDs from form: {dept_ids}")

            # Filter out empty values and convert to integers
            dept_ids = [int(dept_id) for dept_id in dept_ids if dept_id]
            _logger.info(f"Processed department IDs: {dept_ids}")

            # Return the many2many command
            return [(6, 0, dept_ids)]
        except Exception as e:
            _logger.error(f"Error processing department IDs: {str(e)}")
            # Return empty list as fallback
            return [(6, 0, [])]

    @http.route(['/rti'], type='http', auth="public", website=True)
    def rti_home(self, **kw):
        states = request.env['rti.state'].sudo().search([])
        about_content = request.env['rti.about'].sudo().search([], order='sequence')
        howitworks_content = request.env['rti.howitworks'].sudo().search([], order='sequence')
        faq_content = request.env['rti.faq'].sudo().search([], order='sequence')

        # Get the website page as main_object
        main_object = request.env['website.page'].sudo().search([('url', '=', '/rti')], limit=1)
        if not main_object:
            main_object = request.env['website.page'].sudo().create({
                'url': '/rti',
                'website_published': True,
                'name': 'RTI Home',
                'type': 'qweb',
                'arch': '<div class="oe_structure oe_empty"/>'
            })

        return request.render("ai_rti_master.rti_home", {
            'states': states,
            'about_content': about_content,
            'howitworks_content': howitworks_content,
            'faq_content': faq_content,
            'main_object': main_object,
        })

    @http.route(['/rti/apply'], type='http', auth="public", website=True)
    def rti_apply(self, **kw):
        # Get all available states
        states = request.env['rti.state'].sudo().search([])

        # Get about content
        # about_content = request.env['rti.about'].sudo().search([], order='sequence')

        # Get the website page as main_object
        main_object = request.env['website.page'].sudo().search([('url', '=', '/rti/apply')], limit=1)
        if not main_object:
            main_object = request.env['website.page'].sudo().create({
                'url': '/rti/apply',
                'website_published': True,
                'name': 'RTI Application',
                'type': 'qweb',
                'arch': '<div class="oe_structure oe_empty"/>'
            })

        return request.render("ai_rti_master.rti_apply", {
            'states': states,
            # 'about_content': about_content,
            'main_object': main_object,
        })

    @http.route(['/rti/get-languages'], type='http', auth="public", website=True)
    def rti_get_languages(self, **kw):
        state_id = kw.get('state_id')
        result = {'languages': []}

        if state_id:
            try:
                # Get state
                state = request.env['rti.state'].sudo().browse(int(state_id))
                if state.exists():
                    # Get languages for this state
                    languages = []
                    for lang in state.language_ids:
                        languages.append({
                            'id': lang.id,
                            'name': lang.name,
                            'is_default': lang.id == state.default_language_id.id if state.default_language_id else False
                        })
                    result['languages'] = languages
            except (ValueError, TypeError):
                pass

        return request.make_response(
            json.dumps(result),
            headers=[('Content-Type', 'application/json')]
        )
    # @http.route(['/rti/form'], type='http', auth="public", website=True)
    # def rti_form(self, **kw):
    #     # Get and validate state_id and lang_id
    #     state_id = kw.get('state_id')
    #     lang_id = kw.get('lang_id')
    #     if not state_id or not lang_id:
    #         return request.redirect('/rti/apply')
    #     state = request.env['rti.state'].sudo().browse(int(state_id))
    #     if not state.exists():
    #         return request.redirect('/rti/apply')

    #     # Get available addons
    #     addons = request.env['product.template'].sudo().search([
    #         ('is_rti_addon', '=', True)
    #     ])

    #     return request.render("ai_rti_master.rti_form", {
    #         'state': state,
    #         'addons': addons,
    #         'lang_id': lang_id,
    #     })

    @http.route(['/rti/form'], type='http', auth="public", website=True)
    def rti_form(self, **kw):
        # Get and validate state_id and lang_id
        state_id = kw.get('state_id')
        lang_id = kw.get('lang_id')
        if not state_id or not lang_id:
            return request.redirect('/rti/apply')

        state = request.env['rti.state'].sudo().browse(int(state_id))
        if not state.exists():
            return request.redirect('/rti/apply')

        # Get the language from res.lang model
        language = request.env['res.lang'].sudo().browse(int(lang_id))
        if not language.exists():
            language_name = 'Default Language'  # Fallback if lang_id is invalid
            language_code = 'en_US'
        else:
            language_name = language.name  # Fetch the language name based on lang_id
            language_code = language.code  # Get language code for transliteration

        # Get available addons
        addons = request.env['product.template'].sudo().search([('is_rti_addon', '=', True)])

        # Get default language for this state
        default_language_id = state.default_language_id

        return request.render("ai_rti_master.rti_form", {
            'state': state,
            'addons': addons,
            'lang_id': lang_id,
            'language_name': language_name,  # Pass language name to the template
            'language_code': language_code,  # Pass language code for transliteration
            'user_id': request.env.user,  # Always pass user object - template should handle public user check
            'default_language_id': default_language_id,  # Pass the default language object, not just the ID
        })



    # @http.route(['/rti/get_languages'], type='json', auth="public", website=True)
    # def get_languages(self, state_id):
    #     if not state_id:
    #         return []

    #     try:
    #         state = request.env['rti.state'].sudo().browse(int(state_id))
    #         if not state.exists():
    #             return []
    #         return [(l.id, l.name) for l in state.language_ids]
    #     except (ValueError, TypeError):
    #         return []

    @http.route('/rti/apply/submit', type='http', auth='public', website=True, csrf=False, methods=['POST'])
    def submit_rti_application(self, **post):
        _logger.info(f"RTI application data: {post}")
        try:
            # Get data from request based on content type
            data = post  # In Odoo, form data is always available in post

            csrf_token = request.csrf_token()
            # Validate required fields
            required_fields = ['x_state', 'x_first_name', 'x_surname', 'x_mobile_number',
                             'x_email', 'x_street', 'x_pincode']
            for field in required_fields:
                if not data.get(field):
                    return request.redirect(f'/rti/apply?error=Field {field} is required')

            # Create RTI application
            application_data = {
                'x_first_name': data.get('x_first_name'),
                'x_middle_name': data.get('x_middle_name'),
                'x_surname': data.get('x_surname'),
                'x_mobile_number': data.get('x_mobile_number'),
                'x_email': data.get('x_email'),
                'x_whatsapp_number': data.get('x_whatsapp_number'),
                'x_street': data.get('x_street'),
                'x_flat_no': data.get('x_flat_no') or '',  # For backward compatibility
                'x_landmark': data.get('x_landmark'),
                'x_pincode': data.get('x_pincode'),
                'x_state': int(data.get('x_state')),
                'x_district': int(data.get('x_district')) if data.get('x_district') else None,
                'x_taluka': int(data.get('x_taluka')) if data.get('x_taluka') else None,
                'x_village': int(data.get('x_village')) if data.get('x_village') else None,
                # Handle department IDs - this is a many2many field
                'x_department_ids': self._get_department_ids(request.httprequest.form),
                'x_rti_details': data.get('x_rti_details'),
                'rti_state': int(data.get('rti_state')) if data.get('rti_state') else None,
                'rti_district': int(data.get('rti_district')) if data.get('rti_district') else None,
                'rti_taluka': int(data.get('rti_taluka')) if data.get('rti_taluka') else None,
                'rti_village': int(data.get('rti_village')) if data.get('rti_village') else None,
                'x_language': int(data.get('lang_id')) if data.get('lang_id') else None,
                'state': 'draft'
            }

            # Handle manual department if checked
            if data.get('x_manual_department'):
                application_data.update({
                    'x_manual_department': True,
                    'x_manual_department_name': data.get('x_manual_department_name'),
                    # 'x_manual_department_category_id': int(data.get('x_manual_department_category_id')) if data.get('x_manual_department_category_id') else None,
                    # 'x_manual_department_level': data.get('x_manual_department_level'),
                    'x_manual_department_address': data.get('x_manual_department_address'),
                })
            _logger.info(f"RTI application data: {application_data}")
            # Create RTI application
            rti = request.env['ai_rti_master'].sudo().create(application_data)
            _logger.info(f"RTI application created: {rti}")

            # Redirect to success page with RTI number prominently displayed
            return request.redirect(f'/rti/success/{rti.id}')

        except Exception as e:
            _logger.error(f"Error submitting RTI application: {str(e)}")
            return request.redirect(f'/rti/apply?error={str(e)}')

    # @http.route(['/rti/districts'], type='json', auth="public", website=True)
    # def get_districts(self, state_id):
    #     districts = request.env['location.district'].sudo().search([('state_id', '=', int(state_id))])
    #     return [(d.id, d.name) for d in districts]

    # @http.route(['/rti/talukas'], type='json', auth="public", website=True)
    # def get_talukas(self, district_id):
    #     talukas = request.env['location.taluka'].sudo().search([('district_id', '=', int(district_id))])
    #     return [(t.id, t.name) for t in talukas]

    # @http.route(['/rti/villages'], type='json', auth="public", website=True)
    # def get_villages(self, taluka_id):
    #     villages = request.env['location.village'].sudo().search([('taluka_id', '=', int(taluka_id))])
    #     return [(v.id, v.name) for v in villages]

    @http.route(['/rti/districts'], type='json', auth="public", website=True)
    def get_districts(self, state_id):
        # Fetch districts based on state_id
        districts = request.env['location.district'].sudo().search([('x_state_id', '=', int(state_id))])
        return [{
            'id': d.id,
            'name': d.name,
            'state_name': d.x_state_id.name if d.x_state_id else 'No State'  # Add state name to the response
        } for d in districts]

    @http.route(['/rti/talukas'], type='json', auth="public", website=True)
    def get_talukas(self, district_id):
        # Fetch talukas based on district_id
        talukas = request.env['location.taluka'].sudo().search([('x_district_id', '=', int(district_id))])
        return [{
            'id': t.id,
            'name': t.name,
            'district_name': t.x_district_id.name if t.x_district_id else 'No District'  # Add district name to the response
        } for t in talukas]

    @http.route(['/rti/villages'], type='json', auth="public", website=True)
    def get_villages(self, taluka_id):
        # Fetch villages based on taluka_id
        villages = request.env['location.village'].sudo().search([('x_taluka_id', '=', int(taluka_id))])
        return [{
            'id': v.id,
            'name': v.name,
            'taluka_name': v.x_taluka_id.name if v.x_taluka_id else 'No Taluka'  # Add taluka name to the response
        } for v in villages]

    @http.route(['/rti/change_language'], type='json', auth="public", website=True)
    def change_language(self, state_id, lang_code):
        if lang_code:
            lang = request.env['res.lang'].sudo().search([('code', '=', lang_code)], limit=1)
            if lang:
                # Get the current website
                website = request.env['website'].get_current_website()
                # Ensure the language is enabled for the website
                if lang not in website.language_ids:
                    website.write({'language_ids': [(4, lang.id)]})
                # Update the context with the new language
                request.context = dict(request.context, lang=lang_code)
                return {
                    'url': f'/rti/apply?state_id={state_id}&lang={lang_code}'
                }
        return {}

    @http.route(['/rti/departments'], type='json', auth="public", website=True)
    def get_departments(self, **post):
        try:
            # Extract parameters from the post data
            params = post.get('params', {})
            state_id = params.get('state_id')
            lang_id = params.get('lang_id')  # Make sure you get lang_id if needed for filtering

            _logger.info(f"Loading departments for state_id: {state_id}, lang_id: {lang_id}")

            # Debug: Check if any departments exist at all
            all_departments = request.env['rti.department'].sudo().search([])
            _logger.info(f"Total departments in database: {len(all_departments)}")

            # Debug: Check if any rti.state records exist
            all_states = request.env['rti.state'].sudo().search([])
            _logger.info(f"Total RTI states in database: {len(all_states)}")
            for state in all_states:
                _logger.info(f"RTI State: {state.id} - {state.name}")

            # Prepare the domain for filtering
            domain = []

            # Add filter for state_id if provided
            if state_id:
                # First try to find rti.state record
                state = request.env['rti.state'].sudo().search([('id', '=', int(state_id))], limit=1)
                if state:
                    domain.append(('x_state_id', '=', state.id))
                    _logger.info(f"Found RTI state: {state.name}, adding to domain: {domain}")
                else:
                    # If not found, try to find by res.country.state and map to rti.state
                    country_state = request.env['res.country.state'].sudo().search([('id', '=', int(state_id))], limit=1)
                    if country_state:
                        # Try to find corresponding rti.state by name or code
                        rti_state = request.env['rti.state'].sudo().search([
                            '|', ('name', 'ilike', country_state.name), ('code', '=', country_state.code)
                        ], limit=1)
                        if rti_state:
                            domain.append(('x_state_id', '=', rti_state.id))
                            _logger.info(f"Found mapped RTI state: {rti_state.name}, adding to domain: {domain}")
                        else:
                            _logger.warning(f"No RTI state found for country state: {country_state.name}")
                    else:
                        _logger.warning(f"State with ID {state_id} not found in either rti.state or res.country.state")

            # Fetch departments based on the domain filter
            _logger.info(f"Searching for departments with domain: {domain}")
            departments = request.env['rti.department'].sudo().search_read(
                domain,
                ['id', 'name'],  # Only return the 'id' and 'name' of the department
                order='name asc'
            )

            _logger.info(f"Found {len(departments)} departments")

            # If no departments found with state filter, try without state filter as fallback
            if not departments and domain:
                _logger.info("No departments found with state filter, trying without state filter")
                departments = request.env['rti.department'].sudo().search_read(
                    [],  # No domain filter
                    ['id', 'name'],
                    order='name asc'
                )
                _logger.info(f"Found {len(departments)} departments without state filter")

            return {'result': departments}

        except Exception as e:
            _logger.error(f"Error fetching departments: {str(e)}", exc_info=True)
            return {'error': {'data': {'message': str(e)}}}

    @http.route(['/rti/department/categories'], type='json', auth="public", website=True)
    def get_department_categories(self, **post):
        try:
            state_id = post.get('params', {}).get('state_id')

            domain = []
            state = request.env['rti.state'].sudo().search([('id', '=', int(state_id))], limit=1)
            if state_id:
                domain.append(('state_id', '=', state.id))  # updated field for filtering by state

            categories = request.env['rti.department.category'].sudo().search_read(
                domain,
                ['id', 'name', 'x_active'],  # updated the fields accordingly
                order='name asc'
            )

            return categories

        except Exception as e:
            _logger.error("Error fetching department categories: %s", str(e))
            return {'error': str(e)}

    @http.route(['/rti/get_default_language/<int:state_id>'], type='http', auth="public", website=True)
    def get_default_language(self, state_id, **kw):
        try:
            state = request.env['rti.state'].sudo().browse(int(state_id))
            if not state.exists():
                return json.dumps({'error': 'State not found'})

            return json.dumps({
                'default_language_id': state.default_language_id.id if state.default_language_id else None
            })
        except Exception as e:
            return json.dumps({'error': str(e)})

    @http.route(['/rti/get_partner_data'], type='json', auth="user", website=True)
    def get_partner_data(self, **kw):
        """Fetch current user's partner data to pre-fill the RTI form"""
        try:
            if request.env.user._is_public():
                return {}

            partner = request.env.user.partner_id
            if not partner:
                return {}

            # Get previous RTI applications for this partner if any
            previous_rti = request.env['ai_rti_master'].sudo().search(
                [('partner_id', '=', partner.id)], limit=1, order='create_date desc'
            )

            # Return partner data in a format suitable for the form
            result = {
                'name': partner.name,
                'email': partner.email,
                'phone': partner.phone,
                'mobile': partner.mobile,
                'street': partner.street,  # For flat/house number
                'street2': partner.street2,  # For street/area
                'city': partner.city,
                # 'state_id': partner.state_id.id if partner.state_id else False,
                'zip': partner.zip,  # For pincode
                'country_id': partner.country_id.id if partner.country_id else False
            }

            # If there's a previous RTI application, add its data
            if previous_rti:
                # Add any additional fields from previous RTI that might be useful
                if not result.get('street') and previous_rti.x_street:
                    result['street'] = previous_rti.x_street

                if not result.get('zip') and previous_rti.x_pincode:
                    result['zip'] = previous_rti.x_pincode

                # if not result.get('state_id') and previous_rti.x_state:
                    # result['state_id'] = previous_rti.x_state.id

            return result
        except Exception as e:
            _logger.error(f"Error fetching partner data: {str(e)}")
            return {'error': str(e)}

    @http.route(['/rti/success/<int:rti_id>'], type='http', auth="public", website=True)
    def rti_success(self, rti_id, **kw):
        try:
            rti = request.env['ai_rti_master'].sudo().browse(rti_id)
            if not rti.exists():
                return request.not_found()

            # Get available addons
            addons = request.env['product.template'].sudo().search([
                ('is_rti_addon', '=', True)
            ])

            return request.render("ai_rti_master.rti_success_template", {
                'rti': rti,
                'addons': addons,
                'page_name': 'rti_success'
            })
        except Exception as e:
            _logger.error("Error in rti_success: %s", str(e))
            return request.not_found()

    @http.route(['/rti/payment/<int:rti_id>'], type='http', auth="public", website=True, methods=['POST', 'GET'])
    def rti_payment(self, rti_id, **post):
        try:
            if not request.env.user._is_public():
                rti = request.env['ai_rti_master'].sudo().browse(rti_id)
                if not rti.exists():
                    return request.not_found()

                # Check if the RTI belongs to the current user
                if not rti.partner_id:
                    rti.write({'partner_id': request.env.user.partner_id.id})
                elif rti.partner_id != request.env.user.partner_id:
                    return request.not_found()

                # Get selected addons and calculate total
                selected_addons = []
                addon_quantities = []
                total_amount = 0

                # Handle delivery option (radio button)
                if 'delivery_option' in post and post['delivery_option']:
                    delivery_addon_id = int(post['delivery_option'])
                    delivery_addon = request.env['product.template'].sudo().browse(delivery_addon_id)
                    if delivery_addon.exists():
                        quantity = 1
                        if delivery_addon.quantity_per_dept:
                            qty_key = f'qty_{delivery_addon_id}'
                            quantity = int(post.get(qty_key, 1))

                        selected_addons.append(delivery_addon)
                        addon_quantities.append((delivery_addon, quantity))
                        total_amount += delivery_addon.cost * quantity

                # Handle additional services (checkboxes)
                for key, value in post.items():
                    if key.startswith('addon_'):
                        addon_id = int(key.split('_')[1])
                        addon = request.env['product.template'].sudo().browse(addon_id)
                        if addon.exists():
                            quantity = 1
                            if addon.quantity_per_dept:
                                qty_key = f'qty_{addon_id}'
                                quantity = int(post.get(qty_key, 1))

                            selected_addons.append(addon)
                            addon_quantities.append((addon, quantity))
                            total_amount += addon.cost * quantity

                # Check if at least one delivery option is selected
                if 'delivery_option' not in post or not post['delivery_option']:
                    return request.redirect(f'/rti/success/{rti_id}?error=Please select a delivery option')

                if not selected_addons:
                    return request.redirect(f'/rti/success/{rti_id}?error=Please select at least one option')

                # Create payment record
                payment_vals = {
                    'rti_id': rti.id,
                    'amount': total_amount,
                    'addon_ids': [(6, 0, [addon.id for addon in selected_addons])],
                    'state': 'draft'
                }

                payment = request.env['rti.payment'].sudo().create(payment_vals)

                # Create addon quantities
                for addon, quantity in addon_quantities:
                    request.env['rti.addon.quantity'].sudo().create({
                        'rti_id': rti.id,
                        'addon_id': addon.id,
                        'quantity': quantity
                    })

                # Get Razorpay provider
                provider = request.env['payment.provider'].sudo()._get_compatible_providers(
                    partner_id=request.env.user.partner_id.id,
                    amount=total_amount,
                    currency_id=request.env.company.currency_id.id,
                ).filtered(lambda p: p.code == 'razorpay')

                if not provider:
                    return request.render("ai_rti_master.payment_provider_not_found", {
                        'rti': rti
                    })

                # Create Razorpay transaction
                tx_values = {
                    'provider_id': provider.id,
                    'amount': total_amount,
                    'currency_id': request.env.company.currency_id.id,
                    'partner_id': request.env.user.partner_id.id,
                    'reference': f'RTI-{rti.name}',
                }

                tx = request.env['payment.transaction'].sudo().create(tx_values)
                payment.write({'transaction_id': tx.reference})

                # Prepare Razorpay payment data
                payment_data = {
                    'reference': tx.reference,
                    'partner_id': request.env.user.partner_id.id,
                    'amount': total_amount,
                    'currency_id': request.env.company.currency_id.id,
                    'access_token': tx.access_token,
                }

                # Get Razorpay key from provider
                razorpay_key = provider.razorpay_key if hasattr(provider, 'razorpay_key') else ''
                if not razorpay_key and hasattr(provider, 'get_base_url'):
                    # For test mode, use test key
                    razorpay_key = 'rzp_test_1DP5mmOlF5G5ag'

                # Render custom Razorpay payment page
                return request.render("ai_rti_master.razorpay_payment", {
                    'rti': rti,
                    'amount': total_amount,
                    'amount_in_paise': int(total_amount * 100),  # Razorpay expects amount in paise
                    'razorpay_key': razorpay_key,
                    'tx_id': tx.id
                })
            else:
                return request.redirect('/web/login?redirect=/my/rti')

        except Exception as e:
            _logger.error("Error in rti_payment: %s", str(e))
            return request.redirect(f'/my/rti/{rti_id}?error=payment_failed')

    @http.route(['/rti/transliterate'], type='json', auth="public", website=True)
    def transliterate_text(self, **post):
        """Transliterate text from English to the target language"""
        try:
            # Get parameters
            params = post.get('params', {})
            text = params.get('text', '')
            target_lang = params.get('target_lang', 'en')

            if not text or target_lang == 'en':
                return {'result': text}

            # Import requests for API call
            import requests

            # Map language codes to AI4Bharat format
            lang_map = {
                'hi': 'hi',  # Hindi
                'gu': 'gu',  # Gujarati
                'en': 'en'   # English (no transliteration)
            }

            # Get the mapped language code
            ai4bharat_lang = lang_map.get(target_lang, target_lang)

            # Call AI4Bharat API for transliteration
            try:
                # Use the correct API endpoint for AI4Bharat
                response = requests.post(
                    "https://api.ai4bharat.org/v1/transliterate",
                    json={
                        "input": [{
                            "source": text
                        }],
                        "config": {
                            "sourceLanguage": "en",
                            "targetLanguage": ai4bharat_lang,
                            "isSentence": False
                        }
                    },
                    timeout=5  # Set a timeout to avoid long waits
                )
                _logger.info(f"API request sent to AI4Bharat: {text} -> {ai4bharat_lang}")

                # For production use with API key:
                # response = requests.post(
                #     "https://api.ai4bharat.org/v1/transliterate",
                #     headers={
                #         "Authorization": "Bearer YOUR_API_KEY_HERE"
                #     },
                #     json={
                #         "input": [{
                #             "source": text
                #         }],
                #         "config": {
                #             "sourceLanguage": "en",
                #             "targetLanguage": ai4bharat_lang,
                #             "isSentence": False
                #         }
                #     },
                #     timeout=5
                # )

                if response.status_code == 200:
                    result_data = response.json()
                    _logger.info(f"AI4Bharat API response: {result_data}")

                    # The API returns data in a specific format
                    if 'output' in result_data:
                        # Extract the transliterated text from the output
                        transliterated = result_data['output'][0]['target']
                        _logger.info(f"Transliterated text: {transliterated}")
                        return {'result': transliterated}
                    elif 'result' in result_data:
                        return {'result': result_data['result']}

                # Fallback to original text if API call fails
                _logger.warning(f"AI4Bharat API returned non-200 status code: {response.status_code}")
                return {'result': text, 'status': 'api_error'}

            except requests.RequestException as req_err:
                _logger.warning(f"Error calling AI4Bharat API: {str(req_err)}")
                return {'result': text, 'status': 'connection_error'}

        except Exception as e:
            _logger.error(f"Error in transliteration: {str(e)}")
            return {'error': str(e), 'result': text}

    @http.route(['/rti/get_id_from_number'], type='json', auth="public", website=True)
    def get_id_from_number(self, **post):
        """Get RTI ID from RTI number"""
        try:
            params = post.get('params', {})
            rti_number = params.get('rti_number')

            if not rti_number:
                return {'error': 'RTI number is required'}

            # Search for the RTI by name (which is the RTI number)
            rti = request.env['ai_rti_master'].sudo().search([('name', '=', rti_number)], limit=1)

            if not rti:
                return {'error': 'RTI not found'}

            return {'id': rti.id, 'name': rti.name}

        except Exception as e:
            _logger.error(f"Error getting RTI ID from number: {str(e)}")
            return {'error': str(e)}

    # @http.route(['/rti/payment/verify'], type='http', auth="public", website=True, methods=['POST'])  # Disabled - using portal.py version
    def payment_verify(self, **post):
        try:
            # Get payment details from post
            razorpay_payment_id = post.get('razorpay_payment_id')
            rti_id = int(post.get('rti_id'))
            tx_id = int(post.get('tx_id'))

            if not razorpay_payment_id or not rti_id or not tx_id:
                return request.render("ai_rti_master.payment_error", {
                    'error': "Missing payment information"
                })

            # Get RTI and transaction
            rti = request.env['ai_rti_master'].sudo().browse(rti_id)
            tx = request.env['payment.transaction'].sudo().browse(tx_id)

            if not rti.exists() or not tx.exists():
                return request.render("ai_rti_master.payment_error", {
                    'error': "Invalid RTI or transaction"
                })

            # Update transaction with Razorpay payment ID
            tx.sudo().write({
                'provider_reference': razorpay_payment_id,
                'state': 'done'
            })

            # Get or create payment record
            payment = request.env['rti.payment'].sudo().search([('transaction_id', '=', tx.reference)], limit=1)
            if not payment:
                # If payment record doesn't exist, create one
                _logger.info(f"Creating new payment record for transaction: {tx.reference}")
                payment = request.env['rti.payment'].sudo().create({
                    'rti_id': rti.id,
                    'amount': tx.amount,
                    'transaction_id': tx.reference,
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'payment_method': 'razorpay'
                })
            else:
                # Update existing payment record
                _logger.info(f"Updating existing payment record: {payment.id}")
                payment.write({
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'payment_method': 'razorpay'
                })

            # Update RTI payment status
            rti.sudo().write({
                'payment_status': 'paid',
                'payment_transaction_id': tx.id
            })

            _logger.info(f"Payment verification successful - Payment ID: {payment.id}, Amount: {payment.amount}, Date: {payment.payment_date}")

            # Return success page with proper data
            return request.render("ai_rti_master.payment_success", {
                'payment': payment,
                'rti': rti,
                'transaction': tx,
                'razorpay_payment_id': razorpay_payment_id
            })

        except Exception as e:
            _logger.error(f"Error verifying Razorpay payment: {str(e)}")
            return request.render("ai_rti_master.payment_error", {
                'error': f"Payment verification failed: {str(e)}"
            })

    @http.route(['/rti/payment/confirm'], type='http', auth="public", website=True)
    def payment_confirm(self, **post):
        tx_id = request.session.get('__payment_monitored_tx_ids__', [])[-1]
        if not tx_id:
            return request.redirect('/my/rti')

        tx = request.env['payment.transaction'].sudo().browse(tx_id)
        if not tx.exists():
            return request.redirect('/my/rti')

        if tx.state == 'done':
            # Update RTI payment status
            payment = request.env['rti.payment'].sudo().search([('transaction_id', '=', tx.reference)], limit=1)
            if payment:
                payment.write({
                    'state': 'paid',
                    'payment_date': fields.Datetime.now(),
                    'payment_method': 'razorpay'
                })
                return request.render("ai_rti_master.payment_success", {
                    'payment': payment,
                    'rti': payment.rti_id
                })
        else:
            return request.render("ai_rti_master.payment_error", {
                'error': _("Payment failed. Please try again.")
            })
