<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- RTI Home Page -->
    <template id="rti_home" name="RTI Home">
        <t t-call="website.layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <div id="wrap" class="oe_structure oe_empty">
                <section class="s_text_block pt40 pb40 o_colored_level" data-snippet="s_text_block" data-name="Text" style="background-image: none;">
                    <div class="s_allow_columns container">
                        <h3 style="text-align: center;">RTI Application Services</h3>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-body">
                                        <h4>File RTI Application</h4>
                                        <p>Get information from government departments through Right to Information Act</p>
                                        <a href="/rti/apply" class="btn btn-primary">Apply Now</a>
                                        <div>
                                            <h2 class="text-center mb-4">Right to Information (RTI) Application</h2>
                                                <div class="row">
                                                    <div class="col-lg-6 mb-4 ml-40">
                                                        <div class="card h-100" style="margin-left:32px;">
                                                            <div class="card-body">
                                                                <h3 class="card-title mb-4">About RTI</h3>
                                                                <t t-foreach="about_content" t-as="about">
                                                                    <h4><t t-esc="about.name"/></h4>
                                                                    <p><t t-esc="about.about"/></p>
                                                                </t>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>

    <!-- RTI Application Form -->
    <template id="rti_apply" name="RTI Application">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container">
                    <h2 class="text-center mb-4">Right to Information (RTI) Application</h2>
                    <div class="row">
                        <div class="col-lg-12 mx-auto">
                            <div class="card">
                                <div class="card-body">
                                    <h2 class="text-center mb-4">Right to Information (RTI) Application</h2>
                                    <div class="row">
                                        <!-- State Selection Section -->
                                        <div class="col-lg-12 mb-4" id="stateSelectionSection">
                                            <div class="card h-100">
                                               <div class="card-body">
                                                <h3 class="card-title mb-4">Select State</h3>
                                                <p>Choose your state to proceed with RTI application:</p>

                                                <!-- Search Input -->
                                                <input type="text" id="stateSearch" class="form-control mb-3" placeholder="Search for a state..." />

                                                <div class="row mt-3" id="statesContainer">
                                                    <t t-foreach="states" t-as="state">
                                                        <div class="col-md-4 mb-3 state-item" t-att-data-state-name="state.name">
                                                            <div class="card h-120">
                                                                <div class="card-body text-center">
                                                                    <h5 class="card-title"><t t-esc="state.name"/></h5>
                                                                    <t t-if="state.id == selected_state_id">
                                                                        <span class="badge bg-primary">Selected</span>
                                                                    </t>
                                                                    <t t-else="">
                                                                        <button class="btn btn-primary select-state-btn" t-att-data-state-id="state.id">Select</button>
                                                                    </t>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>

                                            <script>
                                               document.addEventListener("DOMContentLoaded", function() {
                                                // Clear previous selection when page loads/refreshes
                                                localStorage.removeItem("selected_state_id");

                                                // Reference to language container that we'll update
                                                const languageContainer = document.querySelector(".language-selection .row");
                                                const languageCardTitle = document.querySelector(".language-selection").closest(".card").querySelector(".card-title");
                                                const languageCardDescription = document.querySelector(".language-selection").previousElementSibling;
                                                const stateSelectionSection = document.getElementById("stateSelectionSection");
                                                const languageSelectionSection = document.getElementById("languageSelectionSection");

                                                // Initially hide the language section until a state is selected
                                                if (!document.querySelector(".state-item .badge.bg-primary")) {
                                                    languageSelectionSection.style.display = "none";
                                                }

                                                // Search function for states
                                                document.getElementById("stateSearch").addEventListener("input", function() {
                                                    let searchText = this.value.toLowerCase();
                                                    document.querySelectorAll(".state-item").forEach(item => {
                                                        let stateName = item.getAttribute("data-state-name").toLowerCase();
                                                        item.style.display = stateName.includes(searchText) ? "block" : "none";
                                                    });
                                                });

                                                // State selection handling
                                                document.querySelectorAll(".select-state-btn").forEach(button => {
                                                    button.addEventListener("click", function() {
                                                        // Get state data
                                                        let selectedStateId = this.getAttribute("data-state-id");
                                                        let selectedStateName = this.closest(".card-body").querySelector(".card-title").textContent;

                                                        // Update UI for states
                                                        document.querySelectorAll(".badge.bg-primary").forEach(badge => badge.remove()); // Remove existing "Selected" badges
                                                        document.querySelectorAll(".select-state-btn").forEach(btn => btn.style.display = "inline-block"); // Show all buttons

                                                        this.style.display = "none"; // Hide selected button

                                                        let badge = document.createElement("span");
                                                        badge.classList.add("badge", "bg-primary");
                                                        badge.innerText = "Selected";

                                                        this.parentNode.appendChild(badge); // Add badge to selected state

                                                        // Hide state section and show language section
                                                        stateSelectionSection.style.display = "none";
                                                        languageSelectionSection.style.display = "block";

                                                        // Update language section title with selected state
                                                        if (languageCardTitle) {
                                                            languageCardTitle.innerHTML = "Select Language for " + selectedStateName;
                                                        }
                                                        if (languageCardDescription) {
                                                            languageCardDescription.innerHTML = "Choose your preferred language for " + selectedStateName + " RTI application:";
                                                        }

                                                        // Store selected state name for later use
                                                        sessionStorage.setItem("selected_state_name", selectedStateName);

                                                        // Fetch languages for the selected state via AJAX
                                                        fetch("/rti/get-languages?state_id=" + selectedStateId, {
                                                            method: 'GET',
                                                            headers: {
                                                                'X-Requested-With': 'XMLHttpRequest',
                                                                'Accept': 'application/json'
                                                            }
                                                        })
                                                        .then(response => response.json())
                                                        .then(data => {
                                                            // Clear current languages
                                                            languageContainer.innerHTML = '';

                                                            // Track the default language ID if there is one
                                                            let defaultLanguageId = null;

                                                            // Add new language options
                                                            data.languages.forEach(function(language) {
                                                                const langDiv = document.createElement('div');
                                                                langDiv.className = 'col-md-3 mb-2';

                                                                const formCheck = document.createElement('div');
                                                                formCheck.className = 'form-check';

                                                                // Create input element
                                                                const input = document.createElement('input');
                                                                input.className = 'form-check-input';
                                                                input.type = 'radio';
                                                                input.name = 'lang_id';
                                                                input.value = language.id;
                                                                input.id = 'lang_' + language.id;
                                                                if (language.is_default) {
                                                                    input.checked = true;
                                                                    defaultLanguageId = language.id;
                                                                }

                                                                // Create label element
                                                                const label = document.createElement('label');
                                                                label.className = 'form-check-label';
                                                                label.htmlFor = 'lang_' + language.id;
                                                                label.textContent = language.name;

                                                                // Add default badge if needed
                                                                if (language.is_default) {
                                                                    const badge = document.createElement('span');
                                                                    badge.className = 'badge bg-primary ms-1';
                                                                    badge.textContent = 'Default';
                                                                    label.appendChild(badge);
                                                                }

                                                                // Assemble the elements
                                                                formCheck.appendChild(input);
                                                                formCheck.appendChild(label);
                                                                langDiv.appendChild(formCheck);
                                                                languageContainer.appendChild(langDiv);
                                                            });

                                                            // Add event listeners to new radio buttons
                                                            document.querySelectorAll('input[name="lang_id"]').forEach(radio => {
                                                                radio.addEventListener('change', function() {
                                                                    handleLanguageSelection(this.value, selectedStateId);
                                                                });
                                                            });

                                                            // Immediately handle the default language if one exists
                                                            if (defaultLanguageId) {
                                                                // Find the radio button for the default language
                                                                const defaultRadio = document.querySelector('input[name="lang_id"][value="' + defaultLanguageId + '"]');
                                                                if (defaultRadio) {
                                                                    // Programmatically mark it as selected in our UI (with badge)
                                                                    handleLanguageSelection(defaultLanguageId, selectedStateId);
                                                                }
                                                            }

                                                            // Initialize the Change State button
                                                            initChangeStateButton();
                                                        })
                                                        .catch(error => {
                                                            console.error('Error fetching languages:', error);
                                                            languageContainer.innerHTML = '<div class="col-12"><div class="alert alert-danger">Error loading languages. Please try again.</div></div>';
                                                        });
                                                    });
                                                });

                                                // Function to handle language selection
                                                function handleLanguageSelection(languageId, stateId) {
                                                    // Store the selection
                                                    sessionStorage.setItem("selected_language_id", languageId);
                                                    sessionStorage.setItem("selected_state_id", stateId);

                                                    // Update UI to show selected language
                                                    document.querySelectorAll('input[name="lang_id"]').forEach(r => {
                                                        const label = r.nextElementSibling;
                                                        const existingBadge = label.querySelector('.badge.selected-badge');
                                                        if (existingBadge) {
                                                            existingBadge.remove();
                                                        }
                                                    });

                                                    // Find the selected radio and add the badge
                                                    const selectedRadio = document.querySelector('input[name="lang_id"][value="' + languageId + '"]');
                                                    if (selectedRadio) {
                                                        const selectedLabel = selectedRadio.nextElementSibling;
                                                        // Only add the Selected badge if it doesn't already have one
                                                        if (!selectedLabel.querySelector('.badge.selected-badge')) {
                                                            const selectedBadge = document.createElement('span');
                                                            selectedBadge.classList.add('badge', 'bg-success', 'ms-1', 'selected-badge');
                                                            selectedBadge.textContent = 'Selected';
                                                            selectedLabel.appendChild(selectedBadge);
                                                        }
                                                    }

                                                    // Show continue button
                                                    showContinueButton(stateId, languageId);
                                                }

                                                // Add event listener for the Change State button
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const changeStateBtn = document.getElementById('changeStateBtn');
                                                    if (changeStateBtn) {
                                                        changeStateBtn.addEventListener('click', function() {
                                                            // Hide language section and show state section
                                                            document.getElementById('languageSelectionSection').style.display = 'none';
                                                            document.getElementById('stateSelectionSection').style.display = 'block';

                                                            // Remove any continue button that might be present
                                                            const continueBtnContainer = document.getElementById('continueBtnContainer');
                                                            if (continueBtnContainer) {
                                                                continueBtnContainer.remove();
                                                            }
                                                        });
                                                    }
                                                });

                                                // Initialize the Change State button when it's dynamically created
                                                function initChangeStateButton() {
                                                    const changeStateBtn = document.getElementById('changeStateBtn');
                                                    if (changeStateBtn) {
                                                        changeStateBtn.addEventListener('click', function() {
                                                            // Hide language section and show state section
                                                            document.getElementById('languageSelectionSection').style.display = 'none';
                                                            document.getElementById('stateSelectionSection').style.display = 'block';

                                                            // Remove any continue button that might be present
                                                            const continueBtnContainer = document.getElementById('continueBtnContainer');
                                                            if (continueBtnContainer) {
                                                                continueBtnContainer.remove();
                                                            }
                                                        });
                                                    }
                                                }

                                                // Function to show continue button
                                                function showContinueButton(stateId, langId) {
                                                    // Remove existing continue button if any
                                                    const existingButton = document.querySelector('#continueBtnContainer');
                                                    if (existingButton) {
                                                        existingButton.remove();
                                                    }

                                                    // Create continue button container
                                                    const buttonContainer = document.createElement('div');
                                                    buttonContainer.id = 'continueBtnContainer';
                                                    buttonContainer.className = 'text-center mt-4';

                                                    // Create continue button
                                                    const continueBtn = document.createElement('a');
                                                    continueBtn.href = "/rti/form?state_id=" + stateId + "&amp;lang_id=" + langId;
                                                    continueBtn.className = 'btn btn-primary btn-lg';
                                                    continueBtn.textContent = 'Continue to Application Form';

                                                    buttonContainer.appendChild(continueBtn);

                                                    // Append the button container to the language selection section
                                                    const languageSelection = document.querySelector('.language-selection');
                                                    if (languageSelection) {
                                                        languageSelection.appendChild(buttonContainer);
                                                    } else {
                                                        // Fallback if language selection container not found
                                                        document.getElementById('languageSelectionSection').querySelector('.card-body').appendChild(buttonContainer);
                                                    }
                                                }
                                            });
                                            </script>
                                            </div>
                                        </div>
                                        <div class="col-lg-12 mb-4" id="languageSelectionSection">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h3 class="card-title mb-4">Select Language</h3>
                                                    <p>Choose your language to proceed with RTI application:</p>
                                                    <div class="language-selection mb-4">
                                                        <h5>Select Language:</h5>
                                                        <div class="row">
                                                            <t t-foreach="language_ids" t-as="language">
                                                                <div class="col-md-3 mb-2">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio" name="lang_id" t-att-value="language.id" t-att-checked="language.id == (lang_id and int(lang_id) or default_language_id.id)" onchange="changeLanguage(this.value)" />
                                                                        <label class="form-check-label">
                                                                            <t t-esc="language.name"/>
                                                                            <t t-if="language.id == default_language_id.id">
                                                                                <span class="badge bg-primary ms-1">Default</span>
                                                                            </t>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </t>
                                                        </div>
                                                    </div>
                                                    <div class="text-center mt-3">
                                                        <button id="changeStateBtn" class="btn btn-outline-secondary">
                                                            <i class="fas fa-arrow-left me-2"></i> Change State
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="rti_form" name="RTI Form">
         <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <!-- FontAwesome CSS with fallback -->
                <link rel="stylesheet" href="/ai_rti_master/static/src/css/icon_fallback.css"/>
                <!-- RTI Custom CSS -->
                <link rel="stylesheet" href="/ai_rti_master/static/src/css/rti_style.css"/>
                <link rel="stylesheet" href="/ai_rti_master/static/src/css/rti_style_mobile.css"/>
                <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css"/>
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"/>
                <!-- Google Transliteration API -->
                <script type="text/javascript" src="https://www.google.com/jsapi"/>
                <script type="text/javascript">
                    // Set language_code in window object for access in JavaScript
                    window.language_code = '<t t-esc="language_code or 'en_US'"/>';
                    console.log('Setting language code in window:', window.language_code);

                    // Pre-load Google Transliteration API
                    if (window.google &amp;&amp; window.google.load) {
                        try {
                            google.load('elements', '1', {
                                packages: 'transliteration',
                                callback: function() {
                                    console.log('Google Transliteration API pre-loaded successfully');
                                }
                            });
                        } catch (e) {
                            console.error('Error pre-loading Google Transliteration API:', e);
                        }
                    }
                </script>
                <script type="text/javascript" src="/ai_rti_master/static/src/js/transliteration_standalone.js" defer="defer"/>


                <div class="container">
                    <h2 class="text-center mb-4">RTI Application Form</h2>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <h4>State: <t t-esc="state.name"/> | Language: <t t-esc="language_name"/></h4>
                                    <!-- RTI Form Section -->
                                    <div t-if="lang_id" class="rti-form-section">
                                        <form id="rtiForm" method="POST" class="js_rti_form" action="/rti/apply/submit" enctype="multipart/form-data"  data-mark="*" data-pre-fill="true" data-model_name="ai_rti_master" >
                                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                            <input type="hidden" name="rti_state" t-att-value="state.id"/>
                                            <input type="hidden" name="lang_id" t-att-value="lang_id"/>

                                            <!-- Tab Navigation -->
                                            <ul class="nav nav-tabs mb-4" id="rtiTab" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <a class="nav-link active" id="personal-tab" data-toggle="tab" href="#personal" role="tab" aria-controls="personal" aria-selected="true">
                                                        Personal &amp; Address Details
                                                    </a>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <a class="nav-link" id="specific-tab" data-toggle="tab" href="#specific" role="tab" aria-controls="specific" aria-selected="false">
                                                        Department &amp; RTI Details
                                                    </a>
                                                </li>
                                            </ul>

                                            <!-- Tab Content -->
                                            <div class="tab-content" id="rtiTabContent">
                                                <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                                                    <!-- Personal Details -->
                                                    <div class="row g-3">
                                                        <div class="col-12">
                                                            <div class="section-header mb-3">
                                                                <h4>Personal Information</h4>
                                                                <div class="alert alert-info" id="prefill-notice" style="display:none;">
                                                                    <i class="fa fa-info-circle"></i> Some fields have been pre-filled with your account information. You can modify these values if needed.
                                                                </div>
                                                                <div class="transliteration-controls" t-if="language_code and language_code != 'en_US'">
                                                                    <div class="form-check form-switch">
                                                                        <input class="form-check-input" type="checkbox" id="toggleTransliteration" data-enabled="false"/>
                                                                        <label class="form-check-label" for="toggleTransliteration">
                                                                            <span id="transliteration-label">Enable transliteration to <t t-esc="language_name"/></span>
                                                                        </label>
                                                                    </div>
                                                                    <small class="text-muted">Type in English and your text will be automatically converted to <t t-esc="language_name"/>.</small>
                                                                    <script>
                                                                        // Set initial state based on language
                                                                        document.addEventListener('DOMContentLoaded', function() {
                                                                            const langCode = window.language_code ? window.language_code.split('_')[0].toLowerCase() : 'en';
                                                                            const isIndianLanguage = langCode === 'hi' || langCode === 'gu';

                                                                            // Update the label text based on default state
                                                                            const label = document.getElementById('transliteration-label');
                                                                            if (label &amp;&amp; isIndianLanguage) {
                                                                                label.textContent = 'Disable transliteration to ' + '<t t-esc="language_name"/>';
                                                                            }
                                                                        });
                                                                    </script>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="x_first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="x_first_name" name="x_first_name" pattern="[A-Za-z\s]{1,50}" title="Only letters and spaces allowed" required="1"/>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="x_middle_name" class="form-label">Middle Name</label>
                                                            <input type="text" class="form-control" id="x_middle_name" name="x_middle_name" pattern="[A-Za-z\s]{0,50}" title="Only letters and spaces allowed"/>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <label for="x_surname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="x_surname" name="x_surname" pattern="[A-Za-z\s]{1,50}" title="Only letters and spaces allowed" required="1"/>
                                                        </div>
                                                    </div>

                                                    <div class="row g-3 mt-2">
                                                        <div class="col-md-6">
                                                            <label for="x_mobile_number" class="form-label">Mobile Number <span class="text-danger">*</span></label>
                                                            <input type="tel" class="form-control" id="x_mobile_number" name="x_mobile_number" pattern="[6-9][0-9]{9}" title="Please enter a valid 10-digit mobile number starting with 6, 7, 8, or 9" required="1" placeholder="e.g., 9876543210"/>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="x_whatsapp_number" class="form-label">WhatsApp Number</label>
                                                            <input type="tel" class="form-control" id="x_whatsapp_number" name="x_whatsapp_number" pattern="[6-9][0-9]{9}" title="Please enter a valid 10-digit WhatsApp number"/>
                                                        </div>
                                                    </div>

                                                    <div class="row g-3 mt-2">
                                                        <div class="col-md-6">
                                                            <label for="x_email" class="form-label">Email <span class="text-danger">*</span></label>
                                                            <input type="email" class="form-control" id="x_email" name="x_email" pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}" title="Please enter a valid email address" required="1"/>
                                                        </div>
                                                    </div>

                                                    <!-- Address Details -->
                                                    <div class="row g-3 mt-2">
                                                        <div class="col-md-12">
                                                            <label for="x_street" class="form-label">Street Address <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="x_street" name="x_street" placeholder="Enter your complete street address (flat/house number and street/area)" required="1"/>
                                                            <small class="text-muted">Enter your complete address including flat/house number and street/area</small>
                                                            <input type="hidden" id="x_flat_no" name="x_flat_no" value=""/>
                                                            <script>
                                                                // Set the x_flat_no field to a default value for backward compatibility
                                                                document.addEventListener('DOMContentLoaded', function() {
                                                                    document.getElementById('rtiForm').addEventListener('submit', function() {
                                                                        // If x_flat_no is empty, set it to a default value
                                                                        if (!document.getElementById('x_flat_no').value) {
                                                                            document.getElementById('x_flat_no').value = 'See street address';
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                        </div>
                                                    </div>

                                                    <div class="row g-3 mt-2">
                                                        <div class="col-md-6">
                                                            <label for="x_landmark" class="form-label">Landmark</label>
                                                            <input type="text" class="form-control" id="x_landmark" name="x_landmark"/>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="x_pincode" class="form-label">Pincode <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="x_pincode" name="x_pincode" pattern="[0-9]{6}" maxlength="6" required="1"/>
                                                        </div>
                                                    </div>

                                                    <div class="row g-3 mt-2">
                                                        <div class="col-md-6">
                                                            <label for="x_state" class="form-label">State <span class="text-danger">*</span></label>
                                                            <select class="form-control select2" id="x_state" name="x_state" required="1">
                                                                <option value="">Select State</option>
                                                                <t t-foreach="request.env['res.country.state'].sudo().search([('country_id.code', '=', 'IN')])" t-as="state">
                                                                    <option t-att-value="state.id"><t t-esc="state.name"/></option>
                                                                </t>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="x_district" class="form-label">District</label>
                                                            <select class="form-control select2" id="x_district" name="x_district">
                                                                <option value="">Select District</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="row g-3 mt-2">
                                                        <div class="col-md-6">
                                                            <label for="x_taluka" class="form-label">Taluka</label>
                                                            <select class="form-control select2" id="x_taluka" name="x_taluka">
                                                                <option value="">Select Taluka</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="x_village" class="form-label">Village/City</label>
                                                            <select class="form-control select2" id="x_village" name="x_village">
                                                                <option value="">Select Village/City</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-4">
                                                        <div class="col-12 d-flex justify-content-end">
                                                            <button type="button" class="btn btn-primary" id="nextButton" onclick="nextTab()">
                                                                Next Step <i class="fas fa-chevron-right ms-2"/>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                 <div class="tab-pane fade" id="specific" role="tabpanel" aria-labelledby="specific-tab">
                                                    <!-- Department Details -->
                                                    <div class="row g-3">
                                                        <div class="col-12">
                                                            <h4>Department Information</h4>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="form-check">
                                                                <input type="checkbox" class="form-check-input" id="x_manual_department" name="x_manual_department"/>
                                                                <label class="form-check-label" for="x_manual_department">Add Manual Department</label>
                                                            </div>
                                                        </div>

                                                        <div id="manualDepartmentFields" class="col-12" style="display: none;">
                                                            <div class="form-group mb-3">
                                                                <label for="x_manual_department_name">Department Name <span class="text-danger">*</span></label>
                                                                <input type="text" class="form-control" id="x_manual_department_name" name="x_manual_department_name"/>
                                                            </div>
                                                            <!-- <div class="form-group mb-3" id="departmentCategoryField" style="display: none;">
                                                                <label for="x_manual_department_category_id">Department Category <span class="text-danger">*</span></label>
                                                                <select class="form-control select2" id="x_manual_department_category_id" name="x_manual_department_category_id">
                                                                    <option value="">Select Category</option>
                                                                </select>
                                                            </div>
                                                            <div class="form-group mb-3" id="departmentLevelField" style="display: none;">
                                                                <label for="x_manual_department_level">Department Level <span class="text-danger">*</span></label>
                                                                <select class="form-control" id="x_manual_department_level" name="x_manual_department_level">
                                                                    <option value="">Select Level</option>
                                                                    <option value="state">State</option>
                                                                    <option value="zone">Zone</option>
                                                                    <option value="district">District</option>
                                                                    <option value="taluka">Taluka</option>
                                                                </select>
                                                            </div> -->
                                                            <div class="form-group mb-3"> 
                                                                <label for="x_manual_department_address">Department Address <span class="text-danger">*</span></label>
                                                                <textarea class="form-control" id="x_manual_department_address" name="x_manual_department_address" rows="4" placeholder="Enter department address"></textarea>
                                                            </div>
                                                        </div>

                                                        <!-- Department Dropdown -->
                                                        <div class="col-md-12 form-row" id="selectDepartmentContainer">
                                                            <label for="x_department_ids" class="form-label">Select Department(s) <span class="text-danger">*</span></label>
                                                            <select class="form-control select2" id="x_department_ids" name="x_department_ids" required="1" multiple="multiple">
                                                                <!-- Options will be loaded dynamically -->
                                                            </select>
                                                            <small class="text-muted">
                                                                <i class="fa fa-info-circle me-1"></i> You can select multiple departments. Click on a department to select it, click again to deselect.
                                                            </small>
                                                        </div>
                                                    </div>

                                                    <script>
                                                        $(document).ready(function() {
                                                            // Initially check the checkbox and show/hide fields accordingly
                                                            $('#x_manual_department').change(function() {
                                                                if ($(this).prop('checked')) {
                                                                    // Show the manual department fields
                                                                    $('#manualDepartmentFields').show();
                                                                    // Hide the select department dropdown
                                                                    $('#selectDepartmentContainer').hide();
                                                                    // Show the department category dropdown
                                                                    $('#departmentCategoryField').show();
                                                                    // Show the department level field
                                                                    $('#departmentLevelField').show();
                                                                } else {
                                                                    // Hide the manual department fields
                                                                    $('#manualDepartmentFields').hide();
                                                                    // Show the select department dropdown
                                                                    $('#selectDepartmentContainer').show();
                                                                    // Hide the department category dropdown
                                                                    $('#departmentCategoryField').hide();
                                                                    // Hide the department level field
                                                                    $('#departmentLevelField').hide();
                                                                }
                                                            });

                                                            // Trigger change event on page load to ensure the correct visibility
                                                            $('#x_manual_department').trigger('change');
                                                        });
                                                    </script>


                                                    <!-- RTI Details -->
                                                    <div class="row g-3 mt-4">
                                                        <div class="col-12">
                                                            <h4>RTI Information</h4>
                                                        </div>
                                                        <div class="col-12">
                                                            <label for="x_rti_details" class="form-label">RTI Details <span class="text-danger">*</span></label>
                                                            <textarea class="form-control" id="x_rti_details" name="x_rti_details" rows="4" required="1" placeholder="Please provide detailed information about your RTI request"/>
                                                            <small class="text-muted">
                                                                Please provide specific details about the information you are seeking through this RTI application
                                                            </small>
                                                        </div>
                                                    </div>

                                                    <!-- File Attachment -->
                                                    <div class="row g-3 mt-4">
                                                        <div class="col-md-12">
                                                            <label for="x_attachment" class="form-label">Attachment</label>
                                                            <input type="file" class="form-control" id="x_attachment" name="x_attachment"/>
                                                            <small class="text-muted">Upload supporting documents (if any)</small>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-4">
                                                        <div class="col-12 d-flex justify-content-between align-items-center">
                                                            <button type="button" class="btn btn-outline-secondary" onclick="prevTab()">
                                                                <i class="fas fa-chevron-left me-2"/> Previous Step
                                                            </button>
                                                            <button type="submit" id="submitBtn" class="btn btn-success">
                                                                Submit Application <i class="fas fa-check ms-2"/>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div t-if="not lang_id" class="alert alert-info">
                                        Please select a language to proceed with the RTI application.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script type="text/javascript">
                    // Set user_id and language_code in window object for access in JavaScript
                    window.user_id = <t t-esc="user_id.id if user_id and not user_id._is_public() else 'null'"/>;
                    window.language_code = '<t t-esc="language_code or 'en_US'"/>';

                    // Function to load partner data and pre-fill the form
                    function loadPartnerData() {
                        // Only attempt to load partner data if the user is logged in
                        if (window.user_id &amp;&amp; window.user_id !== null &amp;&amp; window.user_id !== 'None' &amp;&amp; window.user_id !== 'False') {
                            console.log('Fetching partner data for pre-filling form...');

                            // Make AJAX call to get partner data
                            $.ajax({
                                url: '/rti/get_partner_data',
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {}
                                }),
                                success: function(response) {
                                    if (response.result &amp;&amp; !$.isEmptyObject(response.result)) {
                                        console.log('Partner data received:', response.result);
                                        const data = response.result;

                                        // Fill in the form fields with partner data
                                        if (data.name) {
                                            // Split name into parts (assuming format is "First Middle Last")
                                            var nameParts = data.name.split(' ');
                                            if (nameParts.length >= 1) $('#x_first_name').val(nameParts[0]);
                                            if (nameParts.length >= 3) {
                                                $('#x_middle_name').val(nameParts[1]);
                                                $('#x_surname').val(nameParts.slice(2).join(' '));
                                            } else if (nameParts.length >= 2) {
                                                $('#x_surname').val(nameParts.slice(1).join(' '));
                                            }
                                        }

                                        // Fill in contact details
                                        if (data.email) $('#x_email').val(data.email);
                                        if (data.mobile) $('#x_mobile_number').val(data.mobile);
                                        else if (data.phone) $('#x_mobile_number').val(data.phone);

                                        // Fill in address fields - using the combined street field
                                        if (data.street) {
                                            let fullAddress = data.street;
                                            if (data.street2) fullAddress += ', ' + data.street2;
                                            $('#x_street').val(fullAddress);
                                        }

                                        if (data.zip) $('#x_pincode').val(data.zip);

                                        // Set state if available
                                        if (data.state_id) {
                                            $('#x_state').val(data.state_id).trigger('change.select2');
                                            // This will trigger district loading
                                        }

                                        // Show the pre-fill notice
                                        $('#prefill-notice').show();

                                        // Reinitialize transliteration after form is pre-filled
                                        if (window.rtiTransliteration &amp;&amp; typeof window.rtiTransliteration._setupAllFieldsForTransliteration === 'function') {
                                            console.log('Reinitializing transliteration after form pre-fill');
                                            const langCode = window.language_code ? window.language_code.split('_')[0].toLowerCase() : 'en';
                                            window.rtiTransliteration._setupAllFieldsForTransliteration(langCode);

                                            // If transliteration should be enabled by default (for Hindi/Gujarati)
                                            if (langCode === 'hi' || langCode === 'gu') {
                                                const toggleBtn = document.getElementById('toggleTransliteration');
                                                if (toggleBtn) {
                                                    toggleBtn.checked = true;
                                                    toggleBtn.setAttribute('data-enabled', 'true');
                                                    window.transliterationEnabled = true;
                                                }
                                            }
                                        }
                                    }
                                },
                                error: function(error) {
                                    console.error('Error loading partner data:', error);
                                }
                            });
                        } else {
                            console.log('User not logged in, skipping partner data fetch');
                        }
                    }

                    function changeLanguage(langId) {
                        const stateId = <t t-esc="state.id"/>;
                        window.location.href = `/rti/form?state_id=${stateId}&amp;lang_id=${langId}`;
                    }

                    // Auto-select default language if no valid language is selected
                    document.addEventListener('DOMContentLoaded', function() {
                        // Check if lang_id is passed in the URL
                        var lang_id = <t t-esc="lang_id or 'null'"/>;
                        var default_language_id = <t t-esc="default_language_id.id if default_language_id else 'null'"/>;
                        console.log('Default language ID:', default_language_id);

                        // Only auto-select default language if lang_id is not present or invalid
                        if (!lang_id || lang_id === 'null') {
                            if (default_language_id &amp;&amp; default_language_id !== 'null') {
                                var defaultLang = document.querySelector(`input[name="lang_id"][value="${default_language_id}"]`);
                                if (defaultLang) {
                                    defaultLang.checked = true;
                                    changeLanguage(default_language_id);
                                }
                            }
                        }

                        // Initialize transliteration after page load
                        setTimeout(function() {
                            if (typeof RtiTransliteration !== 'undefined') {
                                console.log('Initializing RTI Transliteration');
                                window.rtiTransliteration = new RtiTransliteration();
                            } else {
                                console.log('RtiTransliteration class not found, retrying...');
                                // Retry after a short delay
                                setTimeout(function() {
                                    if (typeof RtiTransliteration !== 'undefined') {
                                        console.log('Initializing RTI Transliteration (retry)');
                                        window.rtiTransliteration = new RtiTransliteration();
                                    } else {
                                        console.warn('RtiTransliteration class still not available');
                                    }
                                }, 1000);
                            }
                        }, 500);
                    });
                </script>

                <script type="text/javascript">
                    $(document).ready(function() {
                        // Initialize Select2 for all dropdowns
                        $('.select2').select2({
                            width: '100%',
                            placeholder: function() {
                                return $(this).attr('placeholder') || 'Select...';
                            },
                            allowClear: true
                        });

                        // Special configuration for multiple select - we'll initialize this in loadDepartments
                        // to ensure it's properly configured after departments are loaded

                        // Load partner data to pre-fill the form
                        loadPartnerData();

                        // Load departments with immediate fallback
                        loadDepartmentsWithFallback();

                        // Add real-time mobile number validation
                        $('#x_mobile_number').on('input', function() {
                            var mobile = $(this).val().replace(/[^0-9]/g, '');
                            var isValid = mobile.length === 10 && /^[6-9]/.test(mobile);

                            if (mobile.length > 0) {
                                if (mobile.length !== 10) {
                                    $(this).removeClass('is-valid').addClass('is-invalid');
                                    $(this).attr('title', 'Mobile number must be exactly 10 digits');
                                } else if (!/^[6-9]/.test(mobile)) {
                                    $(this).removeClass('is-valid').addClass('is-invalid');
                                    $(this).attr('title', 'Mobile number must start with 6, 7, 8, or 9');
                                } else {
                                    $(this).removeClass('is-invalid').addClass('is-valid');
                                    $(this).attr('title', 'Valid mobile number');
                                }
                            } else {
                                $(this).removeClass('is-valid is-invalid');
                            }
                        });
                        function loadDepartments() {
                            const urlParams = new URLSearchParams(window.location.search);
                            const stateId = urlParams.get('state_id');
                            const langId = <t t-esc="lang_id"/>;

                            // Check if stateId is present
                            if (!stateId) {
                                console.error('State ID is missing in the URL.');
                                return;
                            }
                            console.log('State ID from URL:', stateId);
                            console.log('Loading departments for state:', stateId, 'lang:', langId);

                            return $.ajax({
                                url: '/rti/departments',
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        state_id: stateId,
                                        lang_id: langId
                                    }
                                })
                            })
                            .then(function(response) {
                                // Log the full response to verify its structure
                                console.log('Response:', response);

                                if (response.error) {
                                    console.error('Department error details:', response.error);
                                    throw new Error(response.error.data?.message || 'Server error');
                                }

                                // Ensure response.result is an array
                                const departments = Array.isArray(response.result) ? response.result : [];
                                if (departments.length === 0) {
                                    console.error('No departments found.');
                                    // Add a message to the dropdown
                                    const select = $('#x_department_ids');
                                    select.empty().append('<option value="">No departments found</option>');
                                    select.trigger('change.select2');
                                    return;
                                }

                                // Select dropdown element
                                const select = $('#x_department_ids');
                                if (select.length === 0) {
                                    console.error('Dropdown element not found.');
                                    return;
                                }

                                // Empty the dropdown and add a placeholder
                                select.empty();

                                // Add departments to the dropdown
                                departments.forEach(function(dept) {
                                    select.append(new Option(dept.name || dept.display_name, dept.id));
                                });

                                // Destroy and reinitialize Select2 to ensure it works properly
                                if (select.data('select2')) {
                                    select.select2('destroy');
                                }

                                // Initialize Select2 with proper configuration for multiple selection
                                select.select2({
                                    width: '100%',
                                    placeholder: 'Select Department(s)...',
                                    allowClear: true,
                                    multiple: true,
                                    closeOnSelect: false
                                });

                                // Log the number of departments loaded
                                console.log('Loaded', departments.length, 'departments');
                            })
                            .catch(function(error) {
                                console.error('Error loading departments:', error);
                                // Try to load all departments as fallback
                                console.log('Attempting to load all departments as fallback...');
                                $.ajax({
                                    url: '/rti/departments',
                                    type: 'POST',
                                    dataType: 'json',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        jsonrpc: '2.0',
                                        method: 'call',
                                        params: {}  // No state filter
                                    })
                                })
                                .then(function(response) {
                                    console.log('Fallback response:', response);
                                    const departments = Array.isArray(response.result) ? response.result : [];
                                    const select = $('#x_department_ids');
                                    select.empty();

                                    if (departments.length > 0) {
                                        departments.forEach(function(dept) {
                                            select.append(new Option(dept.name || dept.display_name, dept.id));
                                        });
                                        console.log('Loaded', departments.length, 'departments as fallback');
                                    } else {
                                        select.append('<option value="">No departments available</option>');
                                    }

                                    // Reinitialize Select2
                                    if (select.data('select2')) {
                                        select.select2('destroy');
                                    }
                                    select.select2({
                                        width: '100%',
                                        placeholder: 'Select Department(s)...',
                                        allowClear: true,
                                        multiple: true,
                                        closeOnSelect: false
                                    });
                                })
                                .catch(function(fallbackError) {
                                    console.error('Fallback also failed:', fallbackError);
                                    console.log('Loading static departments as final fallback...');
                                    const select = $('#x_department_ids');
                                    select.empty();

                                    // Add some common departments as static fallback
                                    const staticDepartments = [
                                        {id: 'revenue', name: 'Revenue Department'},
                                        {id: 'education', name: 'Education Department'},
                                        {id: 'transport', name: 'Transport Department'},
                                        {id: 'agriculture', name: 'Agriculture Department'},
                                        {id: 'health', name: 'Health Department'},
                                        {id: 'police', name: 'Police Department'},
                                        {id: 'municipal', name: 'Municipal Corporation'},
                                        {id: 'other', name: 'Other Department'}
                                    ];

                                    staticDepartments.forEach(function(dept) {
                                        select.append(new Option(dept.name, dept.id));
                                    });

                                    console.log('Loaded', staticDepartments.length, 'static departments');

                                    // Reinitialize Select2
                                    if (select.data('select2')) {
                                        select.select2('destroy');
                                    }
                                    select.select2({
                                        width: '100%',
                                        placeholder: 'Select Department(s)...',
                                        allowClear: true,
                                        multiple: true,
                                        closeOnSelect: false
                                    });
                                });
                            });
                        }

                        function loadDepartmentCategories() {
                            const stateId = <t t-esc="state.id"/>;
                            const langId = <t t-esc="lang_id"/>;

                            return $.ajax({
                                url: '/rti/department/categories',
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        state_id: stateId,
                                        lang_id: langId
                                    }
                                })
                            })
                            .then(function(response) {
                                if (response.error) {
                                    throw new Error(response.error.data?.message || 'Server error');
                                }

                                const categories = Array.isArray(response.result) ? response.result : [];
                                const select = $('#x_manual_department_category_id');
                                select.empty().append('<option value="">Select Category...</option>');

                                categories.forEach(function(category) {
                                    select.append(new Option(category.name || category.display_name, category.id));
                                });
                                select.trigger('change.select2'); // Use change.select2 instead of change
                            })
                            .fail(function(jqXHR, textStatus, errorThrown) {
                                console.error('Categories AJAX error details:', {
                                    status: jqXHR.status,
                                    statusText: jqXHR.statusText,
                                    responseText: jqXHR.responseText,
                                    textStatus: textStatus,
                                    errorThrown: errorThrown
                                });

                                const select = $('#x_manual_department_category_id');
                                select.empty().append('<option value="">Error loading categories</option>');
                                select.trigger('change.select2'); // Use change.select2 instead of change
                            });
                        }

                         function loadDistricts(stateId) {
                            if (!stateId) return;

                            const langId = <t t-esc="lang_id"/>;  // Get lang_id from template context

                            console.log('Loading districts for state:', stateId, 'lang:', langId);

                            return $.ajax({
                                url: '/rti/districts',  // Ensure this matches your backend endpoint
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        state_id: stateId,
                                        lang_id: langId
                                    }
                                })
                            })
                            .then(function(response) {
                                if (response.error) {
                                    console.error('District error details:', response.error);
                                    throw new Error(response.error.data?.message || 'Server error');
                                }

                                const districts = Array.isArray(response.result) ? response.result : [];
                                const select = $('#x_district');
                                select.empty().append('<option value="">Select District...</option>');

                                districts.forEach(function(district) {
                                    select.append(new Option(district.name || district.display_name, district.id));
                                });
                                select.trigger('change.select2');  // Update select2 dropdown
                            })
                        }
                            // Add event listener for state change
                            $('#x_state').on('change', function() {
                                // Clear dependent dropdowns
                                $('#x_district, #x_taluka, #x_village').empty().trigger('change.select2');
                                if ($(this).val()) {
                                    loadDistricts($(this).val());  // Call loadDistricts when a state is selected
                                }
                            });

                            function loadTalukas(districtId) {
                            if (!districtId) return;

                            const langId = <t t-esc="lang_id"/>;  // Get lang_id from template context

                            console.log('Loading talukas for district:', districtId, 'lang:', langId);

                            return $.ajax({
                                url: '/rti/talukas',  // Backend endpoint for talukas
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        district_id: districtId,
                                        lang_id: langId
                                    }
                                })
                            })
                            .then(function(response) {
                                if (response.error) {
                                    console.error('Taluka error details:', response.error);
                                    throw new Error(response.error.data?.message || 'Server error');
                                }

                                const talukas = Array.isArray(response.result) ? response.result : [];
                                const select = $('#x_taluka');
                                select.empty().append('<option value="">Select Taluka...</option>');

                                talukas.forEach(function(taluka) {
                                    select.append(new Option(taluka.name || taluka.display_name, taluka.id));
                                });
                                select.trigger('change.select2');  // Update select2 dropdown
                            });
                        }

                        $('#x_district').on('change', function() {
                            // Clear dependent dropdowns
                            $('#x_taluka, #x_village').empty().trigger('change.select2');
                            if ($(this).val()) {
                                loadTalukas($(this).val());  // Call loadTalukas when a district is selected
                            }
                        });

                        function loadVillages(talukaId) {
                            if (!talukaId) return;

                            const langId = <t t-esc="lang_id"/>;  // Get lang_id from template context

                            console.log('Loading villages for taluka:', talukaId, 'lang:', langId);

                            return $.ajax({
                                url: '/rti/villages',  // Backend endpoint for villages
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        taluka_id: talukaId,
                                        lang_id: langId
                                    }
                                })
                            })
                            .then(function(response) {
                                if (response.error) {
                                    console.error('Village error details:', response.error);
                                    throw new Error(response.error.data?.message || 'Server error');
                                }

                                const villages = Array.isArray(response.result) ? response.result : [];
                                const select = $('#x_village');
                                select.empty().append('<option value="">Select Village...</option>');

                                villages.forEach(function(village) {
                                    select.append(new Option(village.name || village.display_name, village.id));
                                });
                                select.trigger('change.select2');  // Update select2 dropdown
                            });
                        }

                    $('#x_taluka').on('change', function() {
                        // Clear dependent dropdown
                        $('#x_village').empty().trigger('change.select2');
                        if ($(this).val()) {
                            loadVillages($(this).val());  // Call loadVillages when a taluka is selected
                        }
                    });
                        // Initialize other dependent dropdowns
                        $('#x_state').on('change', function() {
                            // Clear dependent dropdowns
                            $('#x_district, #x_taluka, #x_village').empty().trigger('change.select2');
                            if ($(this).val()) {
                                loadDistricts($(this).val());
                            }
                        });

                        $('#x_district').on('change', function() {
                            // Clear dependent dropdowns
                            $('#x_taluka, #x_village').empty().trigger('change.select2');
                            if ($(this).val()) {
                                loadTalukas($(this).val());
                            }
                        });

                        $('#x_taluka').on('change', function() {
                            // Clear dependent dropdown
                            $('#x_village').empty().trigger('change.select2');
                            if ($(this).val()) {
                                loadVillages($(this).val());
                            }
                        });

                    });
                       function nextTab() {
                        var currentTab = $('.nav-tabs .active');
                        var nextTab = currentTab.parent().next('li');

                        if (nextTab.length) {
                            nextTab.find('a').tab('show');
                        } else {
                            console.log('No more tabs available');
                            $('#nextButton').prop('disabled', true);  // Disable Next button if last tab
                        }
                    }

                    // Previous Tab function
                    function prevTab() {
                        var currentTab = $('.nav-tabs .active');
                        var prevTab = currentTab.parent().prev('li');

                        if (prevTab.length) {
                            prevTab.find('a').tab('show');
                            $('#prevButton').prop('disabled', false);  // Enable Previous button
                        } else {
                            console.log('No previous tab available');
                            $('#prevButton').prop('disabled', true);  // Disable Previous button if first tab
                        }
                    }

                    $(document).ready(function() {
                        $('#rtiForm').submit(function(e) {
                            console.log('Form submission started');
                            e.preventDefault();

                            // Basic form validation
                            var isValid = true;
                            var errorMessage = '';

                            // Check required fields
                            var requiredFields = ['x_first_name', 'x_surname', 'x_mobile_number', 'x_email', 'x_street', 'x_pincode', 'x_state'];
                            for (var i = 0; i &lt; requiredFields.length; i++) {
                                var field = $('#' + requiredFields[i]);
                                if (!field.val() || field.val().trim() === '') {
                                    isValid = false;
                                    errorMessage = 'Please fill in all required fields: ' + requiredFields[i];
                                    break;
                                }
                            }

                            // Special validation for mobile number
                            var mobileNumber = $('#x_mobile_number').val();
                            if (mobileNumber) {
                                // Remove any spaces, dashes, or other characters
                                var cleanMobile = mobileNumber.replace(/[^0-9]/g, '');
                                if (cleanMobile.length !== 10) {
                                    isValid = false;
                                    errorMessage = 'Mobile number must be exactly 10 digits';
                                } else if (!/^[6-9]/.test(cleanMobile)) {
                                    isValid = false;
                                    errorMessage = 'Mobile number must start with 6, 7, 8, or 9';
                                } else {
                                    // Update the field with clean number
                                    $('#x_mobile_number').val(cleanMobile);
                                }
                            }

                            // Check if departments are selected (unless manual department is checked)
                            if (!$('#x_manual_department').prop('checked')) {
                                var selectedDepts = $('#x_department_ids').val();
                                if (!selectedDepts || selectedDepts.length === 0) {
                                    isValid = false;
                                    errorMessage = 'Please select at least one department';
                                }
                            } else {
                                // Check manual department fields
                                if (!$('#x_manual_department_name').val() || !$('#x_manual_department_address').val()) {
                                    isValid = false;
                                    errorMessage = 'Please fill in manual department name and address';
                                }
                            }

                            // Check RTI details
                            if (!$('#x_rti_details').val() || $('#x_rti_details').val().trim() === '') {
                                isValid = false;
                                errorMessage = 'Please provide RTI details';
                            }

                            if (!isValid) {
                                alert(errorMessage);
                                console.log('Form validation failed:', errorMessage);
                                return false;
                            }

                            console.log('Form validation passed, preparing submission');

                            var formData = new FormData(this);

                            // Ensure CSRF token is included
                            var csrfToken = $('input[name="csrf_token"]').val();
                            if (csrfToken && !formData.has('csrf_token')) {
                                formData.append('csrf_token', csrfToken);
                            }

                            // Add language ID if not already in the form
                            if (!formData.has('lang_id')) {
                                formData.append('lang_id', $('#languageSelect').val());
                            }

                            // Debug: Log form data
                            console.log('Form data being submitted:');
                            console.log('CSRF Token found:', csrfToken);
                            for (var pair of formData.entries()) {
                                console.log(pair[0] + ': ' + pair[1]);
                            }

                            // Show loading indicator
                            var submitBtn = $('#submitBtn');
                            if (submitBtn.length === 0) {
                                submitBtn = $('button[type="submit"]').first();
                            }
                            submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Submitting...');
                            console.log('Submit button disabled, starting AJAX request');

                            $.ajax({
                                url: '/rti/apply/submit',
                                type: 'POST',
                                data: formData,
                                processData: false,
                                contentType: false,
                                success: function(response) {
                                    console.log('AJAX Success - Response received:', response);
                                    console.log('Response type:', typeof response);
                                    // The server should redirect to the success page
                                    // If we get a response, it might be HTML content
                                    if (typeof response === 'string') {
                                        // Try to extract the RTI ID from the response
                                        const rtiIdMatch = response.match(/\/rti\/success\/(\d+)/);
                                        if (rtiIdMatch &amp;&amp; rtiIdMatch[1]) {
                                            window.location.href = '/rti/success/' + rtiIdMatch[1];
                                            return;
                                        }

                                        // Try to extract the RTI number
                                        const rtiNumberMatch = response.match(/Application Number:\s*&lt;[^&gt;]*&gt;([^&lt;]+)&lt;\/span&gt;/);
                                        if (rtiNumberMatch &amp;&amp; rtiNumberMatch[1]) {
                                            const rtiNumber = rtiNumberMatch[1].trim();
                                            // Redirect to the success page with the RTI number
                                            window.location.href = '/my/rti?rti_number=' + encodeURIComponent(rtiNumber);
                                            return;
                                        }
                                    }

                                    // If we can't extract anything, just go to the RTI dashboard
                                    window.location.href = '/my/rti';
                                },
                                error: function(xhr, status, error) {
                                    console.log('AJAX Error occurred');
                                    console.log('XHR Status:', xhr.status);
                                    console.log('Status:', status);
                                    console.log('Error:', error);
                                    console.log('Response Text:', xhr.responseText);
                                    // Re-enable the submit button
                                    var submitBtn = $('#submitBtn');
                                    if (submitBtn.length === 0) {
                                        submitBtn = $('button[type="submit"]').first();
                                    }
                                    submitBtn.prop('disabled', false).html('Submit Application <i class="fas fa-check ms-2"></i>');

                                    // Check for redirect status codes
                                    if (xhr.status === 302 || xhr.status === 303) {
                                        // Get the redirect URL from the Location header
                                        var redirectUrl = xhr.getResponseHeader('Location');
                                        if (redirectUrl) {
                                            window.location.href = redirectUrl;
                                            return;
                                        }
                                    }

                                    // If the response contains HTML, it might be the success page
                                    if (xhr.responseText &amp;&amp; xhr.responseText.indexOf('RTI Application Submitted Successfully') !== -1) {
                                        // Extract the RTI ID if possible
                                        const rtiIdMatch = xhr.responseText.match(/\/rti\/success\/(\d+)/);
                                        if (rtiIdMatch &amp;&amp; rtiIdMatch[1]) {
                                            window.location.href = '/rti/success/' + rtiIdMatch[1];
                                            return;
                                        }

                                        // Try to extract the RTI number
                                        const match = xhr.responseText.match(/Application Number:\s*&lt;[^&gt;]*&gt;([^&lt;]+)&lt;\/span&gt;/);
                                        if (match &amp;&amp; match[1]) {
                                            const rtiNumber = match[1].trim();
                                            // Try to find the RTI ID from the number
                                            $.ajax({
                                                url: '/rti/get_id_from_number',
                                                type: 'POST',
                                                data: JSON.stringify({
                                                    'jsonrpc': '2.0',
                                                    'method': 'call',
                                                    'params': {
                                                        'rti_number': rtiNumber
                                                    }
                                                }),
                                                contentType: 'application/json',
                                                dataType: 'json',
                                                success: function(data) {
                                                    if (data.result &amp;&amp; data.result.id) {
                                                        window.location.href = '/rti/success/' + data.result.id;
                                                    } else {
                                                        window.location.href = '/my/rti?rti_number=' + encodeURIComponent(rtiNumber);
                                                    }
                                                },
                                                error: function() {
                                                    window.location.href = '/my/rti';
                                                }
                                            });
                                            return;
                                        }
                                    }

                                    // Check if we got a redirect to the login page
                                    if (xhr.responseText &amp;&amp; xhr.responseText.indexOf('login') !== -1 &amp;&amp; xhr.responseText.indexOf('password') !== -1) {
                                        window.location.href = '/web/login?redirect=' + encodeURIComponent(window.location.pathname + window.location.search);
                                        return;
                                    }

                                    // Default fallback
                                    alert('Request failed: ' + (xhr.responseText || error));
                                }
                            });
                        });
                    });

                </script>
            </div>
        </t>
    </template>

    <template id="rti_success_template" name="RTI Success">
        <t t-call="website.layout">
            <!-- Make sure jQuery is loaded -->
            <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body">
                                <h2 class="text-center text-success mb-4">RTI Application Submitted Successfully!</h2>
                                <div class="alert alert-success">
                                    <p><strong>Application Number: </strong><span t-field="rti.name"/></p>
                                    <p><strong>Applicant Name: </strong><span t-field="rti.x_first_name"/> <span t-field="rti.x_middle_name"/> <span t-field="rti.x_surname"/></p>
                                    <p><strong>State: </strong><span t-field="rti.x_state.name"/></p>
                                </div>

                                <!-- Addon Selection -->
                                <div class="mt-4">
                                    <h4 class="mb-3">Select Additional Services</h4>
                                    <form id="addonForm" t-att-action="'/rti/payment/%s' % rti.id" method="POST">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                        <div class="addon-list">
                                            <t t-foreach="request.env['product.product'].sudo().search([('is_rti_addon', '=', True)])" t-as="addon">
                                                <div class="form-check mb-3 addon-item">
                                                    <input t-if="addon.name in ['Download Only', 'Download and Delivery', 'Download, Delivery and Post']"
                                                           type="radio"
                                                           class="form-check-input addon-radio"
                                                           t-att-id="'addon_%s' % addon.id"
                                                           name="delivery_option"
                                                           t-att-value="addon.id"
                                                           t-att-data-price="addon.list_price"
                                                           t-att-data-per-dept="addon.allow_multiple_qty and 1 or 0"
                                                           t-att-checked="addon.name == 'Download Only' and 'checked' or None"
                                                           t-att-data-name="addon.name"/>
                                                    <input t-if="addon.name not in ['Download Only', 'Download and Delivery', 'Download, Delivery and Post']"
                                                           type="checkbox"
                                                           class="form-check-input addon-checkbox"
                                                           t-att-id="'addon_%s' % addon.id"
                                                           t-att-name="'addon_%s' % addon.id"
                                                           t-att-value="addon.id"
                                                           t-att-data-price="addon.list_price"
                                                           t-att-data-per-dept="addon.allow_multiple_qty and 1 or 0"
                                                           t-att-data-name="addon.name"/>
                                                    <label class="form-check-label" t-att-for="'addon_%s' % addon.id">
                                                        <span t-field="addon.name"/> - ₹<span t-field="addon.list_price"/>
                                                        <t t-if="addon.allow_multiple_qty"> (per department)</t>
                                                        <p class="text-muted mb-0 mt-1" t-field="addon.description_sale"/>
                                                    </label>
                                                    <div t-if="addon.allow_multiple_qty" class="mt-2 quantity-input" style="display: none;">
                                                        <div class="input-group" style="max-width: 200px;">
                                                            <span class="input-group-text">Quantity</span>
                                                            <input type="number"
                                                                   class="form-control addon-quantity"
                                                                   t-att-name="'qty_%s' % addon.id"
                                                                   t-att-id="'qty_%s' % addon.id"
                                                                   value="1" min="1"
                                                                   t-att-data-addon-id="addon.id"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </t>
                                        </div>

                                        <div class="mt-4 alert alert-info">
                                            <h5>Total Amount: ₹<span id="totalAmount">0.00</span></h5>
                                            <input type="hidden" id="initialTotal" value="0.00"/>
                                        </div>

                                        <div class="mt-4 text-center">
                                            <button type="submit" class="btn btn-primary">Proceed to Payment</button>
                                        </div>

                                        <!-- Direct script to set initial total -->
                                        <script>
                                            document.addEventListener('DOMContentLoaded', function() {
                                                // Find the Download Only radio button and get its price
                                                var radioButtons = document.querySelectorAll('.addon-radio');
                                                var foundDownloadOnly = false;

                                                // First try to find and select Download Only
                                                for (var i = 0; i &lt; radioButtons.length; i++) {
                                                    var radio = radioButtons[i];
                                                    var name = radio.getAttribute('data-name');
                                                    if (name &amp;&amp; name.indexOf('Download Only') !== -1) {
                                                        radio.checked = true;
                                                        foundDownloadOnly = true;
                                                        var price = parseFloat(radio.getAttribute('data-price') || 0);
                                                        document.getElementById('totalAmount').textContent = price.toFixed(2);
                                                        document.getElementById('initialTotal').value = price.toFixed(2);
                                                        break;
                                                    }
                                                }

                                                // If Download Only not found, select the first radio button
                                                if (!foundDownloadOnly &amp;&amp; radioButtons.length &gt; 0) {
                                                    radioButtons[0].checked = true;
                                                    var price = parseFloat(radioButtons[0].getAttribute('data-price') || 0);
                                                    document.getElementById('totalAmount').textContent = price.toFixed(2);
                                                    document.getElementById('initialTotal').value = price.toFixed(2);
                                                }

                                                // Calculate total including any checked checkboxes
                                                var checkboxes = document.querySelectorAll('.addon-checkbox:checked');
                                                var total = parseFloat(document.getElementById('initialTotal').value || 0);

                                                for (var j = 0; j &lt; checkboxes.length; j++) {
                                                    var checkbox = checkboxes[j];
                                                    var checkboxPrice = parseFloat(checkbox.getAttribute('data-price') || 0);
                                                    total += checkboxPrice;
                                                }

                                                document.getElementById('totalAmount').textContent = total.toFixed(2);
                                            });
                                        </script>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script type="text/javascript">
                // Make sure jQuery is available
                (function(w, d) {
                    function waitForJQuery() {
                        if (typeof w.jQuery === 'undefined') {
                            // jQuery not loaded yet, wait and try again
                            console.log('Waiting for jQuery to load...');
                            setTimeout(waitForJQuery, 100);
                            return;
                        }

                        // jQuery is loaded, initialize our code
                        console.log('jQuery loaded, initializing addon selection');
                        var $ = w.jQuery;

                        // Now we can use jQuery safely
                    // Show/hide quantity input based on selection
                    $('.addon-checkbox, .addon-radio').change(function() {
                        const quantityInput = $(this).closest('.addon-item').find('.quantity-input');
                        if ($(this).is(':checked') &amp;&amp; $(this).data('per-dept')) {
                            quantityInput.slideDown();
                        } else {
                            quantityInput.slideUp();
                        }
                        updateTotal();
                    });

                    // Update total when quantity changes
                    $('.addon-quantity').change(function() {
                        updateTotal();
                    });

                    // Calculate total amount
                    function updateTotal() {
                        let total = 0;

                        // Add selected radio button value (delivery option)
                        const selectedDelivery = $('.addon-radio:checked');
                        if (selectedDelivery.length &gt; 0) {
                            const price = parseFloat(selectedDelivery.data('price') || 0);
                            console.log('Selected delivery option:', selectedDelivery.data('name'), 'Price:', price);
                            const perDept = selectedDelivery.data('per-dept');
                            if (perDept) {
                                const quantity = parseInt(selectedDelivery.closest('.addon-item').find('.addon-quantity').val()) || 1;
                                console.log('Per dept delivery with quantity:', quantity);
                                total += price * quantity;
                            } else {
                                total += price;
                            }
                        }

                        // Add selected checkboxes (additional services)
                        $('.addon-checkbox:checked').each(function() {
                            const price = parseFloat($(this).data('price') || 0);
                            console.log('Checked addon:', $(this).data('name'), 'Price:', price);
                            const perDept = $(this).data('per-dept');
                            if (perDept) {
                                const quantity = parseInt($(this).closest('.addon-item').find('.addon-quantity').val()) || 1;
                                console.log('Per dept addon with quantity:', quantity);
                                total += price * quantity;
                            } else {
                                total += price;
                            }
                        });

                        console.log('Total calculated:', total);
                        $('#totalAmount').text(total.toFixed(2));
                    }

                    // Show quantity inputs for checked items with per-dept enabled
                    $('.addon-checkbox:checked, .addon-radio:checked').each(function() {
                        if ($(this).data('per-dept')) {
                            $(this).closest('.addon-item').find('.quantity-input').show();
                        }
                    });

                    // Make sure at least one delivery option is selected (Download Only should be default)
                    if ($('.addon-radio:checked').length === 0) {
                        // Find the Download Only radio and check it
                        $('.addon-radio').each(function() {
                            const addonName = $(this).data('name');
                            console.log('Checking delivery option:', addonName);
                            if (addonName &amp;&amp; addonName.indexOf('Download Only') !== -1) {
                                console.log('Found Download Only, checking it');
                                $(this).prop('checked', true);
                                return false; // break the loop
                            }
                        });
                    }

                    // Force check first delivery option if nothing else is working
                    if ($('.addon-radio:checked').length === 0) {
                        console.log('No delivery option selected, forcing first one');
                        $('.addon-radio').first().prop('checked', true);
                    }

                        // Initial total calculation
                        updateTotal();
                    }

                    // Start waiting for jQuery
                    waitForJQuery();
                })(window, document);
            </script>
        </t>
    </template>

    <!-- RTI Success Page -->
    <template id="rti_success" name="RTI Success">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <!-- FontAwesome CSS -->
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fa fa-check-circle text-success fa-5x mb-3"></i>
                                    <h2 class="mb-3">RTI Application Submitted Successfully!</h2>
                                    <p class="mb-3">Your RTI Application Number is: <strong><t t-esc="rti.name"/></strong></p>
                                    <p>You can track your application status from your dashboard.</p>
                                    <a href="/my/rti" class="btn btn-primary">Go to Dashboard</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- RTI Error Page -->
    <template id="rti_error" name="RTI Error">
        <t t-call="website.layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fa fa-exclamation-circle text-danger fa-5x mb-3"></i>
                                    <h2 class="mb-3">Error in RTI Application</h2>
                                    <p class="mb-3"><t t-esc="error"/></p>
                                    <a href="/rti/apply" class="btn btn-primary">Try Again</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="rti_error_template" name="RTI Error">
        <t t-call="website.layout">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body text-center">
                                <h2 class="card-title text-danger mb-4">Error Submitting RTI Application</h2>
                                <div class="alert alert-danger">
                                    <p><t t-esc="error"/></p>
                                </div>
                                <p class="mt-4">
                                    We apologize for the inconvenience. Please try again or contact support if the problem persists.
                                </p>
                                <div class="mt-4">
                                    <a href="javascript:history.back()" class="btn btn-secondary">Go Back</a>
                                    <a href="/" class="btn btn-primary ml-2">Home</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
    <template id="payment_form" name="RTI Payment">
        <t t-call="website.layout">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body">
                                <h2 class="text-center mb-4">Payment Details</h2>

                                <!-- Order Summary -->
                                <div class="order-summary mb-4">
                                    <h4>Order Summary</h4>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Item</th>
                                                    <th>Quantity</th>
                                                    <th class="text-end">Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="addons" t-as="addon">
                                                    <tr>
                                                        <td><span t-field="addon.name"/></td>
                                                        <td>
                                                            <t t-set="qty" t-value="addon_quantities.get(addon.id, 1)"/>
                                                            <span t-esc="qty"/>
                                                        </td>
                                                        <td class="text-end">
                                                            ₹<span t-esc="'%.2f' % (addon.cost * addon_quantities.get(addon.id, 1))"/>
                                                        </td>
                                                    </tr>
                                                </t>
                                                <tr class="table-active">
                                                    <td colspan="2"><strong>Total Amount</strong></td>
                                                    <td class="text-end"><strong>₹<span t-esc="'%.2f' % total_amount"/></strong></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Payment Form -->
                                <form id="paymentForm" t-att-action="'/rti/payment/process/%s' % payment.id" method="POST">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <div class="mb-3">
                                        <label class="form-label">Payment Method</label>
                                        <div class="payment-methods">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="payment_method" id="upi" value="upi" checked="checked"/>
                                                <label class="form-check-label" for="upi">UPI</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="payment_method" id="card" value="card"/>
                                                <label class="form-check-label" for="card">Credit/Debit Card</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="payment_method" id="netbanking" value="netbanking"/>
                                                <label class="form-check-label" for="netbanking">Net Banking</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="upiSection" class="payment-section">
                                        <div class="mb-3">
                                            <label for="upiId" class="form-label">UPI ID</label>
                                            <input type="text" class="form-control" id="upiId" name="upi_id" placeholder="Enter your UPI ID"/>
                                        </div>
                                    </div>

                                    <div id="cardSection" class="payment-section" style="display: none;">
                                        <div class="mb-3">
                                            <label for="cardNumber" class="form-label">Card Number</label>
                                            <input type="text" class="form-control" id="cardNumber" name="card_number" placeholder="1234 5678 9012 3456"/>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="expiryDate" class="form-label">Expiry Date</label>
                                                <input type="text" class="form-control" id="expiryDate" name="expiry_date" placeholder="MM/YY"/>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="cvv" class="form-label">CVV</label>
                                                <input type="password" class="form-control" id="cvv" name="cvv" placeholder="123"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="netbankingSection" class="payment-section" style="display: none;">
                                        <div class="mb-3">
                                            <label for="bank" class="form-label">Select Bank</label>
                                            <select class="form-select" id="bank" name="bank">
                                                <option value="">Choose your bank</option>
                                                <option value="sbi">State Bank of India</option>
                                                <option value="hdfc">HDFC Bank</option>
                                                <option value="icici">ICICI Bank</option>
                                                <option value="axis">Axis Bank</option>
                                                <option value="other">Other Banks</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mt-4 text-center">
                                        <button type="submit" class="btn btn-primary">Pay ₹<span t-esc="'%.2f' % total_amount"/></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script type="text/javascript">
                $(document).ready(function() {
                    // Handle payment method selection
                    $('input[name="payment_method"]').change(function() {
                        $('.payment-section').hide();
                        $('#' + $(this).val() + 'Section').show();
                    });

                    // Basic form validation
                    $('#paymentForm').on('submit', function(e) {
                        const method = $('input[name="payment_method"]:checked').val();
                        let isValid = true;

                        if (method === 'upi') {
                            const upiId = $('#upiId').val();
                            if (!upiId || !upiId.includes('@')) {
                                alert('Please enter a valid UPI ID');
                                isValid = false;
                            }
                        } else if (method === 'card') {
                            const cardNumber = $('#cardNumber').val().replace(/\s/g, '');
                            const expiry = $('#expiryDate').val();
                            const cvv = $('#cvv').val();

                            if (!/^\d{16}$/.test(cardNumber)) {
                                alert('Please enter a valid 16-digit card number');
                                isValid = false;
                            }
                            if (!/^\d{2}\/\d{2}$/.test(expiry)) {
                                alert('Please enter expiry date in MM/YY format');
                                isValid = false;
                            }
                            if (!/^\d{3}$/.test(cvv)) {
                                alert('Please enter a valid 3-digit CVV');
                                isValid = false;
                            }
                        } else if (method === 'netbanking') {
                            if (!$('#bank').val()) {
                                alert('Please select a bank');
                                isValid = false;
                            }
                        }

                        if (!isValid) {
                            e.preventDefault();
                        }
                    });

                    // Format card number with spaces
                    $('#cardNumber').on('input', function() {
                        let value = $(this).val().replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                        const matches = value.match(/\d{4,16}/g);
                        const match = matches &amp;&amp; matches[0] || '';
                        const parts = [];

                        for (let i = 0, len = match.length; i &lt; len; i += 4) {
                            parts.push(match.substring(i, i + 4));
                        }

                        if (parts.length) {
                            $(this).val(parts.join(' '));
                        }
                    });

                    // Format expiry date
                    $('#expiryDate').on('input', function() {
                        let value = $(this).val().replace(/\D/g, '');
                        if (value.length &gt;= 2) {
                            value = value.substring(0, 2) + '/' + value.substring(2, 4);
                        }
                        $(this).val(value);
                    });
                });
            </script>
        </t>
    </template>

    <template id="payment_success" name="Payment Success">
        <t t-call="website.layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="mb-4">
                                    <i class="fa fa-check-circle text-success" style="font-size: 64px;"></i>
                                </div>
                                <h2 class="card-title text-success mb-4">Payment Successful!</h2>

                                <!-- Debug information (remove in production) -->
                                <div class="alert alert-info" style="font-size: 12px; text-align: left;">
                                    <strong>Debug Info:</strong><br/>
                                    Payment Object: <span t-esc="payment"/><br/>
                                    Payment ID: <span t-esc="payment.id if payment else 'None'"/><br/>
                                    Transaction Object: <span t-esc="transaction"/><br/>
                                    Razorpay Payment ID: <span t-esc="razorpay_payment_id"/><br/>
                                </div>
                                <div class="alert alert-success">
                                    <p><strong>RTI Application: </strong><span t-field="rti.name"/></p>
                                    <p><strong>Amount Paid: </strong>
                                        <t t-if="payment and payment.amount">₹<span t-field="payment.amount"/></t>
                                        <t t-elif="transaction and transaction.amount">₹<span t-esc="transaction.amount"/></t>
                                        <t t-else>N/A</t>
                                    </p>
                                    <p><strong>Transaction ID: </strong>
                                        <t t-if="payment and payment.transaction_id"><span t-field="payment.transaction_id"/></t>
                                        <t t-elif="razorpay_payment_id"><span t-esc="razorpay_payment_id"/></t>
                                        <t t-elif="transaction and transaction.provider_reference"><span t-esc="transaction.provider_reference"/></t>
                                        <t t-else>N/A</t>
                                    </p>
                                    <p><strong>Payment Date: </strong>
                                        <t t-if="payment and payment.payment_date"><span t-field="payment.payment_date"/></t>
                                        <t t-elif="transaction and transaction.write_date"><span t-field="transaction.write_date"/></t>
                                        <t t-else>N/A</t>
                                    </p>
                                </div>
                                <p class="mt-4">
                                    Thank you for your payment. Your RTI application process is now complete.
                                    You can track your application status in your dashboard.
                                </p>
                                <div class="mt-4">
                                    <a href="/my/rti" class="btn btn-primary">View My Applications</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Payment Error Template -->
    <template id="payment_error" name="Payment Error">
        <t t-call="website.layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="mb-4">
                                    <i class="fa fa-times-circle text-danger" style="font-size: 64px;"></i>
                                </div>
                                <h2 class="card-title text-danger mb-4">Payment Failed</h2>
                                <div class="alert alert-danger">
                                    <p t-esc="error"/>
                                </div>
                                <p class="mt-4">
                                    We were unable to process your payment. Please try again or contact support if the problem persists.
                                </p>
                                <div class="mt-4">
                                    <a href="/my/rti" class="btn btn-secondary me-2">My Applications</a>
                                    <button onclick="window.history.back()" class="btn btn-primary">Try Again</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>