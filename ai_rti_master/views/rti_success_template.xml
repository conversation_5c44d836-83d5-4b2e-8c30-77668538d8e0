<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="rti_success_template" name="RTI Success Page">
        <t t-call="website.layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <div id="wrap" class="oe_structure">
                <div class="container py-5">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card shadow">
                                <div class="card-body">
                                    <h2 class="text-center text-success mb-4">
                                        <i class="fa fa-check-circle"></i> RTI Application Submitted Successfully
                                    </h2>
                                    <p class="text-center mb-4">Your RTI application has been submitted. Application number: <strong t-field="rti.name"/></p>

                                    <div class="mt-4">
                                        <h4 class="mb-3">Payment</h4>
                                        <form t-attf-action="/rti/payment/#{rti.id}" method="POST" id="addon-form">
                                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                            <input type="hidden" name="submitted" value="true"/>

                                            <!-- <div class="addon-list">
                                                <t t-foreach="addons" t-as="addon">
                                                    <div class="card mb-3">
                                                        <div class="card-body">
                                                            <div class="form-check d-flex justify-content-between align-items-center">
                                                                <div>
                                                                    <input t-if="addon.name in ['Download Only', 'Download and Delivery', 'Download, Delivery and Post']"
                                                                           type="radio"
                                                                           t-attf-class="form-check-input addon-radio"
                                                                           t-attf-id="addon_#{addon.id}"
                                                                           name="delivery_option"
                                                                           t-att-value="addon.id"
                                                                           t-att-checked="addon.name == 'Download Only'"/>
                                                                <input t-if="addon.name not in ['Download Only', 'Download and Delivery', 'Download, Delivery and Post']"
                                                                           type="checkbox"
                                                                           t-attf-class="form-check-input addon-checkbox"
                                                                           t-attf-id="addon_#{addon.id}"
                                                                           t-attf-name="addon_#{addon.id}"
                                                                           t-att-value="addon.id"/>
                                                                    <label t-attf-class="form-check-label" t-attf-for="addon_#{addon.id}">
                                                                        <span t-field="addon.name"/>
                                                                    </label>
                                                                    <p class="text-muted mb-0 mt-1" t-field="addon.description"/>
                                                                </div>
                                                                <div class="addon-price">
                                                                    <span class="h5 mb-0">₹<t t-esc="addon.price"/></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </t>
                                            </div> -->

                                            <!-- <div class="total-section mt-4 p-3 bg-light rounded">
                                                <div class="d-flex justify-content-between">
                                                    <h5>Total Amount:</h5>
                                                    <h5>₹<span id="total-amount">0</span></h5>
                                                </div>
                                            </div> -->

                                            <div class="text-center mt-4">
                                                <button type="submit" class="btn btn-primary btn-lg" id="proceed-payment">
                                                    Proceed to Payment
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const addonCheckboxes = document.querySelectorAll('.addon-checkbox');
                    const addonRadios = document.querySelectorAll('.addon-radio');
                    const totalAmount = document.getElementById('total-amount');
                    const form = document.getElementById('addon-form');
                    const submitButton = document.getElementById('proceed-payment');

                    function updateTotal() {
                        let total = 0;

                        // Add selected radio button value (delivery option)
                        const selectedDelivery = document.querySelector('.addon-radio:checked');
                        if (selectedDelivery) {
                            const priceElement = selectedDelivery.closest('.card-body').querySelector('.addon-price');
                            const price = parseInt(priceElement.textContent.replace('₹', ''));
                            total += price;
                        }

                        // Add selected checkboxes (additional services)
                        addonCheckboxes.forEach(checkbox => {
                            if (checkbox.checked) {
                                const priceElement = checkbox.closest('.card-body').querySelector('.addon-price');
                                const price = parseInt(priceElement.textContent.replace('₹', ''));
                                total += price;
                            }
                        });

                        totalAmount.textContent = total;
                    }

                    // Add event listeners to all inputs
                    addonCheckboxes.forEach(checkbox => {
                        checkbox.addEventListener('change', updateTotal);
                    });

                    addonRadios.forEach(radio => {
                        radio.addEventListener('change', updateTotal);
                    });

                    // Make sure Download Only is selected by default
                    if (!document.querySelector('.addon-radio:checked')) {
                        const downloadOnlyRadio = Array.from(addonRadios).find(radio => {
                            return radio.closest('.card-body').textContent.includes('Download Only');
                        });
                        if (downloadOnlyRadio) {
                            downloadOnlyRadio.checked = true;
                        } else if (addonRadios.length > 0) {
                            addonRadios[0].checked = true;
                        }
                    }

                    // Initialize total with default selection
                    updateTotal();

                    form.addEventListener('submit', function(e) {
                        e.preventDefault();

                        // Check if a delivery option is selected
                        const hasDeliveryOption = document.querySelector('.addon-radio:checked');
                        if (!hasDeliveryOption) {
                            alert('Please select a delivery option to proceed.');
                            return;
                        }

                        // Disable submit button to prevent double submission
                        submitButton.disabled = true;
                        submitButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...';

                        // Submit the form
                        this.submit();
                    });
                });
            </script>
        </t>
    </template>
</odoo>
