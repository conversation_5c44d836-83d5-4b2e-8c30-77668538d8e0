<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- RTI Portal Templates -->
    <template id="rti_portal_code" inherit_id="portal.portal_my_home" name="Custom RTI Portal Section">
    <xpath expr="//div[@class='o_portal_docs row g-2']" position="inside">
        <div class="o_portal_category row g-2 mt-3" id="portal_rti_category">
            <div class="o_portal_index_card col-md-6">
                <a href="/my/rti" title="RTI"
                   class="d-flex justify-content-start gap-2 gap-md-3 align-items-center py-3 pe-2 px-md-3 h-100 rounded text-decoration-none text-reset text-bg-light">
                    <div class="o_portal_icon align-self-start">
                        <img src="/ai_rti_master/static/src/img/rti_icon.svg" loading="lazy" alt="RTI Icon"/>
                    </div>
                    <div>
                        <h5 class="mt-0 mb-1">RTI</h5>
                        <p class="mb-0 text-muted">Request and manage your RTI applications</p>
                    </div>
                </a>
            </div>
        </div>
    </xpath>
</template>

<!-- Template for Breadcrumb Navigation in RTI -->
<template id="portal_layout" name="Portal Layout: RTI Services" inherit_id="portal.portal_breadcrumbs" priority="40">
    <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
        <!-- Breadcrumb for RTI Listing -->
        <li t-if="page_name == 'rti_listing'" class="breadcrumb-item active">
            RTI
        </li>
        <!-- Breadcrumb for Detailed RTI Page -->
        <li t-if="page_name == 'rti_detail'" class="breadcrumb-item">
            <a t-attf-href="/my/rti">RTI</a>
        </li>
        <!-- Active breadcrumb for specific RTI -->
        <li t-if="page_name == 'rti_detail'" class="breadcrumb-item active text-truncate col-8 col-lg-10">
            <t t-esc="rti.name"/>
        </li>
    </xpath>
</template>


    <!-- RTI Applications List -->
    <template id="portal_my_rti" name="My RTI Applications">
        <t t-call="portal.portal_layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">RTI Applications</t>
            </t>
            <t t-if="not applications">
                <div class="alert alert-info" role="alert">
                    There are currently no RTI applications.
                </div>
            </t>
            <t t-if="applications" t-call="portal.portal_table">
                <thead>
                    <tr>
                        <th>Application Number</th>
                        <th>Submission Date</th>
                        <th>State</th>
                        <th>Department</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <t t-foreach="applications" t-as="application">
                        <tr>
                            <td>
                                <a t-attf-href="/my/rti/#{application.id}">
                                    <t t-esc="application.name"/>
                                </a>
                            </td>
                            <td><span t-field="application.create_date"/></td>
                            <td><span t-field="application.rti_state.name"/></td>
                            <td>
                                <t t-foreach="application.x_department_ids" t-as="dept">
                                    <div><span t-field="dept.name"/></div>
                                </t>
                            </td>
                            <td>
                                <t t-set="status_mapping" t-value="{
                                    'draft': ['secondary', 'Draft'],
                                    'submitted': ['info', 'Submitted'],
                                    'payment': ['warning', 'Payment Pending'],
                                    'paid': ['success', 'Paid'],
                                    'processing': ['primary', 'Processing'],
                                    'completed': ['success', 'Completed'],
                                    'rejected': ['danger', 'Rejected']
                                }"/>
                                <span t-if="application.state" t-attf-class="badge badge-pill badge-#{status_mapping.get(application.state, ['secondary'])[0]}">
                                    <t t-esc="status_mapping.get(application.state, ['', 'Unknown'])[1]"/>
                                </span>
                            </td>
                        </tr>
                    </t>
                </tbody>
            </t>
        </t>
    </template>

    <!-- RTI Application View -->
    <template id="portal_my_rti_application" name="RTI Application">
        <t t-call="portal.portal_layout">
            <t t-set="head">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"/>
            </t>
            <div class="container">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2>RTI Application <small t-esc="rti.name"/></h2>
                            <div class="o_portal_status">
                                <t t-set="status_mapping" t-value="{
                                    'draft': ['secondary', 'Draft'],
                                    'submitted': ['info', 'Submitted'],
                                    'payment': ['warning', 'Payment Pending'],
                                    'paid': ['success', 'Paid'],
                                    'processing': ['primary', 'Processing'],
                                    'completed': ['success', 'Completed'],
                                    'rejected': ['danger', 'Rejected']
                                }"/>
                                <span t-if="rti.state" t-attf-class="badge badge-pill badge-#{status_mapping.get(rti.state, ['secondary'])[0]}">
                                    <t t-esc="status_mapping.get(rti.state, ['', 'Unknown'])[1]"/>
                                </span>
                            </div>
                        </div>

                        <!-- Application Details -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4>Application Details</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Application Number:</strong> <span t-esc="rti.name"/></p>
                                        <p><strong>Status:</strong> <span t-esc="rti.state"/></p>
                                        <p><strong>RTI State:</strong> <span t-field="rti.rti_state.name"/></p>

                                        <!-- Personal Information -->
                                        <h5 class="mt-4">Personal Information</h5>
                                        <p><strong>First Name:</strong> <span t-esc="rti.x_first_name"/></p>
                                        <p t-if="rti.x_middle_name"><strong>Middle Name:</strong> <span t-esc="rti.x_middle_name"/></p>
                                        <p><strong>Surname:</strong> <span t-esc="rti.x_surname"/></p>
                                        <p><strong>Mobile:</strong> <span t-esc="rti.x_mobile_number"/></p>
                                        <p><strong>Email:</strong> <span t-esc="rti.x_email"/></p>
                                        <p t-if="rti.x_whatsapp_number"><strong>WhatsApp:</strong> <span t-esc="rti.x_whatsapp_number"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <!-- Address Information -->
                                        <h5>Address Information</h5>
                                        <p><strong>Flat/House Number:</strong> <span t-esc="rti.x_flat_no"/></p>
                                        <p><strong>Street/Area:</strong> <span t-esc="rti.x_street"/></p>
                                        <p t-if="rti.x_landmark"><strong>Landmark:</strong> <span t-esc="rti.x_landmark"/></p>
                                        <p><strong>Pincode:</strong> <span t-esc="rti.x_pincode"/></p>
                                        <p><strong>State:</strong> <span t-field="rti.x_state.name"/></p>
                                        <p t-if="rti.x_district"><strong>District:</strong> <span t-field="rti.x_district.name"/></p>
                                        <p t-if="rti.x_taluka"><strong>Taluka:</strong> <span t-field="rti.x_taluka.name"/></p>
                                        <p t-if="rti.x_village"><strong>Village:</strong> <span t-field="rti.x_village.name"/></p>
                                    </div>
                                </div>

                                <!-- Department Information -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5>Department Information</h5>
                                        <div t-if="rti.x_department_ids">
                                            <p><strong>Selected Departments:</strong></p>
                                            <ul class="list-unstyled">
                                                <t t-foreach="rti.x_department_ids" t-as="dept">
                                                    <li><span t-field="dept.name"/></li>
                                                </t>
                                            </ul>
                                        </div>

                                        <div t-if="rti.x_manual_department">
                                            <p><strong>Manual Department Name:</strong> <span t-esc="rti.x_manual_department_name"/></p>
                                            <!-- <p><strong>Department Category:</strong> <span t-field="rti.x_manual_department_category_id.name"/></p> -->
                                            <!-- <p><strong>Department Level:</strong> <span t-esc="rti.x_manual_department_level"/></p> -->
                                            <p t-if="rti.x_manual_department_address"><strong>Department Address:</strong> <span t-esc="rti.x_manual_department_address"/></p>
                                        </div>
                                    </div>
                                </div>

                                <!-- RTI Details -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5>RTI Information</h5>
                                        <p><strong>RTI Details:</strong> <span t-esc="rti.x_rti_details"/></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Addon Selection -->
                        <div t-if="rti.state in ['draft', 'submitted']" class="card mt-4">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">Additional Services</h4>
                            </div>
                            <div class="card-body">
                                <form t-attf-action="/my/rti/#{rti.id}/payment" method="post" id="addon-form">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                    <input type="hidden" name="submitted" value="1"/>

                                    <div class="row">
                                        <t t-foreach="addons" t-as="addon">
                                            <div class="col-md-6 mb-3">
                                                <div class="form-check mb-3 addon-item">
                                                    <!-- Radio buttons for delivery options -->
                                                    <input t-if="addon.name in ['Download Only', 'Download and Delivery', 'Download, Delivery and Post']"
                                                           type="radio"
                                                           class="form-check-input addon-radio"
                                                           t-attf-id="addon_#{addon.id}"
                                                           name="delivery_option"
                                                           t-att-value="addon.id"
                                                           t-att-data-price="addon.list_price"
                                                           t-att-data-per-dept="addon.allow_multiple_qty and 1 or 0"
                                                           t-att-checked="addon.name == 'Download Only' and 'checked' or None"
                                                           t-att-data-name="addon.name"/>
                                                    <!-- Checkboxes for additional services -->
                                                    <input t-if="addon.name not in ['Download Only', 'Download and Delivery', 'Download, Delivery and Post']"
                                                           type="checkbox"
                                                           class="form-check-input addon-checkbox"
                                                           t-attf-id="addon_#{addon.id}"
                                                           t-attf-name="addon_#{addon.id}"
                                                           t-att-value="addon.id"
                                                           t-att-data-price="addon.list_price"
                                                           t-att-data-per-dept="addon.allow_multiple_qty and 1 or 0"
                                                           t-att-data-name="addon.name"
                                                           t-att-checked="addon.id in selected_addon_ids"/>
                                                    <label class="form-check-label" t-attf-for="addon_#{addon.id}">
                                                        <span t-esc="addon.name"/> - ₹<span t-esc="addon.list_price"/>
                                                        <t t-if="addon.allow_multiple_qty"> (per department)</t>
                                                        <p class="text-muted mb-0 mt-1" t-esc="addon.description"/>
                                                    </label>
                                                </div>
                                                <div t-if="addon.is_rti_addon and addon.allow_multiple_qty and addon.id in selected_addon_ids"
                                                     class="quantity-input mt-2" style="display: none;">
                                                    <label t-attf-for="qty_#{addon.id}" class="form-label">Quantity:</label>
                                                    <input type="number"
                                                           class="form-control form-control-sm addon-quantity"
                                                           t-attf-id="qty_#{addon.id}"
                                                           t-attf-name="qty_#{addon.id}"
                                                           min="1"
                                                           value="1"/>
                                                </div>
                                            </div>
                                        </t>
                                    </div>

                                    <div class="text-right mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            Proceed to Payment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4>Payment</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="rti.payment_status == 'pending'">
                                    <p class="text-muted mb-4">Please proceed with the payment to submit your RTI application:</p>
                                    <div class="text-center">
                                        <!-- <a t-att-href="'/my/rti/%s/payment' % rti.id" class="btn btn-primary"> -->
                                            Payment Pending
                                        <!-- </a> -->
                                    </div>
                                </t>
                                <t t-elif="rti.payment_status == 'paid'">
                                    <div class="alert alert-success">
                                        <i class="fa fa-check-circle"/> Payment completed successfully
                                    </div>
                                    <p>Transaction Reference: <span t-field="rti.payment_transaction_id.reference"/></p>

                                    <!-- PDF Download Button -->
                                    <div class="text-center mt-3">
                                        <a t-att-href="'/my/rti/%s/pdf' % rti.id" class="btn btn-primary">
                                            <i class="fa fa-download"/> Download RTI Application PDF
                                        </a>
                                    </div>
                                </t>
                                <t t-elif="rti.payment_status == 'failed'">
                                    <div class="alert alert-danger">
                                        <i class="fa fa-times-circle"/> Payment failed. Please try again.
                                    </div>
                                    <div class="text-center mt-3">
                                        <a t-att-href="'/my/rti/%s/payment' % rti.id" class="btn btn-primary">
                                            Retry Payment
                                        </a>
                                    </div>
                                </t>
                            </div>
                        </div>

                        <!-- History -->
                        <div t-if="history" class="card mt-4">
                            <div class="card-header">
                                <h4 class="mb-0">Application History</h4>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <t t-foreach="history" t-as="record">
                                        <div class="timeline-item">
                                            <span class="timeline-date"><t t-esc="record.create_date"/></span>
                                            <span class="timeline-content"><t t-esc="record.name"/></span>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Demo Payment Template -->
    <template id="rti_payment_demo" name="RTI Payment Demo">
        <t t-call="portal.portal_layout">
            <div class="container py-4">
                <div class="row">
                    <div class="col-12">
                        <h2>Payment Details for RTI Application <small t-esc="rti.name"/></h2>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h4>Amount to Pay</h4>
                            </div>
                            <div class="card-body">
                                <div class="total-section p-3 bg-light rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Total Amount:</h5>
                                        <h5 class="mb-0">₹<t t-esc="amount"/></h5>
                                    </div>
                                </div>

                                <div class="selected-addons mt-4" t-if="rti.product_template_addon_quantity_ids">
                                    <h6>Selected Additional Services:</h6>
                                    <ul class="list-unstyled">
                                        <t t-foreach="rti.product_template_addon_quantity_ids" t-as="addon_qty">
                                            <li class="mb-2">
                                                <span t-esc="addon_qty.addon_id.name"/> - ₹<span t-esc="addon_qty.addon_id.list_price"/>
                                                <t t-if="addon_qty.quantity > 1">
                                                    (Quantity: <span t-esc="addon_qty.quantity"/>)
                                                </t>
                                            </li>
                                        </t>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h4>Bank Transfer Details</h4>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-4">Please transfer the amount to the following bank account:</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th class="pl-0">Bank Name:</th>
                                                <td><t t-esc="bank_name"/></td>
                                            </tr>
                                            <tr>
                                                <th class="pl-0">Account Number:</th>
                                                <td><t t-esc="account_number"/></td>
                                            </tr>
                                            <tr>
                                                <th class="pl-0">IFSC Code:</th>
                                                <td><t t-esc="ifsc_code"/></td>
                                            </tr>
                                            <tr>
                                                <th class="pl-0">Account Holder:</th>
                                                <td><t t-esc="account_holder"/></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <form t-attf-action="/my/rti/#{rti.id}/confirm_payment" method="post" class="mt-4">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <div class="form-group">
                                        <label for="payment_reference">Payment Reference Number:</label>
                                        <input type="text" class="form-control" name="payment_reference" id="payment_reference"
                                               placeholder="Enter your payment reference number" required="required"/>
                                        <small class="form-text text-muted">Please enter the reference number from your bank transfer.</small>
                                    </div>

                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            Confirm Payment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
