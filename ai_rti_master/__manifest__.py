{
    'name': 'AI RTI Master',
    'version': '1.0',
    'category': 'Document Management',
    'summary': 'RTI Application Management System',
    'description': """
        AI RTI Master Module for managing Right to Information (RTI) applications
        with multilingual support and document generation.

        Features:
        - Multilingual RTI document templates (Gujarati, Hindi, English)
        - State-based language selection
        - Department categorization (State/Board/Corporation)
        - Multiple addon types with per-department quantities
        - Comprehensive content management (descriptions, images, tips, etc.)
    """,
    'author': 'Arihant Ai',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'mail',
        'portal',
        'web',
        'website',
        'payment',
        'website_payment',
        'ai_location',
        'payment_razorpay',
       
    ],
    'data': [
        'security/rti_security.xml',
        'security/ir.model.access.csv',
        'data/rti_sequence.xml',
        'data/payment_sequence.xml',
        'data/department_category_data.xml',
        'data/rti_master_data.xml',
        'data/rti_data.xml',
        'data/rti_demo.xml',
        'data/mail_templates.xml',
        'data/website_data.xml',
        'views/assets.xml',
        'views/rti_views.xml',
        'views/rti_content_views.xml',
        'views/rti_master_views.xml',
        'views/portal_templates.xml',
        'views/website_templates.xml',
        'views/payment_templates.xml',
        'views/payment_provider_not_found.xml',
        'views/razorpay_payment.xml',
        'reports/rti_document_templates.xml',
        'data/website_pages.xml',
        'data/rti_addon_type_data.xml',
    ],

    'assets': {
        'web.report_assets_common': [
            'ai_rti_master/static/src/css/report_fonts.css'
        ],
    },
    'application': True,
    'installable': True,
    'auto_install': False,

    'license': 'LGPL-3',
}
