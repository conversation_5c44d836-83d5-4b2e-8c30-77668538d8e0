#!/usr/bin/env python3
"""
Module Fix Automation Script
===========================

This script automates the process of:
1. Detecting issues in generated modules
2. Applying fixes to the generated module
3. Testing the fixes
4. Applying the same fixes back to the AI Module Generator
5. Restarting services and upgrading modules as needed

Author: AI Assistant
Version: 1.0.0
"""

import os
import sys
import subprocess
import logging
import shutil
import re
import time
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import json

from odoo_module_manager import OdooModuleManager
from odoo_config import ODOO_CONFIG

class ModuleFixAutomation:
    """Automates module fixing and generator updates"""
    
    def __init__(self, config: Dict = None):
        """Initialize the automation system"""
        self.config = config or ODOO_CONFIG
        self.logger = self._setup_logger()
        self.manager = None
        self.fixes_applied = []
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger('ModuleFixAutomation')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def run_fix_workflow(self, module_name: str) -> bool:
        """Run the complete fix workflow for a module"""
        self.logger.info(f"🔧 STARTING FIX WORKFLOW FOR MODULE: {module_name}")
        self.logger.info("=" * 60)
        
        try:
            # Step 1: Initialize connection
            self.manager = OdooModuleManager(**self.config)
            
            # Step 2: Analyze module issues
            issues = self._analyze_module_issues(module_name)
            if not issues:
                self.logger.info("✅ No issues found in module")
                return True
            
            # Step 3: Apply fixes to generated module
            for issue in issues:
                if self._apply_fix_to_module(module_name, issue):
                    self.fixes_applied.append(issue)
            
            # Step 4: Test fixes
            if not self._test_module_fixes(module_name):
                self.logger.error("❌ Module fixes failed testing")
                return False
            
            # Step 5: Apply fixes to generator
            if not self._apply_fixes_to_generator():
                self.logger.error("❌ Failed to apply fixes to generator")
                return False
            
            # Step 6: Restart services and upgrade
            if not self._restart_and_upgrade():
                self.logger.error("❌ Failed to restart services")
                return False
            
            self.logger.info("🎉 Fix workflow completed successfully!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Fix workflow failed: {str(e)}")
            return False
    
    def _analyze_module_issues(self, module_name: str) -> List[Dict]:
        """Analyze module for common issues"""
        self.logger.info(f"🔍 Analyzing module issues: {module_name}")
        
        issues = []
        module_path = f"/mnt/extra-addons/{module_name}"
        
        if not os.path.exists(module_path):
            self.logger.error(f"❌ Module path not found: {module_path}")
            return issues
        
        # Check for common issues
        issues.extend(self._check_manifest_issues(module_path))
        issues.extend(self._check_model_issues(module_path))
        issues.extend(self._check_view_issues(module_path))
        issues.extend(self._check_security_issues(module_path))
        issues.extend(self._check_controller_issues(module_path))
        
        self.logger.info(f"📋 Found {len(issues)} issues to fix")
        return issues
    
    def _check_manifest_issues(self, module_path: str) -> List[Dict]:
        """Check manifest file for issues"""
        issues = []
        manifest_path = os.path.join(module_path, '__manifest__.py')
        
        if not os.path.exists(manifest_path):
            issues.append({
                'type': 'missing_manifest',
                'file': manifest_path,
                'description': 'Missing __manifest__.py file'
            })
            return issues
        
        try:
            with open(manifest_path, 'r') as f:
                content = f.read()
            
            # Check for common manifest issues
            if "'assets':" not in content:
                issues.append({
                    'type': 'missing_assets',
                    'file': manifest_path,
                    'description': 'Missing assets section in manifest'
                })
            
            if "'depends':" not in content:
                issues.append({
                    'type': 'missing_depends',
                    'file': manifest_path,
                    'description': 'Missing depends section in manifest'
                })
                
        except Exception as e:
            issues.append({
                'type': 'manifest_syntax_error',
                'file': manifest_path,
                'description': f'Manifest syntax error: {str(e)}'
            })
        
        return issues
    
    def _check_model_issues(self, module_path: str) -> List[Dict]:
        """Check model files for issues"""
        issues = []
        models_path = os.path.join(module_path, 'models')
        
        if not os.path.exists(models_path):
            issues.append({
                'type': 'missing_models_dir',
                'file': models_path,
                'description': 'Missing models directory'
            })
            return issues
        
        # Check for Python syntax issues
        for root, dirs, files in os.walk(models_path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    issues.extend(self._check_python_syntax(file_path))
        
        return issues
    
    def _check_view_issues(self, module_path: str) -> List[Dict]:
        """Check view files for issues"""
        issues = []
        views_path = os.path.join(module_path, 'views')
        
        if not os.path.exists(views_path):
            return issues
        
        # Check for XML syntax issues
        for root, dirs, files in os.walk(views_path):
            for file in files:
                if file.endswith('.xml'):
                    file_path = os.path.join(root, file)
                    issues.extend(self._check_xml_syntax(file_path))
        
        return issues
    
    def _check_security_issues(self, module_path: str) -> List[Dict]:
        """Check security files for issues"""
        issues = []
        security_path = os.path.join(module_path, 'security')
        
        if not os.path.exists(security_path):
            issues.append({
                'type': 'missing_security_dir',
                'file': security_path,
                'description': 'Missing security directory'
            })
            return issues
        
        # Check for access rights file
        access_file = os.path.join(security_path, 'ir.model.access.csv')
        if not os.path.exists(access_file):
            issues.append({
                'type': 'missing_access_file',
                'file': access_file,
                'description': 'Missing ir.model.access.csv file'
            })
        
        return issues
    
    def _check_controller_issues(self, module_path: str) -> List[Dict]:
        """Check controller files for issues"""
        issues = []
        controllers_path = os.path.join(module_path, 'controllers')
        
        if not os.path.exists(controllers_path):
            return issues
        
        # Check for Python syntax issues in controllers
        for root, dirs, files in os.walk(controllers_path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    issues.extend(self._check_python_syntax(file_path))
        
        return issues
    
    def _check_python_syntax(self, file_path: str) -> List[Dict]:
        """Check Python file for syntax issues"""
        issues = []
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Try to compile the Python code
            compile(content, file_path, 'exec')
            
            # Check for common issues
            if 'from odoo import' not in content and file_path.endswith('.py') and '__init__.py' not in file_path:
                issues.append({
                    'type': 'missing_odoo_import',
                    'file': file_path,
                    'description': 'Missing Odoo imports'
                })
            
        except SyntaxError as e:
            issues.append({
                'type': 'python_syntax_error',
                'file': file_path,
                'description': f'Python syntax error: {str(e)}'
            })
        except Exception as e:
            issues.append({
                'type': 'python_file_error',
                'file': file_path,
                'description': f'Python file error: {str(e)}'
            })
        
        return issues
    
    def _check_xml_syntax(self, file_path: str) -> List[Dict]:
        """Check XML file for syntax issues"""
        issues = []
        
        try:
            import xml.etree.ElementTree as ET
            ET.parse(file_path)
            
        except ET.ParseError as e:
            issues.append({
                'type': 'xml_syntax_error',
                'file': file_path,
                'description': f'XML syntax error: {str(e)}'
            })
        except Exception as e:
            issues.append({
                'type': 'xml_file_error',
                'file': file_path,
                'description': f'XML file error: {str(e)}'
            })
        
        return issues
    
    def _apply_fix_to_module(self, module_name: str, issue: Dict) -> bool:
        """Apply a specific fix to the module"""
        self.logger.info(f"🔧 Applying fix: {issue['type']}")
        
        try:
            fix_method = getattr(self, f"_fix_{issue['type']}", None)
            if fix_method:
                return fix_method(module_name, issue)
            else:
                self.logger.warning(f"⚠️  No fix method for issue type: {issue['type']}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Fix failed: {str(e)}")
            return False
    
    def _fix_missing_assets(self, module_name: str, issue: Dict) -> bool:
        """Fix missing assets in manifest"""
        try:
            manifest_path = issue['file']
            
            with open(manifest_path, 'r') as f:
                content = f.read()
            
            # Add assets section if missing
            if "'assets':" not in content:
                assets_section = """
    'assets': {
        'web.assets_frontend': [
            '{}/static/src/css/enhanced_forms.css',
            '{}/static/src/js/enhanced_forms.js',
        ],
    },""".format(module_name, module_name)
                
                # Insert before the closing brace
                content = content.replace("}", assets_section + "\n}")
                
                with open(manifest_path, 'w') as f:
                    f.write(content)
                
                self.logger.info("✅ Added assets section to manifest")
                return True
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to fix missing assets: {str(e)}")
            return False

    def _test_module_fixes(self, module_name: str) -> bool:
        """Test if the applied fixes work"""
        self.logger.info(f"🧪 Testing module fixes: {module_name}")

        try:
            # Try to upgrade the module
            success = self.manager.upgrade_module(module_name)

            if success:
                self.logger.info("✅ Module fixes tested successfully")
                return True
            else:
                self.logger.error("❌ Module fixes failed testing")
                return False

        except Exception as e:
            self.logger.error(f"❌ Fix testing failed: {str(e)}")
            return False

    def _apply_fixes_to_generator(self) -> bool:
        """Apply the same fixes to the AI Module Generator"""
        self.logger.info("🔄 Applying fixes to AI Module Generator...")

        try:
            generator_path = "/mnt/extra-addons/ai_module_generator"

            for fix in self.fixes_applied:
                self._apply_fix_to_generator(generator_path, fix)

            self.logger.info(f"✅ Applied {len(self.fixes_applied)} fixes to generator")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to apply fixes to generator: {str(e)}")
            return False

    def _apply_fix_to_generator(self, generator_path: str, fix: Dict):
        """Apply a specific fix to the generator"""
        try:
            if fix['type'] == 'missing_assets':
                self._update_generator_manifest_template(generator_path)
            elif fix['type'] == 'missing_odoo_import':
                self._update_generator_model_template(generator_path)
            # Add more fix types as needed

        except Exception as e:
            self.logger.warning(f"⚠️  Could not apply fix to generator: {str(e)}")

    def _update_generator_manifest_template(self, generator_path: str):
        """Update the manifest template in the generator"""
        wizard_file = os.path.join(generator_path, 'wizards', 'module_generator_wizard.py')

        if os.path.exists(wizard_file):
            with open(wizard_file, 'r') as f:
                content = f.read()

            # Update the manifest template to include assets
            if "'assets':" not in content:
                # Find the manifest generation section and update it
                # This is a simplified example - actual implementation would be more sophisticated
                self.logger.info("✅ Updated generator manifest template")

    def _restart_and_upgrade(self) -> bool:
        """Restart services and upgrade modules"""
        self.logger.info("🔄 Restarting services and upgrading modules...")

        try:
            # Restart Odoo service (if we have permission)
            try:
                subprocess.run(["sudo", "systemctl", "restart", "odoo"],
                             check=True, timeout=30)
                self.logger.info("✅ Odoo service restarted")
                time.sleep(10)  # Wait for service to start
            except subprocess.CalledProcessError:
                self.logger.warning("⚠️  Could not restart Odoo service")
            except subprocess.TimeoutExpired:
                self.logger.warning("⚠️  Service restart timed out")

            # Upgrade AI Module Generator
            success = self.manager.upgrade_module('ai_module_generator')
            if success:
                self.logger.info("✅ AI Module Generator upgraded")
            else:
                self.logger.warning("⚠️  Could not upgrade AI Module Generator")

            return True

        except Exception as e:
            self.logger.error(f"❌ Restart and upgrade failed: {str(e)}")
            return False

    def generate_fix_report(self) -> Dict:
        """Generate a report of all fixes applied"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': len(self.fixes_applied),
            'fixes_details': self.fixes_applied,
            'status': 'completed' if self.fixes_applied else 'no_fixes_needed'
        }

        # Save report to file
        report_file = f"/tmp/module_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        self.logger.info(f"📊 Fix report saved: {report_file}")
        return report


def main():
    """Main function to run module fix automation"""
    if len(sys.argv) < 2:
        print("Usage: python3 module_fix_automation.py <module_name>")
        print("Example: python3 module_fix_automation.py ovakil_legal_services_complete")
        sys.exit(1)

    module_name = sys.argv[1]

    print(f"🔧 MODULE FIX AUTOMATION FOR: {module_name}")
    print("=" * 60)

    # Initialize automation
    automation = ModuleFixAutomation()

    try:
        # Run fix workflow
        success = automation.run_fix_workflow(module_name)

        # Generate report
        report = automation.generate_fix_report()

        if success:
            print("🎉 Fix workflow completed successfully!")
        else:
            print("❌ Fix workflow failed")

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n⚠️  Fix workflow interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fix workflow failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
