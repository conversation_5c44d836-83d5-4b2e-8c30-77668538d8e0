// Cart fix for the Add to Cart functionality
// Using vanilla JavaScript to avoid dependency issues
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    console.log("Cart fix loaded");

    // Simple translation function
    function _t(text) {
        return text; // In a real implementation, this would translate the text
    }

    // Initialize the cart fix
    console.log("Initializing Add to Cart fix");

    // Create a function to handle the Add to Cart button click
    function handleAddToCart(event) {
        console.log("Add to Cart button clicked");

        // Get the form
        const form = event.target.closest('form');
        if (!form) {
            console.error("Form not found");
            return;
        }

        // Get the product ID
        const productIdInput = form.querySelector('input[name="product_id"]');
        if (!productIdInput) {
            console.error("Product ID input not found");
            return;
        }

        const productId = productIdInput.value;
        console.log("Product ID:", productId);

        // Get the quantity
        const quantityInput = form.querySelector('input[name="add_qty"]');
        const quantity = quantityInput ? quantityInput.value : 1;
        console.log("Quantity:", quantity);

        // Get the warehouse ID and other data from the button's dataset
        const warehouseId = event.target.dataset.warehouseId;
        const shippingMethod = event.target.dataset.shippingMethod;
        const deliveryDate = event.target.dataset.deliveryDate;

        // Prevent the default form submission
        event.preventDefault();

        // Show loading indicator
        const originalText = event.target.innerHTML;
        event.target.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
        event.target.disabled = true;

        // Create form data
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('add_qty', quantity);

        if (warehouseId) formData.append('warehouse_id', warehouseId);
        if (shippingMethod) formData.append('shipping_method', shippingMethod);
        if (deliveryDate) formData.append('delivery_date', deliveryDate);

        // Add CSRF token if available
        const csrfToken = document.querySelector('input[name="csrf_token"]');
        if (csrfToken && csrfToken.value) formData.append('csrf_token', csrfToken.value);

        // Send the request to the JSON endpoint
        fetch('/shop/cart/update_json', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log("Cart update response:", data);

            // Reset the button
            event.target.innerHTML = originalText;
            event.target.disabled = false;

            // Create notification_info object with required properties
            const notification_info = {
                lines: data.lines || [],
                currency_id: data.currency_id,
                cart_quantity: data.cart_quantity || 1,
                warning: data.warning || null
            };

            try {
                // Create a proper notification_info object with all required properties
                const notificationInfo = {
                    title: 'Product added to your cart',
                    message: 'Product added to your cart',
                    sticky: false,
                    warning: data.warning || '',
                    lines: data.lines || [],
                    currency_id: data.currency_id || 1
                };

                // Update the cart quantity display
                const cartQuantityElements = document.querySelectorAll('.my_cart_quantity');
                if (cartQuantityElements.length && data.cart_quantity) {
                    cartQuantityElements.forEach(el => {
                        el.textContent = data.cart_quantity;
                    });
                }

                // Redirect to cart page
                console.log('Redirecting to cart page');
                window.location.href = '/shop/cart';
            } catch (e) {
                console.error('Error handling cart update:', e);
                // Fallback if anything fails
                window.location.href = '/shop/cart';
            }
        })
        .catch(error => {
            console.error('Error adding to cart:', error);

            // Reset the button
            event.target.innerHTML = originalText;
            event.target.disabled = false;

            // Show error message
            alert(_t('Error adding to cart. Please try again.'));
        });
        }

    // Function to find and patch the Add to Cart button
    function patchAddToCartButton() {
        const addToCartButton = document.querySelector('#add_to_cart, .a-submit');
        if (addToCartButton) {
            console.log("Found Add to Cart button, adding click event listener");

            // Remove existing event listeners
            const newButton = addToCartButton.cloneNode(true);
            addToCartButton.parentNode.replaceChild(newButton, addToCartButton);

            // Add our event listener
            newButton.addEventListener('click', handleAddToCart);

            console.log("Add to Cart button patched successfully");
        }
    }

    // Patch the button immediately if it exists
    patchAddToCartButton();

    // Also set up a MutationObserver to watch for the button being added to the DOM
    const observer = new MutationObserver(function(mutations) {
        for (const mutation of mutations) {
            if (mutation.type === 'childList' && mutation.addedNodes.length) {
                // Check if the Add to Cart button was added
                const addToCartButton = document.querySelector('#add_to_cart, .a-submit');
                if (addToCartButton) {
                    console.log("Add to Cart button added to DOM, patching it");
                    patchAddToCartButton();
                    observer.disconnect();
                    break;
                }
            }
        }
    });

    // Start observing the document body
    observer.observe(document.body, { childList: true, subtree: true });
});
