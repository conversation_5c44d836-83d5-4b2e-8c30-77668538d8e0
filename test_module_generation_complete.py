#!/usr/bin/env python3
"""
Complete test script to verify AI Module Generator fixes and generate a test module
"""

import os
import sys
import subprocess
import time
import shutil

def test_complete_module_generation():
    """Test complete module generation with One2many fields"""
    
    print("🧪 TESTING COMPLETE AI MODULE GENERATOR FUNCTIONALITY")
    print("=" * 70)
    
    # Test module name
    test_module_name = "test_one2many_complete"
    test_module_path = f"/mnt/extra-addons/{test_module_name}"
    
    print(f"📋 Test Module: {test_module_name}")
    print(f"📁 Test Path: {test_module_path}")
    
    # Check if test module already exists and remove it
    if os.path.exists(test_module_path):
        print(f"🗑️  Removing existing test module...")
        shutil.rmtree(test_module_path)
    
    # Create a test module manually to verify the structure
    print("\n🔧 Creating test module with One2many fields...")
    
    # Create module directory structure
    os.makedirs(test_module_path, exist_ok=True)
    os.makedirs(os.path.join(test_module_path, 'models'), exist_ok=True)
    os.makedirs(os.path.join(test_module_path, 'security'), exist_ok=True)
    os.makedirs(os.path.join(test_module_path, 'data'), exist_ok=True)
    os.makedirs(os.path.join(test_module_path, 'views'), exist_ok=True)
    
    # Create __manifest__.py
    manifest_content = """{
    'name': 'Test One2many Complete',
    'version': '********.0',
    'category': 'Test',
    'summary': 'Test module for One2many fields',
    'description': \"\"\"Test module to verify One2many field generation and related model creation\"\"\",
    'author': 'Oneclickvakil',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web', 'mail', 'portal'],
    'data': [
        'security/ir.model.access.csv',
        'views/views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
}"""
    
    with open(os.path.join(test_module_path, '__manifest__.py'), 'w') as f:
        f.write(manifest_content)
    
    # Create __init__.py
    init_content = "from . import models"
    with open(os.path.join(test_module_path, '__init__.py'), 'w') as f:
        f.write(init_content)
    
    # Create models/__init__.py
    models_init_content = """from . import test_main_model
from . import test_line_model
from . import test_note_model"""
    with open(os.path.join(test_module_path, 'models', '__init__.py'), 'w') as f:
        f.write(models_init_content)
    
    # Create main model with One2many fields
    main_model_content = """from odoo import api, fields, models, _


class TestLineModel(models.Model):
    \"\"\"Model for test lines\"\"\"
    _name = 'test.line.model'
    _description = 'Test Line Model'
    _order = 'sequence, id'

    name = fields.Char(string='Name', required=True)
    description = fields.Text(string='Description')
    sequence = fields.Integer(string='Sequence', default=10)
    test_main_id = fields.Many2one('test.main.model', 
                                  string='Test Main', ondelete='cascade')
    active = fields.Boolean(string='Active', default=True)


class TestNoteModel(models.Model):
    \"\"\"Model for test notes\"\"\"
    _name = 'test.note.model'
    _description = 'Test Note Model'
    _order = 'create_date desc'

    name = fields.Char(string='Subject', required=True)
    note = fields.Text(string='Note', required=True)
    test_main_id = fields.Many2one('test.main.model', 
                                  string='Test Main', ondelete='cascade')
    user_id = fields.Many2one('res.users', string='Created By', default=lambda self: self.env.user)
    date = fields.Datetime(string='Date', default=fields.Datetime.now)


class TestMainModel(models.Model):
    \"\"\"Main test model with One2many fields\"\"\"
    _name = 'test.main.model'
    _description = 'Test Main Model'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(string='Name', required=True, tracking=True)
    description = fields.Text(string='Description')
    test_lines = fields.One2many('test.line.model', 'test_main_id', string='Test Lines')
    test_notes = fields.One2many('test.note.model', 'test_main_id', string='Test Notes')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('done', 'Done')
    ], string='State', default='draft', tracking=True)
    active = fields.Boolean(string='Active', default=True)
"""
    
    with open(os.path.join(test_module_path, 'models', 'test_main_model.py'), 'w') as f:
        f.write(main_model_content)
    
    # Create security file
    security_content = """id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_test_main_model_user,Test Main Model User Access,model_test_main_model,base.group_user,1,1,1,1
access_test_main_model_manager,Test Main Model Manager Access,model_test_main_model,base.group_system,1,1,1,1
access_test_line_model_user,Test Line Model User Access,model_test_line_model,base.group_user,1,1,1,1
access_test_line_model_manager,Test Line Model Manager Access,model_test_line_model,base.group_system,1,1,1,1
access_test_note_model_user,Test Note Model User Access,model_test_note_model,base.group_user,1,1,1,1
access_test_note_model_manager,Test Note Model Manager Access,model_test_note_model,base.group_system,1,1,1,1"""
    
    with open(os.path.join(test_module_path, 'security', 'ir.model.access.csv'), 'w') as f:
        f.write(security_content)
    
    # Create basic views
    views_content = """<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="test_main_model_tree_view" model="ir.ui.view">
        <field name="name">Test Main Model Tree</field>
        <field name="model">test.main.model</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="state"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <record id="test_main_model_form_view" model="ir.ui.view">
        <field name="name">Test Main Model Form</field>
        <field name="model">test.main.model</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="description"/>
                        <field name="state"/>
                    </group>
                    <notebook>
                        <page string="Test Lines">
                            <field name="test_lines">
                                <tree editable="bottom">
                                    <field name="sequence"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Test Notes">
                            <field name="test_notes">
                                <tree editable="bottom">
                                    <field name="date"/>
                                    <field name="name"/>
                                    <field name="note"/>
                                    <field name="user_id"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="test_main_model_action" model="ir.actions.act_window">
        <field name="name">Test Main Model</field>
        <field name="res_model">test.main.model</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="test_main_model_menu" name="Test Main Model" action="test_main_model_action" sequence="10"/>
</odoo>"""
    
    with open(os.path.join(test_module_path, 'views', 'views.xml'), 'w') as f:
        f.write(views_content)
    
    print("✅ Test module structure created successfully!")
    
    # Verify module structure
    print("\n🔍 VERIFYING MODULE STRUCTURE:")
    print("-" * 40)
    
    checks = {
        "manifest_file": os.path.exists(os.path.join(test_module_path, '__manifest__.py')),
        "init_file": os.path.exists(os.path.join(test_module_path, '__init__.py')),
        "models_init": os.path.exists(os.path.join(test_module_path, 'models', '__init__.py')),
        "main_model": os.path.exists(os.path.join(test_module_path, 'models', 'test_main_model.py')),
        "security_file": os.path.exists(os.path.join(test_module_path, 'security', 'ir.model.access.csv')),
        "views_file": os.path.exists(os.path.join(test_module_path, 'views', 'views.xml')),
    }
    
    for check_name, result in checks.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name.replace('_', ' ').title()}: {status}")
    
    # Calculate results
    passed_checks = sum(checks.values())
    total_checks = len(checks)
    success_rate = (passed_checks / total_checks) * 100
    
    print(f"\n📊 STRUCTURE VERIFICATION RESULTS:")
    print("=" * 40)
    print(f"✅ Passed: {passed_checks}/{total_checks}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 ALL STRUCTURE CHECKS PASSED!")
        print(f"📁 Module created at: {test_module_path}")
        print("🚀 Module is ready for installation testing!")
    else:
        print("❌ Some structure checks failed")
    
    return success_rate == 100

if __name__ == "__main__":
    success = test_complete_module_generation()
    sys.exit(0 if success else 1)
