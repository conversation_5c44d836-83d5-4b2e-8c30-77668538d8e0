#!/usr/bin/env python3
"""
Regenerate Test Module Script
============================

This script regenerates the ovakil_legal_services_complete module
using the AI Module Generator via XML-RPC.

Author: AI Assistant
Version: 1.0.0
"""

import xmlrpc.client
import time
import logging
import os
from odoo_config import ODOO_CONFIG

def setup_logging():
    """Setup logging"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    return logging.getLogger('ModuleRegeneration')

def regenerate_module():
    """Regenerate the test module"""
    logger = setup_logging()
    logger.info("🏗️  REGENERATING OVAKIL LEGAL SERVICES COMPLETE MODULE")
    logger.info("=" * 60)
    
    try:
        # Connect to Odoo
        logger.info("🔌 Connecting to Odoo...")
        common = xmlrpc.client.ServerProxy(f'{ODOO_CONFIG["url"]}/xmlrpc/2/common')
        uid = common.authenticate(ODOO_CONFIG["db"], ODOO_CONFIG["username"], ODOO_CONFIG["password"], {})
        
        if not uid:
            logger.error("❌ Authentication failed")
            return False
        
        models = xmlrpc.client.ServerProxy(f'{ODOO_CONFIG["url"]}/xmlrpc/2/object')
        logger.info(f"✅ Connected successfully. User ID: {uid}")
        
        # Find a suitable template
        logger.info("🔍 Finding suitable template...")
        template_ids = models.execute_kw(
            ODOO_CONFIG["db"], uid, ODOO_CONFIG["password"],
            'module.template', 'search',
            [[('state', '=', 'active')]],
            {'limit': 1}
        )
        
        if not template_ids:
            logger.error("❌ No active templates found")
            return False
        
        template_id = template_ids[0]
        logger.info(f"✅ Found template ID: {template_id}")
        
        # Get template info
        template_info = models.execute_kw(
            ODOO_CONFIG["db"], uid, ODOO_CONFIG["password"],
            'module.template', 'read',
            [template_id],
            {'fields': ['name', 'template_type']}
        )[0]
        
        logger.info(f"📋 Using template: {template_info['name']}")
        
        # Create generation wizard
        logger.info("🧙 Creating generation wizard...")
        wizard_data = {
            'template_id': template_id,
            'module_name': 'legal_services_complete',
            'module_title': 'Complete Legal Services Application',
            'module_description': 'Comprehensive legal services management system with client portal, document management, and payment integration',
            'module_author': 'Oneclickvakil',
            'include_website': True,
            'include_portal': True,
            'include_api': True,
        }
        
        wizard_id = models.execute_kw(
            ODOO_CONFIG["db"], uid, ODOO_CONFIG["password"],
            'module.generator.wizard', 'create',
            [wizard_data]
        )
        
        logger.info(f"✅ Wizard created: {wizard_id}")
        
        # Execute generation
        logger.info("⚙️  Executing module generation...")
        result = models.execute_kw(
            ODOO_CONFIG["db"], uid, ODOO_CONFIG["password"],
            'module.generator.wizard', 'action_generate_module',
            [wizard_id]
        )
        
        logger.info(f"📤 Generation result: {result}")
        
        # Wait for generation to complete
        logger.info("⏳ Waiting for generation to complete...")
        time.sleep(10)
        
        # Check if module was created
        module_path = "/mnt/extra-addons/ovakil_legal_services_complete"
        if os.path.exists(module_path):
            logger.info(f"✅ Module successfully created at: {module_path}")
            
            # List generated files
            logger.info("📁 Generated files:")
            for root, dirs, files in os.walk(module_path):
                level = root.replace(module_path, '').count(os.sep)
                indent = ' ' * 2 * level
                logger.info(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files:
                    logger.info(f"{subindent}{file}")
            
            return True
        else:
            logger.error(f"❌ Module not found at: {module_path}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Module regeneration failed: {str(e)}")
        return False

def main():
    """Main function"""
    success = regenerate_module()
    
    if success:
        print("\n🎉 MODULE REGENERATION SUCCESSFUL!")
        print("✅ ovakil_legal_services_complete module is ready for testing!")
    else:
        print("\n❌ MODULE REGENERATION FAILED!")
        print("🔧 Please check the logs for details!")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
