import re
# import razorpay
from odoo import http, _
from odoo.http import request
import logging
from odoo.exceptions import ValidationError, UserError
from odoo.addons.http_routing.models.ir_http import slug, slugify, _guess_mimetype
import json

_logger = logging.getLogger(__name__)

class MetalAPI(http.Controller):

    def _get_image_url(self, category):
        """Helper method to get category image URL"""
        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        if category.image_1920:
            return f"/web/image/product.public.category/{category.id}/image_1920"
            # return f"{base_url}/web/image/product.public.category/{category.id}/image_1920"
        return None

    def _prepare_category_data(self, category):
        """Helper method to prepare category data"""
        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        category_url = f"/shop/category/{slug(category)}"  # Assuming `slug` is a computed field in the category model
        # category_url = f"{base_url}/shop/category/{slug(category)}"  # Assuming `slug` is a computed field in the category model
        return {
            'id': category.id,
            'name': category.name,
            'parent_id': category.parent_id.id if category.parent_id else None,
            'image_url': self._get_image_url(category),
            'url': category_url,  # Add the category URL
        }
    
    @http.route('/api/v3/homepage', type='http', methods=['GET'], csrf=False)
    def homepage_api(self):
        try:
            # Get all metals (assumed model: metal.metal)
            metals = request.env['jewelry.metal'].sudo().search([])            
            metals_data = [{
                    'id': metal.id,
                    'name': metal.name,
                    'current_price': metal.current_price,
                    'maximum_buying_grams': metal.maximum_buying_grams
                } for metal in metals]

            # Get the user vault account for the current user
            account = request.env['user.vault.account'].sudo().search(
                [('user_id', '=', request.env.user.id)],
                limit=1
            )
            account_data = {}
            if account:
                account_data = [{
                    'metal_id': balance.metal_id.id, #added by manali
                    'metal_type': balance.metal_id.name,
                    'quantity': balance.balance,
                    'selling_price': balance.metal_id.selling_price,
                    'wallet_amount': balance.wallet_amount,
                    # 'amount_total': balance.amount_total,  
                    #  'total_wallet_amount': balance.total_wallet_amount,
                }for balance in account.metal_balances_ids]

        #    added total amount  : wallet_amount changed by manali 
            # Get warehouses (assumed model: stock.warehouse)
            warehouses = request.env['stock.warehouse'].sudo().search([])
            warehouses_data = [{'id': w.id, 
                                'name': w.name,
                                'image':'/web/image/stock.warehouse/%s/image_small' % w.id} for w in warehouses]

            # # Get products (assumed model: product.template)
            # Category = request.env['product.public.category'].sudo()
            
            # # product_categories = Category.search([['name','in',['Gold Bar', 'Silver Bar' ,'Gold Coin' ,'Silver Coin']]])
            # # Step 1: Search for the product categories (main categories)
            # main_categories = Category.search([('name', 'in', ['Gold Bar', 'Silver Bar', 'Gold Coin', 'Silver Coin'])])

            # # Step 2: Initialize a list to store all subcategories
            # all_subcategories = []

            # # Step 3: Loop through each main category
            # for category in main_categories:
            #     # Step 4: Search for subcategories where the parent_id is the current category
            #     subcategories = Category.search([('parent_id', '=', category.id)])
                
            #     # Add subcategories to the list
            #     all_subcategories.extend(subcategories)
                
            #     # Optional: If you want to search recursively (in case subcategories also have their own subcategories)
            #     while subcategories:
            #         # Get the next level of subcategories (children of current subcategories)
            #         next_level_subcategories = Category.search([('parent_id', 'in', subcategories.ids)])
            #         all_subcategories.extend(next_level_subcategories)
                    
            #         # Update the subcategories list for the next iteration
            #         subcategories = next_level_subcategories
            # product_categories = all_subcategories
            # Product = request.env['product.template'].sudo().search([['public_categ_ids','in',product_categories.ids],['detailed_type','=','product'],['website_published','=',True]])
            # product_data = [{'id': prod.id, 
            #                     'name': prod.name,                                
            #                     # 'categ_id':prod.categ_id
            #                     } for prod in Product]
            # Get the category model
            Category = request.env['product.public.category'].sudo()

            # Step 1: Search for the product categories (main categories)
            main_categories = Category.search([('name', 'in', ['Gold Bar', 'Silver Bar', 'Gold Coin', 'Silver Coin'])])

            # Step 2: Initialize a recordset to store all subcategories
            all_subcategories = request.env['product.public.category']

            # Step 3: Loop through each main category
            for category in main_categories:
                # Step 4: Search for subcategories where the parent_id is the current category
                subcategories = Category.search([('parent_id', '=', category.id)])
                
                # Add subcategories to the recordset (combine with all_subcategories)
                all_subcategories |= subcategories
                
                # Optional: If you want to search recursively (in case subcategories also have their own subcategories)
                while subcategories:
                    # Get the next level of subcategories (children of current subcategories)
                    next_level_subcategories = Category.search([('parent_id', 'in', subcategories.ids)])
                    
                    # Add to all_subcategories recordset
                    all_subcategories |= next_level_subcategories
                    
                    # Update the subcategories list for the next iteration
                    subcategories = next_level_subcategories

            # Now all_subcategories is an Odoo recordset containing all subcategories
            product_categories = all_subcategories

            # Search for products in these categories
            Product = request.env['product.template'].sudo().search([
                ['public_categ_ids', 'in', product_categories.ids],  # Use the ids of the categories
                ['detailed_type', '=', 'product'],
                ['website_published', '=', True]
            ], limit=8)

            # Prepare product data
            product_data = [{
                'id': prod.id,
                'name': prod.name,
                # 'image':prod.image_1920,
                'img_url': f"/web/image/product.template/{prod.id}/image_1920",

                'price':prod.list_price,
                # 'image':prod.image_1920
                # 'categ_id': prod.categ_id  # You can include additional fields if needed
            } for prod in Product]

                                

        # bar_category_ids = [cat.id for cat in main_categories if cat.name.lower() in ['gold bar', 'silver bar']]
        # coin_category_ids = [cat.id for cat in main_categories if cat.name.lower() in ['gold coin', 'silver coin']]

        # bar_products = request.env['product.template'].sudo().search([('categ_id', 'in', bar_category_ids)])
        # coin_products = request.env['product.template'].sudo().search([('categ_id', 'in', coin_category_ids)])

            # Get main categories (no parent)
            # goldProducts = Product.search([('categ_id', '=', '51')], limit=6)



            main_categories = Category.search([('parent_id', '=', False)], limit=6)
            main_category_list = [self._prepare_category_data(category) for category in main_categories]

            sub_categories = Category.search([('parent_id', '!=', False)], limit=7)
            sub_category_list = [self._prepare_category_data(category) for category in sub_categories]

            #code change by manali 77-87 , 94 for gold jewellery category : bracelets , chains
            goldJewellery = Category.search([('parent_id', '=', 40)], limit=2)
            goldJewellery_list = [self._prepare_category_data(category) for category in goldJewellery]
            

            # Product = request.env['product.template'].sudo()
        #     gold_products =  request.env['product.template']..sudo().search([
        #    ('categ_id','=',goldJewellery)
        # ])
            #   products_data = [{
            #     'id': prod.id,
            #     'name': prod.name,
            #     'price': prod.list_price,
            # } for prod in Product]

            return json.dumps({
                'status': 'success',
                'data': {
                    'metals': metals_data,
                    'account': account_data,
                    'warehouses': warehouses_data,
                    'product_data':product_data,
                    # 'jwellery': gold_products,
                    'products': {'main_categories': {
                            'count': len(main_category_list),
                            'data': main_category_list
                        },
                        'sub_categories': {
                            'count': len(sub_category_list),
                            'data': sub_category_list
                        },
                        #code change by manali 95 - 99
                        'goldJewellery': {
                            'count': len(goldJewellery_list),
                            'data': goldJewellery_list
                        }
                        # ,
                        # 'barCat':{
                        #     'data':bar_category_ids
                        # }
                        # ,
                        # 'products_data':{
                        #     'data':product_data
                        # }
                        }
                }
            })
        except Exception as e:
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/sip/account', type='http', auth='user', csrf=False)
    def metal_account_api(self):
        try:
            account = request.env['user.vault.account'].sudo().search([
                ('user_id', '=', request.env.user.id)
            ], limit=1)

            if not account:
                account = request.env['user.vault.account'].sudo().create({
                    'user_id': request.env.user.id,
                })

            metal_balances = request.env['user.metal.balance'].sudo().search([
                ('user_id', '=', request.env.user.id)
            ])

            metal_type = request.env['jewelry.metal'].sudo().search([])

            # Prepare metal balances data
            balance_data = []
            for balance in metal_balances:
                balance_data.append({
                    'metal_id': balance.metal_id.id,
                    'metal_name': balance.metal_id.name,
                    'balance': balance.balance,
                    'current_price': balance.metal_id.current_price,
                    
                })

            # Prepare metal types data
            metal_types_data = [{
                'id': metal.id,
                'name': metal.name,
            } for metal in metal_type]

            return json.dumps({
                'status': 'success',
                'data': {
                    'account_id': account.id,
                    'metals_type': metal_types_data,
                    'metal_balances': balance_data,
                    'total_invested': account.total_invested_amount,
                    'total_wallet': account.total_wallet_amount
                }
            })
        except Exception as e:
            _logger.error("Error accessing metal account API: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': _("Unable to access your Metal account. Please try again later.")
            })

    
    @http.route('/api/v1/metals', type='http', auth='user', csrf=False)
    def get_metals(self):
        """Get all available metals"""
        try:
            metals = request.env['jewelry.metal'].sudo().search([])
            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': metal.id,
                    'name': metal.name,
                    'current_price': metal.current_price,
                    'maximum_buying_grams': metal.maximum_buying_grams
                } for metal in metals]
            })
        except Exception as e:
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/metal/buy', type='json', auth='user', csrf=False ,methods=['POST'])
    def buy_metal(self, metal_id, amount):
        """Buy metal endpoint"""
        try:
            if float(amount) < float(10):
                return {'status': 'error', 'message': 'Minimum amount is 10'}

            # Check VAT for amounts >= 20000
            if float(amount) >= float(20000):
                if not request.env.user.partner_id.vat:
                    return {'status': 'error', 'message': 'VAT/PAN is required for purchases above ₹20,000'}

            metal = request.env['jewelry.metal'].sudo().browse(int(metal_id))
            if not metal:
                return {'status': 'error', 'message': 'Invalid metal'}

            if metal.maximum_buying_grams:
                grams = float(amount) / metal.current_price
                if grams > float(metal.maximum_buying_grams):
                    return {'status': 'error', 'message': 'Exceeds maximum buying limit'}

            return {
                'status': 'success',
                'data': {
                    'payment_url': f'/donation/pay?amount={amount}&metal_id={metal_id}'
                }
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    @http.route('/api/v1/metal/sell', type='json', auth='user', csrf=False ,methods=['POST'])
    def sell_metal(self, metal_id, grams):
        """Sell metal endpoint"""
        try:
            metal = request.env['jewelry.metal'].sudo().browse(int(metal_id))
            if not metal:
                return {'status': 'error', 'message': 'Invalid metal'}

            grams = float(grams)
            if grams < 1:
                return {'status': 'error', 'message': 'Minimum 1 gram required'}

            balance = request.env['user.metal.balance'].sudo().search([
                ('user_id', '=', request.env.user.id),
                ('metal_id', '=', metal.id)
            ], limit=1)

            if not balance or balance.balance < grams:
                return {'status': 'error', 'message': 'Insufficient balance'}

            transaction = request.env['metal.transaction'].sudo().sell_metal(
                request.env.user.id,
                metal.id,
                grams
            )

            return {
                'status': 'success',
                'data': {
                    'transaction_id': transaction.id,
                    'amount': transaction.currency_amount,
                    'grams': transaction.metal_amount
                }
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    
    @http.route('/api/v1/transactions', type='http', auth='user', csrf=False)
    def get_transactions(self):
        """Get user's transactions"""
        try:
            account = request.env['user.vault.account'].sudo().search([
                ('user_id', '=', request.env.user.id)
            ], limit=1)

            if not account:
                return json.dumps({
                    'status': 'error',
                    'message': 'Account not found'
                })

            transactions = account.metal_transactions_ids

            return json.dumps({
                'status': 'success',
                'data': {
                    'total_invested': account.total_invested_amount,
                    'total_wallet': account.total_wallet_amount,
                    'transactions': [{
                        'id': t.id,
                        'metal_name': t.metal_id.name,
                        'type': t.transaction_type,
                        'amount': t.currency_amount,
                        'grams': t.metal_amount,
                        'date': t.create_date.strftime('%Y-%m-%d %H:%M:%S')
                    } for t in transactions]
                }
            })
        except Exception as e:
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/nominees', type='http', auth='user', csrf=False, methods=['GET'])
    def list_nominees(self, **kw):  # Add **kw to capture all parameters
        """Get list of all nominees for the current user with optional search filter"""
        try:
            # Get search query from kw parameters
            search_query = kw.get('search', '')
            
            domain = [
                ('parent_id', '=', request.env.user.partner_id.id),
                ('type', '=', 'contact')
            ]
            
            if search_query:
                domain += [
                    ('name', 'ilike', search_query)
                ]

            _logger.info("Search domain: %s", domain)

            nominees = request.env['res.partner'].sudo().search(domain)

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': nominee.id,
                    'name': nominee.name,
                    'phone': nominee.phone,
                    'email': nominee.email,
                    'relation': nominee.relation,
                } for nominee in nominees],
                'total_count': len(nominees),
            })
        except Exception as e:
            _logger.error("Error fetching nominees list: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/metal/deliveries/request_delivery', type='json', auth='user', csrf=False, methods=['POST'])
    def request_delivery_api(self, metal_id, **kwargs):
        metal = request.env['jewelry.metal'].browse(int(metal_id))
        if not metal:
            return {'status': 'error', 'message': 'Invalid metal'}
        if (metal.name == 'Gold'):
            specific_items = ['Gold Coin', 'Gold Bar']
        elif (metal.name == 'Silver'):
            specific_items = ['Silver Coin', 'Silver Bar']
        else:
            specific_items = []

        # Fetch categories and their subcategories
        categories = request.env['product.public.category'].sudo().search([
            ('name', 'in', specific_items )
        ])
        category_ids = categories.ids
        if categories:
            subcategories = request.env['product.public.category'].sudo().search([
                ('id', 'child_of', categories.ids)
            ])
            category_ids = subcategories.ids

        # Fetch products within the specified categories and subcategories
        metal_products = request.env['product.template'].sudo().search([
            ('public_categ_ids', 'in', category_ids)
        ])

        # Fetch the user's balance for the specified metal type
        user_balance = request.env['user.metal.balance'].sudo().search([
            ('user_id', '=', request.env.user.id),
            ('metal_id', '=', metal_id)
        ], limit=1)

        # Prepare response data   #351 ,352 line added by manali 
        products_data = [{
            'id': prod.id,
            'name': prod.name,
            'image':prod.image_1920,
            'price':prod.list_price,
        } for prod in metal_products]

        # # Prepare response data
        # products_data = [{
        #     'id': prod.id,
        #     'name': prod.name,
        # } for prod in metal_products]

        balance_data = {}
        if user_balance:
            # Assuming "balance" is a field on the user.metal.balance model
            balance_data = {
                'id': user_balance.id,
                'balance': user_balance.balance,
            }

        return {
            'status': 'success',
            'data': {
                'metal_products': products_data,
                'user_balance': balance_data,
                'request_metal_id': metal_id,
            }
        }
    
    @http.route('/api/v1/metal/deliveries', type='http', auth='user', csrf=False)
    def get_deliveries(self):
        """Get user's delivery requests"""
        try:
            deliveries = request.env['metal.delivery.request'].sudo().search([
                ('user_id', '=', request.env.user.id)
            ], order='create_date desc')

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': d.id,
                    'metal_name': d.metal_id.name,
                    'grams': d.metal_requested,
                    'status': d.status,
                    'address': d.address,
                    'date': d.create_date.strftime('%Y-%m-%d %H:%M:%S')
                } for d in deliveries]
            })
        except Exception as e:
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })
        except Exception as e:
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })
    
    @http.route('/api/v1/metal/deliveries/cancel/<int:delivery_id>', type='http', auth='user', csrf=False, methods=['DELETE'])
    def cancel_delivery(self, delivery_id):
        try:
            # Fetch the delivery request
            delivery = request.env['metal.delivery.request'].sudo().browse(int(delivery_id)).exists()
            if not delivery:
                return {'status': 'error', 'message': 'Delivery request not found.'}

            # Check if the delivery belongs to the user
            if delivery.user_id.id != request.env.user.id:
                return {'status': 'error', 'message': 'Unauthorized access.'}

            # Check if the delivery is in 'pending' state
            if delivery.status != 'pending':
                return {'status': 'error', 'message': 'Only pending deliveries can be cancelled.'}

            # Perform the cancellation
            delivery.action_cancel()

            return {'status': 'success', 'message': 'Delivery request cancelled successfully.'}

        except Exception as e:
            _logger.error("Error cancelling delivery via API: %s", str(e))
            return {'status': 'error', 'message': 'Failed to cancel delivery request.'}
        
    # @http.route('/api/v3/shop', type='http', methods=['GET'], csrf=False)
    # def shop_api(self):
    #     """Get categories and products for shop screen with optional filtering and sorting."""
    #     try:
    #         main_category_id = request.params.get('main_category_id')
    #         sub_category_id = request.params.get('sub_category_id')
    #         search_query = request.params.get('search')
            
    #         # New parameters for filtering and sorting
    #         min_price = request.params.get('min_price')
    #         max_price = request.params.get('max_price')
    #         sort_by = request.params.get('sort_by', 'name_asc')  # Default sort by name ascending

    #         Category = request.env['product.public.category'].sudo()
    #         Product = request.env['product.template'].sudo()

    #         if main_category_id and sub_category_id:
    #             return json.dumps({
    #                 'status': 'error',
    #                 'message': 'Only one of main_category_id or sub_category_id should be provided.'
    #             })
            
    #         # Build domain for products
    #         domain = []

    #         # Add price range filters if provided
    #         if min_price:
    #             domain.append(('list_price', '>=', float(min_price)))
    #         if max_price:
    #             domain.append(('list_price', '<=', float(max_price)))

    #         # Category filtering
    #         if main_category_id:
    #             main_category = Category.browse(int(main_category_id))
    #             sub_categories = Category.search([
    #                 ('id', 'child_of', main_category.id),
    #                 ('id', '!=', main_category.id)
    #             ])
    #             sub_category_list = [self._prepare_category_data(cat) for cat in sub_categories]
    #             main_category_list = [self._prepare_category_data(main_category)]
    #             domain.append(('public_categ_ids', 'child_of', main_category.id))
    #         elif sub_category_id:
    #             chosen_sub = Category.browse(int(sub_category_id))
    #             main_category = chosen_sub.parent_id
    #             sub_category_list = [self._prepare_category_data(chosen_sub)]
    #             main_category_list = [self._prepare_category_data(main_category)]
    #             domain.append(('public_categ_ids', 'child_of', chosen_sub.id))
    #         else:
    #             main_categories = Category.search([('parent_id', '=', False)], limit=7)
    #             main_category_list = [self._prepare_category_data(cat) for cat in main_categories]
    #             sub_categories = Category.search([('parent_id', '!=', False)], limit=7)
    #             sub_category_list = [self._prepare_category_data(cat) for cat in sub_categories]

    #         # Add search query filter
    #         if search_query:
    #             domain.append(('name', 'ilike', search_query))

    #         # Determine sort order
    #         order = 'name asc'  # default sorting
    #         if sort_by == 'price_asc':
    #             order = 'list_price asc'
    #         elif sort_by == 'price_desc':
    #             order = 'list_price desc'
    #         elif sort_by == 'name_desc':
    #             order = 'name desc'
    #         elif sort_by == 'newest':
    #             order = 'create_date desc'

    #         _logger.info('Domain: %s', domain)
    #         _logger.info('Order: %s', order)

    #         # Search products with domain and sorting
    #         products = Product.search(domain, order=order)

    #         products_data = [{
    #             'id': prod.id,
    #             'img_url': f"/web/image/product.template/{prod.id}/image_1920",
    #             'name': prod.name,
    #             'price': prod.list_price,
    #             'create_date': prod.create_date.strftime('%Y-%m-%d %H:%M:%S') if prod.create_date else None
    #         } for prod in products]

    #         return json.dumps({
    #             'status': 'success',
    #             'data': {
    #                 'main_categories': main_category_list,
    #                 'sub_categories': sub_category_list,
    #                 'products': products_data,
    #                 'filters': {
    #                     'min_price': min_price,
    #                     'max_price': max_price,
    #                     'sort_by': sort_by
    #                 }
    #             }
    #         })
    #     except Exception as e:
    #         return json.dumps({
    #             'status': 'error',
    #             'message': str(e)
    #         })

    
    
    @http.route('/api/jivdaya_domain/<string:model_name>', type='json', auth='public', website=True, methods=['GET', 'POST'], cors='*')
    def get_records_v2_domain_filtered(self, model_name, **kwargs):
        try:
            request_data = json.loads(request.httprequest.data)

            page = int(request.httprequest.args.get('page', 1))
            batch_size = int(request.httprequest.args.get('batch_size', 20))
            enabled_models = ['x_advocate_panel','x_volunteer_registration','x_ngo_master','x_court_type','x_stories','res.partner','blog.blog','blog.post','slide.channel','product.product','product.template']
            disabled_fields = ['x_password']
            data = request_data.get('data', [])
            model = model_name
            if model in enabled_models:
                domain_filter = eval(data.get("domain_filter", "[]"))
                order = data.get("args", {})

                include_binary = data.get("include_binary", True)

                search_domain = domain_filter

                records = request.env[model].sudo().search(search_domain, **order, limit=batch_size, offset=batch_size * (page - 1))

                counts = request.env[model].sudo().search_count(search_domain)

                if records:
                    response_data = {
                        "records": [],
                        "total_records": counts
                    }

                    for record in records:  # Iterate over each record
                        record_data = {"id": record.id}

                        for key in record._fields:  # Iterate over fields of the current record
                            if key not in disabled_fields:
                                value = getattr(record, key)
                                if record._fields[key].type == 'binary':
                                    if isinstance(value, bytes):
                                        if isinstance(include_binary, bool) and include_binary:
                                            record_data[key] = base64.b64encode(value).decode('utf-8')
                                        elif isinstance(include_binary, list) and key in include_binary:
                                            record_data[key] = base64.b64encode(value).decode('utf-8')
                                elif record._fields[key].type in ['one2many', 'many2many' ,'many2one']:
                                    related_model = record._fields[key].comodel_name
                                    related_records = request.env[related_model].sudo().search([('id', 'in', value.ids)])
                                    related_data = [{'id': rec.id, 'name': rec.name if hasattr(rec, 'name') else
                                    (rec.x_name if hasattr(rec, 'x_name') else '')} for rec in related_records]
                                    record_data[key] = related_data
                                elif isinstance(value, (datetime.datetime, datetime.date)):
                                    record_data[key] = value.strftime('%Y-%m-%d %H:%M:%S') if isinstance(value, datetime.datetime) else value.strftime('%Y-%m-%d')
                                else:
                                    record_data[key] = value
                        if model == 'x_stories':                            
                            record_data['thumbnail'] = base64.b64encode(record.create_uid.image_1920).decode('utf-8')                                
                            
                        response_data["records"].append(record_data)
                    return response_data
                else:
                    return {"message": "Records not found !"}
            else:
                return {"message": "API is not authorized to provide the requested information !"}
        except Exception as e:
                return {'status': 'error', 'error_message': str(e)}

    
    
    
    @http.route('/api/v3/shop', type='http', methods=['GET'], auth='public' ,csrf=False)
    def shop_api(self):
    #   """Get categories and products for shop screen with optional filtering, sorting, and pagination."""
        try:
            # Category filtering parameters
            main_category_id = request.params.get('main_category_id')
            sub_category_id = request.params.get('sub_category_id')
            search_query = request.params.get('search')
            
            # Filter and sort parameters
            min_price = request.params.get('min_price')
            max_price = request.params.get('max_price')
            sort_by = request.params.get('sort_by', 'name_asc')  # Default sort by name ascending
            
            # Pagination parameters
            limit = int(request.params.get('limit', 10))  # Default 10 products per page
            page = int(request.params.get('page', 1))  # Default to first page
            offset = (page - 1) * limit  # Calculate offset based on page and limit

            Category = request.env['product.public.category'].sudo()
            Product = request.env['product.template'].sudo()

            if main_category_id and sub_category_id:
                return json.dumps({
                    'status': 'error',
                    'message': 'Only one of main_category_id or sub_category_id should be provided.'
                })
            
            # Build domain for products
            domain = []

            # Add price range filters if provided
            if min_price:
                domain.append(('list_price', '>=', float(min_price)))
            if max_price:
                domain.append(('list_price', '<=', float(max_price)))

            # Category filtering
            if main_category_id:
                main_category = Category.browse(int(main_category_id))
                sub_categories = Category.search([
                    ('id', 'child_of', main_category.id),
                    ('id', '!=', main_category.id)
                ])
                sub_category_list = [self._prepare_category_data(cat) for cat in sub_categories]
                main_category_list = [self._prepare_category_data(main_category)]
                domain.append(('public_categ_ids', 'child_of', main_category.id))
            elif sub_category_id:
                chosen_sub = Category.browse(int(sub_category_id))
                main_category = chosen_sub.parent_id
                sub_category_list = [self._prepare_category_data(chosen_sub)]
                main_category_list = [self._prepare_category_data(main_category)]
                domain.append(('public_categ_ids', 'child_of', chosen_sub.id))
            else:
                main_categories = Category.search([('parent_id', '=', False)], limit=7)
                main_category_list = [self._prepare_category_data(cat) for cat in main_categories]
                sub_categories = Category.search([('parent_id', '!=', False)], limit=7)
                sub_category_list = [self._prepare_category_data(cat) for cat in sub_categories]

            # Add search query filter
            if search_query:
                domain.append(('name', 'ilike', search_query))

            # Determine sort order
            order = 'name asc'  # default sorting
            if sort_by == 'price_asc':
                order = 'list_price asc'
            elif sort_by == 'price_desc':
                order = 'list_price desc'
            elif sort_by == 'name_desc':
                order = 'name desc'
            elif sort_by == 'newest':
                order = 'create_date desc'

            _logger.info('Domain: %s', domain)
            _logger.info('Order: %s', order)
            _logger.info('Pagination: limit=%s, page=%s, offset=%s', limit, page, offset)

            # Count total products for pagination info
            total_count = Product.search_count(domain)
            total_pages = total_count / limit if total_count > 0 else 1

            # Get only the products for the current page
            # products = Product.search(domain)

            # products_data = [{
            #     'id': prod.id,
            #     'img_url': f"/web/image/product.template/{prod.id}/image_1920",
            #     'name': prod.name,
            #     'price': prod.list_price,
            #     # 'public_categ_ids': prod.public_categ_ids.id,
            #     # 'attribute_line_ids': prod.attribute_line_ids.id[0],
            #     # 'metal_id': prod.company_id.id,
            #     # 'attribute_line_ids': prod.attribute_line_ids.id[0],
            #     'create_date': prod.create_date.strftime('%Y-%m-%d %H:%M:%S') if prod.create_date else None
            # } for prod in products]

            products = Product.search(domain)
            products_data = [{
                'id': prod.id,
                'img_url': f"/web/image/product.template/{prod.id}/image_1920",
                'name': prod.name,
                'price': prod.list_price,
                'create_date': prod.create_date.strftime('%Y-%m-%d %H:%M:%S') if prod.create_date else None,
                'attributes': [{
                    'attribute_id': attr_line.attribute_id.id,
                    'attribute_name': attr_line.attribute_id.name,
                    'values': [{
                        'value_id': val.id,
                        'value_name': val.name
                    } for val in attr_line.value_ids]
                } for attr_line in prod.attribute_line_ids]
            } for prod in products]
        

            # Get recommended products (can be top sellers or featured)
            recommended_products = Product.search([], order='list_price desc', limit=4)
            recommended_data = [{
                'id': prod.id,
                'img_url': f"/web/image/product.template/{prod.id}/image_1920",
                'name': prod.name,
                'price': prod.list_price
            } for prod in recommended_products]

            return json.dumps({
                'status': 'success',
                'data': {
                    'main_categories': main_category_list,
                    'sub_categories': sub_category_list,
                    'products': products_data,
                    'recommended': recommended_data,
                    'filters': {
                        'min_price': min_price,
                        'max_price': max_price,
                        'sort_by': sort_by
                    },
                    'pagination': {
                        'total_count': total_count,
                        'total_pages': total_pages,
                        'current_page': page,
                        'limit': limit
                    }
                }
            })
        except Exception as e:
            _logger.error("Error in shop_api: %s", str(e), exc_info=True)
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })




    # @http.route('/api/v1/shop_product/<int:product_id>', type='http', auth='user', csrf=False)
    # def shop_product_details_api(self, product_id, variant_id=None):
    #     """API endpoint to return product details with warehouse and delivery information"""
    #     try:
    #         if not product_id:
    #             return json.dumps({
    #                 'status': 'error',
    #                 'message': _("Product ID is required.")
    #             })
            
    #         Product = request.env['product.template'].sudo()
    #         Warehouse = request.env['stock.warehouse'].sudo()
    #         product = Product.browse(int(product_id))

    #         if not product.exists():
    #             return json.dumps({
    #                 'status': 'error',
    #                 'message': _("Product not found.")
    #             })

    #         # Get warehouse information and stock availability
    #         warehouses = Warehouse.search([])
    #         warehouse_data = []
            
    #         for warehouse in warehouses:
    #             # Check stock availa21sbility in this warehouse
    #             quants = request.env['stock.quant'].sudo().search([
    #                 ('product_tmpl_id', '=', product.id),
    #                 ('location_id.usage', '=', 'internal'),
    #                 ('location_id.warehouse_id', '=', warehouse.id),
    #                 ('quantity', '>', 0)
    #             ])

    #             # Calculate total available quantity
    #             available_qty = sum(quants.mapped('quantity'))
                
    #             # Determine delivery options and timing
    #             delivery_options = []
    #             if available_qty > 0:
    #                 delivery_options = [
    #                     {
    #                         'id': 'store_pickup',
    #                         'name': 'In Store Pickup',
    #                         'days': 0,
    #                         'description': 'Same Day Pickup Available'
    #                     },
    #                     {
    #                         'id': 'home_delivery',
    #                         'name': 'Home Delivery',
    #                         'days': 4,
    #                         'description': '4 Day(s) Delivery'
    #                     }
    #                 ]
    #             else:
    #                 delivery_options = [
    #                     {
    #                         'id': 'home_delivery',
    #                         'name': 'Home Delivery',
    #                         'days': 4,
    #                         'description': 'Location not available. 4 Day(s) Delivery'
    #                     }
    #                 ]

    #             warehouse_data.append({
    #                 'id': warehouse.id,
    #                 'name': warehouse.name,
    #                 'city': warehouse.partner_id.city or 'N/A',
    #                 'available_quantity': available_qty,
    #                 'delivery_options': delivery_options,
    #                 'is_central': warehouse.is_central_warehouse
    #             })

    #         # Get product data
    #         product_data = product.read()[0]
            
    #         # Process variant if specified
    #         variant_data = None
    #         if variant_id:
    #             variant = request.env['product.product'].sudo().browse(int(variant_id))
    #             if variant.exists() and variant.product_tmpl_id.id == product.id:
    #                 variant_data = variant.read()[0]
    #                 # Add image URLs for variant
    #                 for key in list(variant_data.keys()):
    #                     if key.startswith('image') and variant_data.get(key):
    #                         variant_data[key] = f"/web/image/product.product/{variant_data['id']}/{key}"

    #         # Add image URLs for product
    #         for key in list(product_data.keys()):
    #             if key.startswith('image') and product_data.get(key):
    #                 product_data[key] = f"/web/image/product.template/{product_data['id']}/{key}"
            
    #         alternative_products = product.alternative_product_ids
    #         alternative_data = []
    #         for alt_product in alternative_products:
    #             # alt_product_data = alt_product.read()[0]
    #             alt_product_data = alt_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #             # Add image URLs for alternative products
    #             for key in list(alt_product_data.keys()):
    #                 if key.startswith('image') and alt_product_data.get(key):
    #                     alt_product_data[key] = f"/web/image/product.template/{alt_product_data['id']}/{key}"
    #             alternative_data.append(alt_product_data)

    #             # Get optional products (trending products) with same fields
    #         optional_products = product.optional_product_ids
    #         trending_data = []
    #         for trend_product in optional_products:
    #             trend_product_data = trend_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #             if trend_product_data.get('image_1920'):
    #                 trend_product_data['image_1920'] = f"/web/image/product.template/{trend_product_data['id']}/image_1920"
    #             trending_data.append(trend_product_data)    


    #         # accessory_product = product.accessory_product_ids
    #         # accessory_product_data = []
    #         # for access_product in accessory_product:
    #         #     # alt_product_data = alt_product.read()[0]
    #         #     acs_product_data = accessory_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #         #     # Add image URLs for alternative products
    #         #     for key in list(acs_product_data.keys()):
    #         #         if key.startswith('image') and acs_product_data.get(key):
    #         #             acs_product_data[key] = f"/web/image/product.template/{acs_product_data['id']}/{key}"
    #         #     access_data.append(acs_product_data)

    #         # Return JSON response including alternative products
    #         return json.dumps({
    #             'status': 'success',
    #             'data': {
    #                 'product': product_data,
    #                 'variant': variant_data,
    #                 'warehouses': warehouse_data,
    #                 'total_warehouses': len(warehouse_data),
    #                 'has_stock': any(w['available_quantity'] > 0 for w in warehouse_data),
    #                 'alternative_products': alternative_data, # Add alternative products here
    #                 'trending_products': trending_data

    #                 # 'access_products':access_data

    #             }
    #         }, default=str)
    #         # return json.dumps({
    #         #     'status': 'success',
    #         #     'data': {
    #         #         'product': product_data,
    #         #         'variant': variant_data,
    #         #         'warehouses': warehouse_data,
    #         #         'total_warehouses': len(warehouse_data),
    #         #         'has_stock': any(w['available_quantity'] > 0 for w in warehouse_data)
    #         #     }
    #         # }, default=str)

    #     except Exception as e:
    #         _logger.error("Error accessing product details API: %s", str(e))
    #         return json.dumps({
    #             'status': 'error',
    #             'message': _("Unable to retrieve product details. Please try again later.")
    #         })
    # codde change by manali : w21st april



    # @http.route('/api/v1/shop_product/<int:product_id>', type='http', auth='user', csrf=False)
    # def shop_product_details_api(self, product_id, variant_id=None):
    #     """API endpoint to return product details with warehouse and delivery information"""
    #     try:
    #         if not product_id:
    #             return json.dumps({
    #                 'status': 'error',
    #                 'message': _("Product ID is required.")
    #             })
            
    #         Product = request.env['product.template'].sudo()
    #         Warehouse = request.env['stock.warehouse'].sudo()
    #         product = Product.browse(int(product_id))

    #         if not product.exists():
    #             return json.dumps({
    #                 'status': 'error',
    #                 'message': _("Product not found.")
    #             })

    #         # Get warehouse information and stock availability
    #         warehouses = Warehouse.search([])
    #         warehouse_data = []
            
    #         for warehouse in warehouses:
    #             # Check stock availability in this warehouse
    #             quants = request.env['stock.quant'].sudo().search([
    #                 ('product_tmpl_id', '=', product.id),
    #                 ('location_id.usage', '=', 'internal'),
    #                 ('location_id.warehouse_id', '=', warehouse.id),
    #                 ('quantity', '>', 0)
    #             ])

    #             # Calculate total available quantity
    #             available_qty = sum(quants.mapped('quantity'))
                
    #             # Determine delivery options and timing
    #             delivery_options = []
    #             if available_qty > 0:
    #                 delivery_options = [
    #                     {
    #                         'id': 'store_pickup',
    #                         'name': 'In Store Pickup',
    #                         'days': 0,
    #                         'description': 'Same Day Pickup Available'
    #                     },
    #                     {
    #                         'id': 'home_delivery',
    #                         'name': 'Home Delivery',
    #                         'days': 4,
    #                         'description': '4 Day(s) Delivery'
    #                     }
    #                 ]
    #             else:
    #                 delivery_options = [
    #                     {
    #                         'id': 'home_delivery',
    #                         'name': 'Home Delivery',
    #                         'days': 4,
    #                         'description': 'Location not available. 4 Day(s) Delivery'
    #                     }
    #                 ]

    #             warehouse_data.append({
    #                 'id': warehouse.id,
    #                 'name': warehouse.name,
    #                 'city': warehouse.partner_id.city or 'N/A',
    #                 'available_quantity': available_qty,
    #                 'delivery_options': delivery_options,
    #                 'is_central': warehouse.is_central_warehouse
    #             })

    #         # Get product data
    #         product_data = product.read()[0]
            
    #         # Add attributes data if they exist
    #         if product.attribute_line_ids:
    #             attributes_data = []
    #             for attribute_line in product.attribute_line_ids:
    #                 values_data = []
    #                 for value in attribute_line.value_ids:
    #                     values_data.append({
    #                         'value_id': value.id,
    #                         'value_name': value.name
    #                     })
                    
    #                 attributes_data.append({
    #                     'attribute_id': attribute_line.attribute_id.id,
    #                     'attribute_name': attribute_line.attribute_id.name,
    #                     'values': values_data
    #                 })
                
    #             product_data['attributes'] = attributes_data
            
    #         # Process variant if specified
    #         variant_data = None
    #         if variant_id:
    #             variant = request.env['product.product'].sudo().browse(int(variant_id))
    #             if variant.exists() and variant.product_tmpl_id.id == product.id:
    #                 variant_data = variant.read()[0]
    #                 # Add image URLs for variant
    #                 for key in list(variant_data.keys()):
    #                     if key.startswith('image') and variant_data.get(key):
    #                         variant_data[key] = f"/web/image/product.product/{variant_data['id']}/{key}"

    #         # Add image URLs for product
    #         for key in list(product_data.keys()):
    #             if key.startswith('image') and product_data.get(key):
    #                 product_data[key] = f"/web/image/product.template/{product_data['id']}/{key}"
            
    #         alternative_products = product.alternative_product_ids
    #         alternative_data = []
    #         for alt_product in alternative_products:
    #             alt_product_data = alt_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #             # Add image URLs for alternative products
    #             for key in list(alt_product_data.keys()):
    #                 if key.startswith('image') and alt_product_data.get(key):
    #                     alt_product_data[key] = f"/web/image/product.template/{alt_product_data['id']}/{key}"
    #             alternative_data.append(alt_product_data)

    #         # Get optional products (trending products) with same fields
    #         optional_products = product.optional_product_ids
    #         trending_data = []
    #         for trend_product in optional_products:
    #             trend_product_data = trend_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #             if trend_product_data.get('image_1920'):
    #                 trend_product_data['image_1920'] = f"/web/image/product.template/{trend_product_data['id']}/image_1920"
    #             trending_data.append(trend_product_data)    

    #         # Return JSON response including alternative products
    #         return json.dumps({
    #             'status': 'success',
    #             'data': {
    #                 'product': product_data,
    #                 'variant': variant_data,
    #                 'warehouses': warehouse_data,
    #                 'total_warehouses': len(warehouse_data),
    #                 'has_stock': any(w['available_quantity'] > 0 for w in warehouse_data),
    #                 'alternative_products': alternative_data,
    #                 'trending_products': trending_data
    #             }
    #         }, default=str)
            
    #     except Exception as e:
    #         _logger.error("Error accessing product details API: %s", str(e))
    #         return json.dumps({
    #             'status': 'error',
    #             'message': _("Unable to retrieve product details. Please try again later.")
    #         })

    # controlling over the api by manali 

    @http.route('/api/v1/shop_product/<int:product_id>', type='http', auth='user', csrf=False, methods=['GET'])
    def shop_product_details_api(self, product_id, **kwargs):
        """API endpoint to return product details with warehouse and delivery information"""
        try:
            if not product_id:
                return json.dumps({
                    'status': 'error',
                    'message': "Product ID is required."
                })
            
            Product = request.env['product.template'].sudo()
            ProductProduct = request.env['product.product'].sudo()
            Warehouse = request.env['stock.warehouse'].sudo()
            
            product = Product.browse(product_id)
            
            if not product.exists():
                return json.dumps({
                    'status': 'error',
                    'message': "Product not found."
                })

            # Get warehouse information and stock availability
            warehouses = Warehouse.search([])
            warehouse_data = []
            
            for warehouse in warehouses:
                # Check stock availability in this warehouse
                quants = request.env['stock.quant'].sudo().search([
                    ('product_tmpl_id', '=', product.id),
                    ('location_id.usage', '=', 'internal'),
                    ('location_id.warehouse_id', '=', warehouse.id),
                    ('quantity', '>', 0)
                ])

                # Calculate total available quantity
                available_qty = sum(quants.mapped('quantity'))
                
                # Determine delivery options and timing
                delivery_options = []
                if available_qty > 0:
                    delivery_options = [
                        {
                            'id': 'store_pickup',
                            'name': 'In Store Pickup',
                            'days': 0,
                            'description': 'Same Day Pickup Available'
                        },
                        {
                            'id': 'home_delivery',
                            'name': 'Home Delivery',
                            'days': 4,
                            'description': '4 Day(s) Delivery'
                        }
                    ]
                else:
                    delivery_options = [
                        {
                            'id': 'home_delivery',
                            'name': 'Home Delivery',
                            'days': 4,
                            'description': 'Location not available. 4 Day(s) Delivery'
                        }
                    ]

                warehouse_data.append({
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'city': warehouse.partner_id.city or 'N/A',
                    'available_quantity': available_qty,
                    'delivery_options': delivery_options,
                    'is_central': warehouse.is_central_warehouse
                })

            # Get product data with specific fields
            product_data = product.read([
                'id', 'name', 'display_name', 'list_price', 'standard_price',
                'description_sale', 'image_1920', 'attribute_line_ids',
                'product_variant_ids', 'alternative_product_ids',
                'optional_product_ids'
            ])[0]
            
            # Add attributes data if they exist
            attributes_data = []
            weight_variants = {}
            
            if product.attribute_line_ids:
                for attribute_line in product.attribute_line_ids:
                    values_data = []
                    for value in attribute_line.value_ids:
                        # Find variants that have this attribute value
                        variants_with_value = product.product_variant_ids.filtered(
                            lambda v: value in v.product_template_attribute_value_ids.product_attribute_value_id
                        )
                        
                        variant_info = []
                        for variant in variants_with_value:
                            variant_info.append({
                                'variant_id': variant.id,
                                'price': variant.list_price,
                                'price_per_unit': variant.list_price,
                                'weight': value.name,
                                'currency': variant.currency_id.name
                            })
                            
                            # Build weight variants map
                            if attribute_line.attribute_id.name.lower() == 'weight':
                                weight_variants[value.name] = {
                                    'variant_id': variant.id,
                                    'price': variant.list_price,
                                    'price_per_unit': variant.list_price
                                }
                        
                        values_data.append({
                            'value_id': value.id,
                            'value_name': value.name,
                            'variants': variant_info
                        })
                    
                    attributes_data.append({
                        'attribute_id': attribute_line.attribute_id.id,
                        'attribute_name': attribute_line.attribute_id.name,
                        'values': values_data
                    })
                
                product_data['attributes'] = attributes_data
                product_data['weight_variants'] = weight_variants

            # Process alternative products
            alternative_data = []
            for alt_product in product.alternative_product_ids:
                alt_product_data = alt_product.read([
                    'id', 'name', 'image_1920', 'description_sale', 
                    'list_price', 'standard_price'
                ])[0]
                if alt_product_data.get('image_1920'):
                    alt_product_data['image_1920'] = f"/web/image/product.template/{alt_product_data['id']}/image_1920"
                alternative_data.append(alt_product_data)

            # Process trending (optional) products
            trending_data = []
            for trend_product in product.optional_product_ids:
                trend_product_data = trend_product.read([
                    'id', 'name', 'image_1920', 'description_sale',
                    'list_price', 'standard_price'
                ])[0]
                if trend_product_data.get('image_1920'):
                    trend_product_data['image_1920'] = f"/web/image/product.template/{trend_product_data['id']}/image_1920"
                trending_data.append(trend_product_data)

            # Return JSON response
            return json.dumps({
                'status': 'success',
                'data': {
                    'product': product_data,
                    'warehouses': warehouse_data,
                    'total_warehouses': len(warehouse_data),
                    'has_stock': any(w['available_quantity'] > 0 for w in warehouse_data),
                    'alternative_products': alternative_data,
                    'trending_products': trending_data
                }
            }, default=str)
            
        except Exception as e:
            _logger.error("Error in shop_product_details_api: %s", str(e), exc_info=True)
            return json.dumps({
                'status': 'error',
                'message': "Unable to retrieve product details. Please try again later.",
                'error': str(e)
            })

    # @http.route('/api/v1/cart', type='http', auth='user', csrf=False)
    # def get_cart(self):
    #     """Get user's shopping cart"""
    #     try:
    #         SaleOrder = request.env['sale.order'].sudo()
    #         cart = SaleOrder.search([
    #             ('partner_id', '=', request.env.user.partner_id.id),
    #             ('state', '=', 'draft'),
    #         ], limit=1)

    #         # Get all integrated/active providers  change by manali 
    #         providers = request.env['payment.provider'].sudo().search([('is_published', '=', True)])  # or use ('is_integrated', '=', True)
    #         # providers = request.env['payment.provider'].sudo().search()  # or use ('is_integrated', '=', True)

    #         provider_data = [{
    #         'id': provider.id,
    #         'name': provider.name,
    #         'code': provider.code,  # optional: like 'stripe', 'razorpay', etc.
    #         'company_id': provider.company_id.id,
    #         'company_name': provider.company_id.name,
    #         # add any other fields you need
    #         } for provider in providers]


    #         # Get user's vault account and metal balances
    #         vault_account = request.env['user.vault.account'].sudo().search([
    #             ('user_id', '=', request.env.user.id)
    #         ], limit=1)

    #         vault_data = {
    #             'total_invested': 0.0,
    #             'total_wallet': 0.0,
    #             'metal_balances': []
    #         }

    #         if vault_account:
    #             vault_data.update({
    #                 'total_invested': vault_account.total_invested_amount,
    #                 'total_wallet': vault_account.total_wallet_amount,
    #                 'metal_balances': [{
    #                     'metal_id': balance.metal_id.id,
    #                     'metal_name': balance.metal_id.name,
    #                     'balance': balance.balance,
    #                     'current_price': balance.metal_current_price,
    #                     'wallet_amount': balance.wallet_amount
    #                 } for balance in vault_account.metal_balances_ids]
    #             })



    #         if not cart:
    #             return json.dumps({
    #                 'status': 'success',
    #                 'data': {
    #                     'items': [],
    #                     'total': 0.0,
    #                     'count': 0,
    #                     'vault': vault_data
    #                 }
    #             })

    #         items = []
    #         for line in cart.order_line:
    #             product = line.product_id
    #             alternative_products = product.alternative_product_ids
    #             alternative_data = []
    #             for alt_product in alternative_products:
    #                 alt_product_data = alt_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #                 # Add image URLs for alternative products
    #                 for key in list(alt_product_data.keys()):
    #                     if key.startswith('image') and alt_product_data.get(key):
    #                         alt_product_data[key] = f"/web/image/product.template/{alt_product_data['id']}/{key}"
    #                 alternative_data.append(alt_product_data)
    #             items.append({
    #                 'line_id': line.id,
    #                 'product_id': product.id,
    #                 'name': product.name,
    #                 'quantity': line.product_uom_qty,
    #                 'price_unit': line.price_unit,
    #                 'price_subtotal': line.price_subtotal,
    #                 'image_url': f"/web/image/product.product/{product.id}/image_128",
    #                 'product_uom': line.product_uom.name,
    #                 # 'alter_products':product.alternative_data
    #             })

    #         return json.dumps({
    #             'status': 'success',
    #             'data': {
    #                 'cart_id': cart.id,
    #                 'items': items,
    #                 'total': cart.amount_total,
    #                 'subtotal': cart.amount_untaxed,
    #                 'tax': cart.amount_tax,
    #                 'count': len(items),
    #                 'vault': vault_data,
    #                 'providers':provider_data,
    #                 'recievers_name':cart.x_recievers_name,
    #                 'recievers_mobile':cart.x_recievers_mobile,
    #             }
    #         })
    #     except Exception as e:
    #         _logger.error("Error accessing cart API: %s", str(e))
    #         return json.dumps({
    #             'status': 'error',
    #             'message': _("Unable to retrieve cart. Please try again later.")
    #         })
    #23rd april commenting cart api 

    # @http.route('/api/v1/cart', type='http', auth='user', csrf=False)
    # def get_cart(self):
    #     """Get user's shopping cart"""
    #     try:
    #         SaleOrder = request.env['sale.order'].sudo()
    #         cart = SaleOrder.search([
    #             ('partner_id', '=', request.env.user.partner_id.id),
    #             ('state', '=', 'draft'),
    #         ], limit=1)

    #         # Get all integrated/active providers  change by manali
    #         providers = request.env['payment.provider'].sudo().search([('is_published', '=', True)])  # or use ('is_integrated', '=', True)
    #         provider_data = [{
    #             'id': provider.id,
    #             'name': provider.name,
    #             'code': provider.code,  # optional: like 'stripe', 'razorpay', etc.
    #             'company_id': provider.company_id.id,
    #             'company_name': provider.company_id.name,
    #         } for provider in providers]

    #         # Get user's vault account and metal balances
    #         vault_account = request.env['user.vault.account'].sudo().search([
    #             ('user_id', '=', request.env.user.id)
    #         ], limit=1)

    #         vault_data = {
    #             'total_invested': 0.0,
    #             'total_wallet': 0.0,
    #             'metal_balances': []
    #         }

    #         if vault_account:
    #             vault_data.update({
    #                 'total_invested': vault_account.total_invested_amount,
    #                 'total_wallet': vault_account.total_wallet_amount,
    #                 'metal_balances': [{
    #                     'metal_id': balance.metal_id.id,
    #                     'metal_name': balance.metal_id.name,
    #                     'balance': balance.balance,
    #                     'current_price': balance.metal_current_price,
    #                     'wallet_amount': balance.wallet_amount
    #                 } for balance in vault_account.metal_balances_ids]
    #             })

    #         if not cart:
    #             return json.dumps({
    #                 'status': 'success',
    #                 'data': {
    #                     'items': [],
    #                     'total': 0.0,
    #                     'count': 0,
    #                     'vault': vault_data
    #                 }
    #             })

    #         items = []
    #         for line in cart.order_line:
    #             product = line.product_id
    #             alternative_products = product.alternative_product_ids

    #             # Process alternative products
    #             alternative_data = []
    #             for alt_product in alternative_products:
    #                 alt_product_data = alt_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
    #                 # Add image URLs for alternative products
    #                 for key in list(alt_product_data.keys()):
    #                     if key.startswith('image') and alt_product_data.get(key):
    #                         alt_product_data[key] = f"/web/image/product.template/{alt_product_data['id']}/{key}"
    #                 alternative_data.append(alt_product_data)

    #             items.append({
    #                 'line_id': line.id,
    #                 'product_id': product.id,
    #                 'name': product.name,
    #                 'quantity': line.product_uom_qty,
    #                 'price_unit': line.price_unit,
    #                 'price_subtotal': line.price_subtotal,
    #                 'image_url': f"/web/image/product.product/{product.id}/image_128",
    #                 'product_uom': line.product_uom.name,
    #                 'alternative_products': alternative_data  # Add alternative products here
    #             })

    #         return json.dumps({
    #             'status': 'success',
    #             'data': {
    #                 'cart_id': cart.id,
    #                 'items': items,
    #                 'total': cart.amount_total,
    #                 'subtotal': cart.amount_untaxed,
    #                 'tax': cart.amount_tax,
    #                 'count': len(items),
    #                 'vault': vault_data,
    #                 'providers': provider_data,
    #                 'recievers_name': cart.x_recievers_name,
    #                 'recievers_mobile': cart.x_recievers_mobile,
    #             }
    #         })
    #     except Exception as e:
    #         _logger.error("Error accessing cart API: %s", str(e))
    #         return json.dumps({
    #             'status': 'error',
    #             'message': _("Unable to retrieve cart. Please try again later.")
    #         })

    # changing api : oneclickgold my cart is not working : 10/6 

    @http.route('/api/v1/cart', type='http', auth='user', csrf=False)
    def get_cart(self):
        """Get user's shopping cart"""
        try:
            # Add logging to track execution
            _logger.info("Cart API called for user: %s", request.env.user.id)
            
            # Check if user is authenticated
            if not request.env.user or request.env.user._is_public():
                _logger.error("Unauthenticated user trying to access cart")
                return json.dumps({
                    'status': 'error',
                    'message': 'Authentication required'
                })

            SaleOrder = request.env['sale.order'].sudo()
            
            # Check if partner exists
            if not request.env.user.partner_id:
                _logger.error("User %s has no partner_id", request.env.user.id)
                return json.dumps({
                    'status': 'error',
                    'message': 'User profile incomplete'
                })
                
            _logger.info("Searching for cart for partner: %s", request.env.user.partner_id.id)
            
            cart = SaleOrder.search([
                ('partner_id', '=', request.env.user.partner_id.id),
                ('state', '=', 'draft'),
            ], limit=1)

            _logger.info("Found cart: %s", cart.id if cart else "No cart found")

            # Get all integrated/active providers
            try:
                providers = request.env['payment.provider'].sudo().search([('is_published', '=', True)])
                provider_data = [{
                    'id': provider.id,
                    'name': provider.name,
                    'code': provider.code,
                    'company_id': provider.company_id.id if provider.company_id else False,
                    'company_name': provider.company_id.name if provider.company_id else '',
                } for provider in providers]
                _logger.info("Found %d payment providers", len(providers))
            except Exception as provider_error:
                _logger.error("Error fetching payment providers: %s", str(provider_error))
                provider_data = []

            # Get user's vault account and metal balances
            vault_data = {
                'total_invested': 0.0,
                'total_wallet': 0.0,
                'metal_balances': []
            }
            
            try:
                vault_account = request.env['user.vault.account'].sudo().search([
                    ('user_id', '=', request.env.user.id)
                ], limit=1)

                if vault_account:
                    vault_data.update({
                        'total_invested': vault_account.total_invested_amount or 0.0,
                        'total_wallet': vault_account.total_wallet_amount or 0.0,
                        'metal_balances': [{
                            'metal_id': balance.metal_id.id if balance.metal_id else False,
                            'metal_name': balance.metal_id.name if balance.metal_id else '',
                            'balance': balance.balance or 0.0,
                            'current_price': balance.metal_current_price or 0.0,
                            'wallet_amount': balance.wallet_amount or 0.0
                        } for balance in vault_account.metal_balances_ids]
                    })
                    _logger.info("Vault data loaded successfully")
            except Exception as vault_error:
                _logger.error("Error fetching vault data: %s", str(vault_error))
                # Continue with empty vault data

            if not cart:
                _logger.info("No cart found, returning empty cart")
                return json.dumps({
                    'status': 'success',
                    'data': {
                        'items': [],
                        'total': 0.0,
                        'count': 0,
                        'vault': vault_data,
                        'providers': provider_data
                    }
                })

            items = []
            try:
                for line in cart.order_line:
                    product = line.product_id
                    
                    # Process alternative products safely
                    alternative_data = []
                    try:
                        alternative_products = product.alternative_product_ids
                        for alt_product in alternative_products:
                            try:
                                alt_product_data = alt_product.read(['id', 'name', 'image_1920', 'description', 'list_price', 'standard_price'])[0]
                                # Add image URLs for alternative products
                                for key in list(alt_product_data.keys()):
                                    if key.startswith('image') and alt_product_data.get(key):
                                        alt_product_data[key] = f"/web/image/product.template/{alt_product_data['id']}/{key}"
                                alternative_data.append(alt_product_data)
                            except Exception as alt_error:
                                _logger.warning("Error processing alternative product %s: %s", alt_product.id, str(alt_error))
                                continue
                    except Exception as alt_products_error:
                        _logger.warning("Error fetching alternative products for product %s: %s", product.id, str(alt_products_error))

                    items.append({
                        'line_id': line.id,
                        'product_id': product.id,
                        'name': product.name or '',
                        'quantity': line.product_uom_qty or 0.0,
                        'price_unit': line.price_unit or 0.0,
                        'price_subtotal': line.price_subtotal or 0.0,
                        'image_url': f"/web/image/product.product/{product.id}/image_128",
                        'product_uom': line.product_uom.name if line.product_uom else '',
                        'alternative_products': alternative_data
                    })
                    
                _logger.info("Processed %d cart items", len(items))
                
            except Exception as items_error:
                _logger.error("Error processing cart items: %s", str(items_error))
                items = []

            return json.dumps({
                'status': 'success',
                'data': {
                    'cart_id': cart.id,
                    'items': items,
                    'total': cart.amount_total or 0.0,
                    'subtotal': cart.amount_untaxed or 0.0,
                    'tax': cart.amount_tax or 0.0,
                    'count': len(items),
                    'vault': vault_data,
                    'providers': provider_data,
                    'recievers_name': getattr(cart, 'x_recievers_name', '') or '',
                    'recievers_mobile': getattr(cart, 'x_recievers_mobile', '') or '',
                }
            })
            
        except Exception as e:
            # More detailed error logging
            _logger.error("Error accessing cart API: %s", str(e))
            _logger.error("Full traceback: %s", traceback.format_exc())
            return json.dumps({
                'status': 'error',
                'message': _("Unable to retrieve cart. Please try again later."),
                'debug_info': str(e) if request.env.user.has_group('base.group_system') else None
            })

    @http.route('/api/v1/cart/add', type='json', auth='user', csrf=False, methods=['POST'])
    def add_to_cart(self, product_id, quantity=1):
        """Add item to cart"""
        try:
            product = request.env['product.product'].sudo().browse(int(product_id))
            if not product.exists():
                return {'status': 'error', 'message': 'Product not found'}

            if quantity <= 0:
                return {'status': 'error', 'message': 'Invalid quantity'}

            SaleOrder = request.env['sale.order'].sudo()
            cart = SaleOrder.search([
                ('partner_id', '=', request.env.user.partner_id.id),
                ('state', '=', 'draft'),
                # ('is_cart', '=', True)
            ], limit=1)

            if not cart:
                cart = SaleOrder.create({
                    'partner_id': request.env.user.partner_id.id,
                    # 'is_cart': True
                })

            # Check if product already exists in cart
            cart_line = cart.order_line.filtered(lambda l: l.product_id.id == product.id)
            if cart_line:
                cart_line.product_uom_qty += quantity
            else:
                cart.order_line = [(0, 0, {
                    'product_id': product.id,
                    'product_uom_qty': quantity,
                    'price_unit': product.list_price
                })]

            return {
                'status': 'success',
                'message': 'Product added to cart',
                'data': {
                    'cart_id': cart.id,
                    'total': cart.amount_total,
                    'count': len(cart.order_line)
                }
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    @http.route('/api/v1/cart/update', type='json', auth='user', csrf=False, methods=['POST'])
    def update_cart(self, line_id, quantity):
        """Update cart item quantity"""
        try:
            line = request.env['sale.order.line'].sudo().browse(int(line_id))
            if not line.exists():
                return {'status': 'error', 'message': 'Cart item not found'}

            if quantity <= 0:
                line.unlink()
            else:
                line.product_uom_qty = quantity

            cart = line.order_id
            return {
                'status': 'success',
                'message': 'Cart updated',
                'data': {
                    'total': cart.amount_total,
                    'count': len(cart.order_line)
                }
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    @http.route('/api/v1/cart/remove/<int:line_id>', type='http', auth='user', csrf=False, methods=['DELETE'])
    def remove_from_cart(self, line_id):
        """Remove item from cart"""
        try:
            line = request.env['sale.order.line'].sudo().browse(int(line_id))
            if not line.exists():
                return json.dumps({'status': 'error', 'message': 'Cart item not found'})

            # Check if the cart belongs to the current user
            if line.order_id.partner_id.id != request.env.user.partner_id.id:
                return json.dumps({'status': 'error', 'message': 'Unauthorized access'})

            cart = line.order_id
            line.unlink()

            return json.dumps({
                'status': 'success',
                'message': 'Item removed from cart',
                'data': {
                    'total': cart.amount_total,
                    'count': len(cart.order_line)
                }
            })
        except Exception as e:
            return json.dumps({'status': 'error', 'message': str(e)})
    
    # @http.route('/api/v1/cart/place_order', type='json', auth='user', csrf=False, methods=['POST'])
    # def place_order(self, **kwargs):
    #     """Place order from cart with payment integration"""
    #     try:
    #         SaleOrder = request.env['sale.order'].sudo()
            
    #         # Get current draft cart
    #         cart = SaleOrder.search([
    #             ('partner_id', '=', request.env.user.partner_id.id),
    #             ('state', '=', 'draft')
    #         ], limit=1)

    #         _logger.info('Cart: %s', cart)

    #         if not cart:
    #             return {'status': 'error', 'message': 'No items in cart'}

    #         if not cart.order_line:
    #             return {'status': 'error', 'message': 'Cart is empty'}

    #         # Validate shipping address
    #         shipping_address_id = kwargs.get('shipping_address_id')
    #         if not shipping_address_id:
    #             return {'status': 'error', 'message': 'Shipping address is required'}

    #         shipping_address = request.env['res.partner'].sudo().browse(int(shipping_address_id))
    #         if not shipping_address.exists() or shipping_address.parent_id.id != request.env.user.partner_id.id:
    #             return {'status': 'error', 'message': 'Invalid shipping address'}

    #         billing_address_id = kwargs.get('billing_address_id')
    #         if not billing_address_id:
    #             billing_address_id = shipping_address_id
    #         billing_address = request.env['res.partner'].sudo().browse(int(billing_address_id))
    #         if not billing_address.exists() or billing_address.parent_id.id != request.env.user.partner_id.id:
    #             return {'status': 'error', 'message': 'Invalid billing address'}

    #         # Update order with shipping address and any additional info
    #         cart.write({
    #             'partner_shipping_id': shipping_address.id,
    #             'partner_invoice_id': billing_address.id,
    #             'note': kwargs.get('note', ''),
    #         })

    #         # Validate stock availability
    #         StockQuant = request.env['stock.quant'].sudo()
    #         warehouse = cart.warehouse_id
            
    #         for line in cart.order_line:
    #             if not line.product_id.type in ['product', 'consu']:
    #                 continue
    #             quants = StockQuant.search([
    #                     ('product_id', '=', line.product_id.id),
    #                     ('location_id.usage', '=', 'internal'),
    #                     ('location_id.warehouse_id', '=', warehouse.id)
    #                 ])
    #             available_qty = sum(quants.mapped('quantity'))
                
    #             if available_qty < line.product_uom_qty:
    #                 return {
    #                     'status': 'error',
    #                     'message': f'Insufficient stock for {line.product_id.name}. Available: {available_qty}'
    #                 }

    #         # Get payment provider
    #         provider_id = kwargs.get('provider_id')
    #         if not provider_id:
    #             return {'status': 'error', 'message': 'Payment provider is required'}

    #         provider = request.env['payment.provider'].sudo().browse(int(provider_id))
    #         if not provider.exists():
    #             return {'status': 'error', 'message': 'Invalid payment provider'}

    #         # Check if it's cash on delivery
    #         is_cod = provider.code == 'cod'

    #         # For non-COD payments, create transaction first
    #         if not is_cod:

    #             payment_method = request.env['payment.method'].sudo().search([
    #                 ('provider_ids', 'in', [provider.id])
    #             ], limit=1)

    #             if not payment_method:

    #                 return {'status': 'error', 'message': 'Payment method not found'}


    #             transaction = request.env['payment.transaction'].sudo().search([
    #                 ('sale_order_ids', 'in', [cart.id]),
    #                 ('state', '=', 'draft')
    #             ], limit=1)

    #             if not transaction:

    #                 # Initialize payment transaction with payment method
    #                 transaction = request.env['payment.transaction'].sudo().create({
    #                     'reference': cart.name,
    #                     'sale_order_ids': [(4, cart.id)],
    #                     'amount': cart.amount_total,
    #                     'currency_id': cart.currency_id.id,
    #                     'partner_id': request.env.user.partner_id.id,
    #                     'provider_id': provider.id,
    #                     'payment_method_id': payment_method.id,  # Add payment method
    #                     'operation': 'online_direct',
    #                     'state': 'draft',
    #                     # Add partner details
    #                     'partner_name': request.env.user.partner_id.name,
    #                     'partner_lang': request.env.user.partner_id.lang,
    #                     'partner_email': request.env.user.partner_id.email,
    #                     'partner_address': billing_address.street,
    #                     'partner_zip': billing_address.zip,
    #                     'partner_city': billing_address.city,
    #                     'partner_state_id': billing_address.state_id.id,
    #                     'partner_country_id': billing_address.country_id.id,
    #                     'partner_phone': billing_address.phone or billing_address.mobile,
    #                 })

    #             # Get the payment processing values
    #             processing_values = transaction._get_processing_values()

    #             _logger.info('Processing values: %s', processing_values)

    #             # Return payment data without confirming order
    #             payment_data = {
    #                 'transaction_id': transaction.id,
    #                 'reference': transaction.reference,
    #                 'amount': transaction.amount,
    #                 'currency': transaction.currency_id.name,
    #                 'provider_id': provider.id,
    #                 'provider_name': provider.name,
    #                 'payment_method_id': payment_method.id,
    #                 'payment_method_name': payment_method.name,
    #                 'payment_url': '/shop/payment/',
    #                 'return_url': f'/shop/payment/validate?tx_id={transaction.id}',
    #                 'state': transaction.state,
    #             }

    #             return {
    #                 'status': 'success',
    #                 'message': 'Payment initiated',
    #                 'data': {
    #                     # 'order_id': cart.id,
    #                     # 'order_name': cart.name,
    #                     'amount_total': cart.amount_total,
    #                     'requires_payment': True,
    #                     'payment': payment_data,
    #                     'delivery_address': {
    #                         'name': shipping_address.name,
    #                         'street': shipping_address.street,
    #                         'city': shipping_address.city,
    #                         'state': shipping_address.state_id.name if shipping_address.state_id else '',
    #                         'zip': shipping_address.zip,
    #                         'country': shipping_address.country_id.name if shipping_address.country_id else ''
    #                     }
    #                 }
    #             }
    #         else:
    #             # For COD, confirm order directly
    #             cart.action_confirm()
                
    #             return {
    #                 'status': 'success',
    #                 'message': 'Order placed successfully',
    #                 'data': {
    #                     # 'order_id': cart.id,
    #                     # 'order_name': cart.name,
    #                     'amount_total': cart.amount_total,
    #                     'requires_payment': False,
    #                     'payment_method': 'Cash on Delivery',
    #                     'delivery_address': {
    #                         'name': shipping_address.name,
    #                         'street': shipping_address.street,
    #                         'city': shipping_address.city,
    #                         'state': shipping_address.state_id.name if shipping_address.state_id else '',
    #                         'zip': shipping_address.zip,
    #                         'country': shipping_address.country_id.name if shipping_address.country_id else ''
    #                     }
    #                 }
    #             }

    #     except Exception as e:
    #         _logger.error("Error placing order: %s", str(e))
    #         return {'status': 'error', 'message': str(e)}

    # changes by manali in place ordr : 
    # @http.route('/api/v1/cart/place_order', type='json', auth='user', csrf=False, methods=['POST'])
    # def place_order(self, **kwargs):
    #     """Place order from cart with payment integration - Enhanced with order items in response"""
    #     try:
    #         SaleOrder = request.env['sale.order'].sudo()
            
    #         # Get cart items from request
    #         cart_items = kwargs.get('cart_items', [])
    #         total_amount = kwargs.get('total_amount', 0)
            
    #         # Validate cart items are provided
    #         if not cart_items:
    #             return {'status': 'error', 'message': 'Cart items are required'}
            
    #         if not total_amount or total_amount <= 0:
    #             return {'status': 'error', 'message': 'Total amount is required and must be greater than 0'}
            
    #         # Validate cart items structure
    #         for item in cart_items:
    #             required_fields = ['product_id', 'quantity', 'unit_price']
    #             if not all(field in item for field in required_fields):
    #                 return {'status': 'error', 'message': f'Each cart item must have: {", ".join(required_fields)}'}
                
    #             if item['quantity'] <= 0:
    #                 return {'status': 'error', 'message': f'Quantity must be greater than 0 for product {item.get("product_name", item["product_id"])}'}
                
    #             if item['unit_price'] <= 0:
    #                 return {'status': 'error', 'message': f'Unit price must be greater than 0 for product {item.get("product_name", item["product_id"])}'}

    #         # Get current draft cart or create new one
    #         cart = SaleOrder.search([
    #             ('partner_id', '=', request.env.user.partner_id.id),
    #             ('state', '=', 'draft')
    #         ], limit=1)

    #         if not cart:
    #             # Create new cart if none exists
    #             cart = SaleOrder.create({
    #                 'partner_id': request.env.user.partner_id.id,
    #                 'state': 'draft',
    #             })

    #         # Clear existing order lines
    #         cart.order_line.unlink()

    #         # Validate and add cart items to order
    #         Product = request.env['product.product'].sudo()
    #         StockQuant = request.env['stock.quant'].sudo()
    #         warehouse = cart.warehouse_id
            
    #         calculated_total = 0
    #         order_lines_data = []
            
    #         for item in cart_items:
    #             # Validate product exists
    #             product = Product.browse(int(item['product_id']))
    #             if not product.exists():
    #                 return {'status': 'error', 'message': f'Product with ID {item["product_id"]} not found'}
                
    #             # Validate stock availability for stockable products
    #             if product.type in ['product', 'consu']:
    #                 quants = StockQuant.search([
    #                     ('product_id', '=', product.id),
    #                     ('location_id.usage', '=', 'internal'),
    #                     ('location_id.warehouse_id', '=', warehouse.id)
    #                 ])
    #                 available_qty = sum(quants.mapped('quantity'))
                    
    #                 if available_qty < item['quantity']:
    #                     return {
    #                         'status': 'error',
    #                         'message': f'Insufficient stock for {product.name}. Available: {available_qty}, Requested: {item["quantity"]}'
    #                     }
                
    #             # Calculate line total
    #             line_total = item['quantity'] * item['unit_price']
    #             calculated_total += line_total
                
    #             # Prepare order line data
    #             order_line_data = {
    #                 'product_id': product.id,
    #                 'name': item.get('product_name', product.name),
    #                 'product_uom_qty': item['quantity'],
    #                 'price_unit': item['unit_price'],
    #                 'product_uom': product.uom_id.id,
    #                 'order_id': cart.id,
    #             }
    #             order_lines_data.append((0, 0, order_line_data))
            
    #         _logger.info('Order products details : %s', order_lines_data)

    #         # Validate total amount matches calculated total (with small tolerance for rounding)
    #         if abs(calculated_total - total_amount) > 0.01:
    #             return {
    #                 'status': 'error', 
    #                 'message': f'Total amount mismatch. Calculated: {calculated_total}, Provided: {total_amount}'
    #             }

    #         # Update cart with order lines
    #         cart.write({
    #             'order_line': order_lines_data
    #         })
            
    #         # IMPORTANT: Recalculate order totals after adding lines
    #         # cart._amount_all()
            
    #         # Handle shipping address - flexible approach
    #         shipping_address = None
    #         shipping_address_id = kwargs.get('shipping_address_id')
    #         shipping_address_data = kwargs.get('shipping_address')
            
    #         if shipping_address_id:
    #             # Use existing address by ID
    #             shipping_address = request.env['res.partner'].sudo().browse(int(shipping_address_id))
    #             if not shipping_address.exists() or shipping_address.parent_id.id != request.env.user.partner_id.id:
    #                 return {'status': 'error', 'message': 'Invalid shipping address'}
    #         elif shipping_address_data:
    #             # Create new address from provided data
    #             required_address_fields = ['name', 'street', 'city']
    #             if not all(field in shipping_address_data for field in required_address_fields):
    #                 return {'status': 'error', 'message': f'Shipping address must have: {", ".join(required_address_fields)}'}
                
    #             # Create new shipping address
    #             address_vals = {
    #                 'name': shipping_address_data['name'],
    #                 'street': shipping_address_data['street'],
    #                 'street2': shipping_address_data.get('street2', ''),
    #                 'city': shipping_address_data['city'],
    #                 'zip': shipping_address_data.get('zip', ''),
    #                 'phone': shipping_address_data.get('phone', ''),
    #                 'mobile': shipping_address_data.get('mobile', ''),
    #                 'email': shipping_address_data.get('email', ''),
    #                 'parent_id': request.env.user.partner_id.id,
    #                 'type': 'delivery',
    #             }
                
    #             # Handle state
    #             state_id = shipping_address_data.get('state_id')
    #             if state_id:
    #                 address_vals['state_id'] = int(state_id)
                
    #             # Handle country
    #             country_id = shipping_address_data.get('country_id')
    #             if country_id:
    #                 address_vals['country_id'] = int(country_id)
                
    #             shipping_address = request.env['res.partner'].sudo().create(address_vals)
    #         else:
    #             return {'status': 'error', 'message': 'Shipping address is required (provide shipping_address_id or shipping_address data)'}

    #         # Handle billing address - flexible approach
    #         billing_address = None
    #         billing_address_id = kwargs.get('billing_address_id')
    #         billing_address_data = kwargs.get('billing_address')
            
    #         if billing_address_id:
    #             # Use existing address by ID
    #             billing_address = request.env['res.partner'].sudo().browse(int(billing_address_id))
    #             if not billing_address.exists() or billing_address.parent_id.id != request.env.user.partner_id.id:
    #                 return {'status': 'error', 'message': 'Invalid billing address'}
    #         elif billing_address_data:
    #             # Create new address from provided data
    #             required_address_fields = ['name', 'street', 'city']
    #             if not all(field in billing_address_data for field in required_address_fields):
    #                 return {'status': 'error', 'message': f'Billing address must have: {", ".join(required_address_fields)}'}
                
    #             # Create new billing address
    #             address_vals = {
    #                 'name': billing_address_data['name'],
    #                 'street': billing_address_data['street'],
    #                 'street2': billing_address_data.get('street2', ''),
    #                 'city': billing_address_data['city'],
    #                 'zip': billing_address_data.get('zip', ''),
    #                 'phone': billing_address_data.get('phone', ''),
    #                 'mobile': billing_address_data.get('mobile', ''),
    #                 'email': billing_address_data.get('email', ''),
    #                 'parent_id': request.env.user.partner_id.id,
    #                 'type': 'invoice',
    #             }
                
    #             # Handle state
    #             state_id = billing_address_data.get('state_id')
    #             if state_id:
    #                 address_vals['state_id'] = int(state_id)
                
    #             # Handle country
    #             country_id = billing_address_data.get('country_id')
    #             if country_id:
    #                 address_vals['country_id'] = int(country_id)
                
    #             billing_address = request.env['res.partner'].sudo().create(address_vals)
    #         else:
    #             # Use shipping address as billing address if not provided
    #             billing_address = shipping_address

    #         # Update order with addresses
    #         cart.write({
    #             'partner_shipping_id': shipping_address.id,
    #             'partner_invoice_id': billing_address.id,
    #             'note': kwargs.get('note', ''),
    #         })

    #         # Prepare order items for response AFTER order lines are saved and calculated
    #         order_items = []
    #         for line in cart.order_line:
    #             order_items.append({
    #                 'id': line.id,
    #                 'product_id': line.product_id.id,
    #                 'product_name': line.name,
    #                 'product_sku': line.product_id.default_code or '',
    #                 'quantity': line.product_uom_qty,
    #                 'unit_price': line.price_unit,
    #                 'subtotal': line.price_subtotal,
    #                 'tax_amount': line.price_tax,
    #                 'total': line.price_total,
    #                 'uom_name': line.product_uom.name,
    #                 'product_image_url': f'/web/image/product.product/{line.product_id.id}/image_1920' if line.product_id.image_1920 else None,
    #             })

    #         _logger.info('Order Items prepared: %s', order_items)

    #         # Get payment provider
    #         provider_id = kwargs.get('provider_id')
    #         # Calculate total count of items
    #         total_items_count = sum(line.product_uom_qty for line in cart.order_line)

    #         if not provider_id:
    #             return {'status': 'error', 'message': 'Payment provider is required'}

    #         provider = request.env['payment.provider'].sudo().browse(int(provider_id))
    #         if not provider.exists():
    #             return {'status': 'error', 'message': 'Invalid payment provider'}

    #         # Check if it's cash on delivery
    #         is_cod = provider.code == 'cod'

    #         if is_cod:
    #             # Cash on Delivery - confirm order directly
    #             cart.action_confirm()
                
    #             return {
    #                 'status': 'success',
    #                 'message': 'Order placed successfully with Cash on Delivery',
    #                 'data': {
    #                     'order_id': cart.id,
    #                     'order_name': cart.name,
    #                     'amount_total': cart.amount_total,
    #                     'amount_untaxed': cart.amount_untaxed,
    #                     'amount_tax': cart.amount_tax,
    #                     'currency': cart.currency_id.name,
    #                     'requires_payment': False,
    #                     'payment_method': 'Cash on Delivery',
    #                     'order_items': order_items,
    #                     'total_items_count': total_items_count,
    #                     'delivery_address': {
    #                         'id': shipping_address.id,
    #                         'name': shipping_address.name,
    #                         'street': shipping_address.street,
    #                         'street2': shipping_address.street2 or '',
    #                         'city': shipping_address.city,
    #                         'state': shipping_address.state_id.name if shipping_address.state_id else '',
    #                         'zip': shipping_address.zip or '',
    #                         'country': shipping_address.country_id.name if shipping_address.country_id else '',
    #                         'phone': shipping_address.phone or '',
    #                         'mobile': shipping_address.mobile or '',
    #                         'email': shipping_address.email or ''
    #                     },
    #                     'billing_address': {
    #                         'id': billing_address.id,
    #                         'name': billing_address.name,
    #                         'street': billing_address.street,
    #                         'street2': billing_address.street2 or '',
    #                         'city': billing_address.city,
    #                         'state': billing_address.state_id.name if billing_address.state_id else '',
    #                         'zip': billing_address.zip or '',
    #                         'country': billing_address.country_id.name if billing_address.country_id else '',
    #                         'phone': billing_address.phone or '',
    #                         'mobile': billing_address.mobile or '',
    #                         'email': billing_address.email or ''
    #                     },
    #                     'order_note': cart.note or '',
    #                     'order_date': cart.date_order.strftime('%Y-%m-%d %H:%M:%S') if cart.date_order else '',
    #                 }
    #             }
            
    #         else:
    #             # Online Payment - create transaction
    #             PaymentTransaction = request.env['payment.transaction'].sudo()
                
    #             # Create payment transaction
    #             transaction_vals = {
    #                 'reference': cart.name,
    #                 'amount': cart.amount_total,
    #                 'currency_id': cart.currency_id.id,
    #                 'provider_id': provider.id,
    #                 'partner_id': request.env.user.partner_id.id,
    #                 'sale_order_ids': [(6, 0, [cart.id])],
    #                 'state': 'draft',
    #             }
                
    #             transaction = PaymentTransaction.create(transaction_vals)
                
    #             # Confirm the order
    #             cart.action_confirm()
                
    #             return {
    #                 'status': 'success',
    #                 'message': 'Payment initiated',
    #                 'data': {
    #                     'order_id': cart.id,
    #                     'order_name': cart.name,
    #                     'amount_total': cart.amount_total,
    #                     'amount_untaxed': cart.amount_untaxed,
    #                     'amount_tax': cart.amount_tax,
    #                     'currency': cart.currency_id.name,
    #                     'requires_payment': True,
    #                     'payment_method': provider.name,
    #                     'product_qty': cart.product_uom_qty,
    #                     'order_date':cart.date_order,
    #                     # 'order_data':cart.order_line_data, #check test 
    #                     # 'total_items_count': cart.total_items_count,
    #                                             # 'order_data': cart.order_lines_data,
    #                     'order_lines': [line[2] for line in order_lines_data],  # cleaned order_lines_data
    #                     'total_items_count': total_items_count,
    #                     'order_items': order_items,  # Now included
    #                     'payment': {
    #                         'transaction_id': transaction.id,
    #                         'reference': transaction.reference,
    #                         'amount': transaction.amount,
    #                         'currency': cart.currency_id.name,
    #                         'provider_id': provider.id,
    #                         'provider_name': provider.name,
    #                         'payment_method_id': transaction.payment_method_id.id if transaction.payment_method_id else None,
    #                         'payment_method_name': transaction.payment_method_id.name if transaction.payment_method_id else provider.name,
    #                         'payment_url': '/shop/payment/',
    #                         'return_url': f'/shop/payment/validate?tx_id={transaction.id}',
    #                         'state': transaction.state
    #                     },
    #                     'delivery_address': {
    #                         'id': shipping_address.id,
    #                         'name': shipping_address.name,
    #                         'street': shipping_address.street,
    #                         'street2': shipping_address.street2 or '',
    #                         'city': shipping_address.city,
    #                         'state': shipping_address.state_id.name if shipping_address.state_id else '',
    #                         'zip': shipping_address.zip or '',
    #                         'country': shipping_address.country_id.name if shipping_address.country_id else '',
    #                         'phone': shipping_address.phone or '',
    #                         'mobile': shipping_address.mobile or '',
    #                         'email': shipping_address.email or ''
    #                     },
    #                     'billing_address': {
    #                         'id': billing_address.id,
    #                         'name': billing_address.name,
    #                         'street': billing_address.street,
    #                         'street2': billing_address.street2 or '',
    #                         'city': billing_address.city,
    #                         'state': billing_address.state_id.name if billing_address.state_id else '',
    #                         'zip': billing_address.zip or '',
    #                         'country': billing_address.country_id.name if billing_address.country_id else '',
    #                         'phone': billing_address.phone or '',
    #                         'mobile': billing_address.mobile or '',
    #                         'email': billing_address.email or ''
    #                     },
    #                     'order_note': cart.note or '',
    #                     'order_date': cart.date_order.strftime('%Y-%m-%d %H:%M:%S') if cart.date_order else '',
    #                 }
    #             }
    #         _logger.info('Order Items prepared: %s', order_items)

  
    #     except Exception as e:
    #         _logger.error("Error placing order: %s", str(e))
    #         return {'status': 'error', 'message': str(e)}

    # @http.route('/api/v1/cart/place_order', type='json', auth='user', csrf=False, methods=['POST'])
    # def place_order(self, **kwargs):
    #     """Place order from cart with payment integration - Enhanced with flexible address handling"""
    #     try:
    #         SaleOrder = request.env['sale.order'].sudo()
            
    #         # Get cart items from request
    #         cart_items = kwargs.get('cart_items', [])
    #         total_amount = kwargs.get('total_amount', 0)
            
    #         # Validate cart items are provided
    #         if not cart_items:
    #             return {'status': 'error', 'message': 'Cart items are required'}
            
    #         if not total_amount or total_amount <= 0:
    #             return {'status': 'error', 'message': 'Total amount is required and must be greater than 0'}
            
    #         # Validate cart items structure
    #         for item in cart_items:
    #             required_fields = ['product_id', 'quantity', 'unit_price']
    #             if not all(field in item for field in required_fields):
    #                 return {'status': 'error', 'message': f'Each cart item must have: {", ".join(required_fields)}'}
                
    #             if item['quantity'] <= 0:
    #                 return {'status': 'error', 'message': f'Quantity must be greater than 0 for product {item.get("product_name", item["product_id"])}'}
                
    #             if item['unit_price'] <= 0:
    #                 return {'status': 'error', 'message': f'Unit price must be greater than 0 for product {item.get("product_name", item["product_id"])}'}

    #         # Get current draft cart or create new one
    #         cart = SaleOrder.search([
    #             ('partner_id', '=', request.env.user.partner_id.id),
    #             ('state', '=', 'draft')
    #         ], limit=1)

    #         if not cart:
    #             # Create new cart if none exists
    #             cart = SaleOrder.create({
    #                 'partner_id': request.env.user.partner_id.id,
    #                 'state': 'draft',
    #             })

    #         # Clear existing order lines
    #         cart.order_line.unlink()

    #         # Validate and add cart items to order
    #         Product = request.env['product.product'].sudo()
    #         StockQuant = request.env['stock.quant'].sudo()
    #         warehouse = cart.warehouse_id
            
    #         calculated_total = 0
    #         order_lines_data = []
            
    #         for item in cart_items:
    #             # Validate product exists
    #             product = Product.browse(int(item['product_id']))
    #             if not product.exists():
    #                 return {'status': 'error', 'message': f'Product with ID {item["product_id"]} not found'}
                
    #             # Validate stock availability for stockable products
    #             if product.type in ['product', 'consu']:
    #                 quants = StockQuant.search([
    #                     ('product_id', '=', product.id),
    #                     ('location_id.usage', '=', 'internal'),
    #                     ('location_id.warehouse_id', '=', warehouse.id)
    #                 ])
    #                 available_qty = sum(quants.mapped('quantity'))
                    
    #                 if available_qty < item['quantity']:
    #                     return {
    #                         'status': 'error',
    #                         'message': f'Insufficient stock for {product.name}. Available: {available_qty}, Requested: {item["quantity"]}'
    #                     }
                
    #             # Calculate line total
    #             line_total = item['quantity'] * item['unit_price']
    #             calculated_total += line_total
                
    #             # Prepare order line data
    #             order_line_data = {
    #                 'product_id': product.id,
    #                 'name': item.get('product_name', product.name),
    #                 'product_uom_qty': item['quantity'],
    #                 'price_unit': item['unit_price'],
    #                 'product_uom': product.uom_id.id,
    #                 'order_id': cart.id,
    #             }
    #             order_lines_data.append((0, 0, order_line_data))
            
    #         # Validate total amount matches calculated total (with small tolerance for rounding)
    #         if abs(calculated_total - total_amount) > 0.01:
    #             return {
    #                 'status': 'error', 
    #                 'message': f'Total amount mismatch. Calculated: {calculated_total}, Provided: {total_amount}'
    #             }

    #         # Update cart with order lines
    #         cart.write({
    #             'order_line': order_lines_data
    #         })

    #         # Handle shipping address - flexible approach
    #         shipping_address = None
    #         shipping_address_id = kwargs.get('shipping_address_id')
    #         shipping_address_data = kwargs.get('shipping_address')
            
    #         if shipping_address_id:
    #             # Use existing address by ID
    #             shipping_address = request.env['res.partner'].sudo().browse(int(shipping_address_id))
    #             if not shipping_address.exists() or shipping_address.parent_id.id != request.env.user.partner_id.id:
    #                 return {'status': 'error', 'message': 'Invalid shipping address'}
    #         elif shipping_address_data:
    #             # Create new address from provided data
    #             required_address_fields = ['name', 'street', 'city']
    #             if not all(field in shipping_address_data for field in required_address_fields):
    #                 return {'status': 'error', 'message': f'Shipping address must have: {", ".join(required_address_fields)}'}
                
    #             # Create new shipping address
    #             address_vals = {
    #                 'name': shipping_address_data['name'],
    #                 'street': shipping_address_data['street'],
    #                 'street2': shipping_address_data.get('street2', ''),
    #                 'city': shipping_address_data['city'],
    #                 'zip': shipping_address_data.get('zip', ''),
    #                 'phone': shipping_address_data.get('phone', ''),
    #                 'mobile': shipping_address_data.get('mobile', ''),
    #                 'email': shipping_address_data.get('email', ''),
    #                 'parent_id': request.env.user.partner_id.id,
    #                 'type': 'delivery',
    #             }
                
    #             # Handle state
    #             state_id = shipping_address_data.get('state_id')
    #             if state_id:
    #                 address_vals['state_id'] = int(state_id)
                
    #             # Handle country
    #             country_id = shipping_address_data.get('country_id')
    #             if country_id:
    #                 address_vals['country_id'] = int(country_id)
                
    #             shipping_address = request.env['res.partner'].sudo().create(address_vals)
    #         else:
    #             return {'status': 'error', 'message': 'Shipping address is required (provide shipping_address_id or shipping_address data)'}

    #         # Handle billing address - flexible approach
    #         billing_address = None
    #         billing_address_id = kwargs.get('billing_address_id')
    #         billing_address_data = kwargs.get('billing_address')
            
    #         if billing_address_id:
    #             # Use existing address by ID
    #             billing_address = request.env['res.partner'].sudo().browse(int(billing_address_id))
    #             if not billing_address.exists() or billing_address.parent_id.id != request.env.user.partner_id.id:
    #                 return {'status': 'error', 'message': 'Invalid billing address'}
    #         elif billing_address_data:
    #             # Create new address from provided data
    #             required_address_fields = ['name', 'street', 'city']
    #             if not all(field in billing_address_data for field in required_address_fields):
    #                 return {'status': 'error', 'message': f'Billing address must have: {", ".join(required_address_fields)}'}
                
    #             # Create new billing address
    #             address_vals = {
    #                 'name': billing_address_data['name'],
    #                 'street': billing_address_data['street'],
    #                 'street2': billing_address_data.get('street2', ''),
    #                 'city': billing_address_data['city'],
    #                 'zip': billing_address_data.get('zip', ''),
    #                 'phone': billing_address_data.get('phone', ''),
    #                 'mobile': billing_address_data.get('mobile', ''),
    #                 'email': billing_address_data.get('email', ''),
    #                 'parent_id': request.env.user.partner_id.id,
    #                 'type': 'invoice',
    #             }
                
    #             # Handle state
    #             state_id = billing_address_data.get('state_id')
    #             if state_id:
    #                 address_vals['state_id'] = int(state_id)
                
    #             # Handle country
    #             country_id = billing_address_data.get('country_id')
    #             if country_id:
    #                 address_vals['country_id'] = int(country_id)
                
    #             billing_address = request.env['res.partner'].sudo().create(address_vals)
    #         else:
    #             # Use shipping address as billing address if not provided
    #             billing_address = shipping_address

    #         # Update order with addresses
    #         cart.write({
    #             'partner_shipping_id': shipping_address.id,
    #             'partner_invoice_id': billing_address.id,
    #             'note': kwargs.get('note', ''),
    #         })

    #         # _logger.info('Cart updated with items: %s', cart_items)
    #         # _logger.info('Cart total: %s', cart.amount_total)
    #         # _logger.info('Shipping address: %s', shipping_address.name)
    #         # _logger.info('Billing address: %s', billing_address.name)

    #         # Get payment provider
    #         provider_id = kwargs.get('provider_id')
    #         if not provider_id:
    #             return {'status': 'error', 'message': 'Payment provider is required'}

    #         provider = request.env['payment.provider'].sudo().browse(int(provider_id))
    #         if not provider.exists():
    #             return {'status': 'error', 'message': 'Invalid payment provider'}

    #         # Check if it's cash on delivery
    #         is_cod = provider.code == 'cod'
    #         is_razorpay = provider.code == 'razorpay'

            
    #         if is_cod:
    #             # Cash on Delivery - confirm order directly
    #             cart.action_confirm()
                
    #             return {
    #                 'status': 'success',
    #                 'message': 'Order placed successfully with Cash on Delivery',
    #                 'data': {
    #                     'order_id': cart.id,
    #                     'order_name': cart.name,
    #                     'amount_total': cart.amount_total,
    #                     'amount_untaxed': cart.amount_untaxed,
    #                     'amount_tax': cart.amount_tax,
    #                     'currency': cart.currency_id.name,
    #                     'requires_payment': False,
    #                     'payment_method': 'Cash on Delivery',
    #                     'order_items': order_items,
    #                     'total_items_count': total_items_count,
    #                     'delivery_address': {
    #                         'id': shipping_address.id,
    #                         'name': shipping_address.name,
    #                         'street': shipping_address.street,
    #                         'street2': shipping_address.street2 or '',
    #                         'city': shipping_address.city,
    #                         'state': shipping_address.state_id.name if shipping_address.state_id else '',
    #                         'zip': shipping_address.zip or '',
    #                         'country': shipping_address.country_id.name if shipping_address.country_id else '',
    #                         'phone': shipping_address.phone or '',
    #                         'mobile': shipping_address.mobile or '',
    #                         'email': shipping_address.email or ''
    #                     },
    #                     'billing_address': {
    #                         'id': billing_address.id,
    #                         'name': billing_address.name,
    #                         'street': billing_address.street,
    #                         'street2': billing_address.street2 or '',
    #                         'city': billing_address.city,
    #                         'state': billing_address.state_id.name if billing_address.state_id else '',
    #                         'zip': billing_address.zip or '',
    #                         'country': billing_address.country_id.name if billing_address.country_id else '',
    #                         'phone': billing_address.phone or '',
    #                         'mobile': billing_address.mobile or '',
    #                         'email': billing_address.email or ''
    #                     },
    #                     'order_note': cart.note or '',
    #                     'order_date': cart.date_order.strftime('%Y-%m-%d %H:%M:%S') if cart.date_order else '',
    #                 }
    #             }
            
    #         else:
    #             # Online Payment - create transaction
    #             PaymentTransaction = request.env['payment.transaction'].sudo()
                
    #             # Create payment transaction
    #             transaction_vals = {
    #                 'reference': cart.name,
    #                 'amount': cart.amount_total,
    #                 'currency_id': cart.currency_id.id,
    #                 'provider_id': provider.id,
    #                 'partner_id': request.env.user.partner_id.id,
    #                 'sale_order_ids': [(6, 0, [cart.id])],
    #                 'state': 'draft',
    #             }
                
    #             transaction = PaymentTransaction.create(transaction_vals)
                
    #             # Confirm the order
    #             cart.action_confirm()
                
    #             return {
    #                 'status': 'success',
    #                 'message': 'Payment initiated',
    #                 'data': {
    #                     'order_id': cart.id,
    #                     'order_name': cart.name,
    #                     'amount_total': cart.amount_total,
    #                     'amount_untaxed': cart.amount_untaxed,
    #                     'amount_tax': cart.amount_tax,
    #                     'currency': cart.currency_id.name,
    #                     'requires_payment': True,
    #                     'payment_method': provider.name,
    #                     'product_qty': cart.product_uom_qty,
    #                     'order_date':cart.date_order,
    #                     # 'order_data':cart.order_line_data, #check test 
    #                     # 'total_items_count': cart.total_items_count,
    #                                             # 'order_data': cart.order_lines_data,
    #                     'order_lines': [line[2] for line in order_lines_data],  # cleaned order_lines_data
    #                     'total_items_count': total_items_count,
    #                     'order_items': order_items,  # Now included
    #                     'payment': {
    #                         'transaction_id': transaction.id,
    #                         'reference': transaction.reference,
    #                         'amount': transaction.amount,
    #                         'currency': cart.currency_id.name,
    #                         'provider_id': provider.id,
    #                         'provider_name': provider.name,
    #                         'payment_method_id': transaction.payment_method_id.id if transaction.payment_method_id else None,
    #                         'payment_method_name': transaction.payment_method_id.name if transaction.payment_method_id else provider.name,
    #                         'payment_url': '/shop/payment/',
    #                         'return_url': f'/shop/payment/validate?tx_id={transaction.id}',
    #                         'state': transaction.state
    #                     },
    #                     'delivery_address': {
    #                         'id': shipping_address.id,
    #                         'name': shipping_address.name,
    #                         'street': shipping_address.street,
    #                         'street2': shipping_address.street2 or '',
    #                         'city': shipping_address.city,
    #                         'state': shipping_address.state_id.name if shipping_address.state_id else '',
    #                         'zip': shipping_address.zip or '',
    #                         'country': shipping_address.country_id.name if shipping_address.country_id else '',
    #                         'phone': shipping_address.phone or '',
    #                         'mobile': shipping_address.mobile or '',
    #                         'email': shipping_address.email or ''
    #                     },
    #                     'billing_address': {
    #                         'id': billing_address.id,
    #                         'name': billing_address.name,
    #                         'street': billing_address.street,
    #                         'street2': billing_address.street2 or '',
    #                         'city': billing_address.city,
    #                         'state': billing_address.state_id.name if billing_address.state_id else '',
    #                         'zip': billing_address.zip or '',
    #                         'country': billing_address.country_id.name if billing_address.country_id else '',
    #                         'phone': billing_address.phone or '',
    #                         'mobile': billing_address.mobile or '',
    #                         'email': billing_address.email or ''
    #                     },
    #                     'order_note': cart.note or '',
    #                     'order_date': cart.date_order.strftime('%Y-%m-%d %H:%M:%S') if cart.date_order else '',
    #                 }
    #             }
    #         _logger.info('Order Items prepared: %s', order_items)

    #         # Rest of the payment processing logic remains the same...
    #         # [Payment processing code continues as in the original enhanced API]

    #     except Exception as e:
    #         _logger.error("Error placing order: %s", str(e))
    #         return {'status': 'error', 'message': str(e)}


    @http.route('/api/v1/cart/place_order', type='json', auth='user', csrf=False, methods=['POST'])
    def place_order(self, **kwargs):
        """Place order from cart with payment integration - Enhanced with error handling and transaction management"""
        try:
            # Start a new savepoint for transaction management
            request.env.cr.execute('SAVEPOINT place_order_start')
            
            SaleOrder = request.env['sale.order'].sudo()
            
            # Get cart items from request
            cart_items = kwargs.get('cart_items', [])
            total_amount = kwargs.get('total_amount', 0)
            
            # Validate cart items are provided
            if not cart_items:
                return {'status': 'error', 'message': 'Cart items are required'}
            
            if not total_amount or total_amount <= 0:
                return {'status': 'error', 'message': 'Total amount is required and must be greater than 0'}
            
            # Validate cart items structure
            for item in cart_items:
                required_fields = ['product_id', 'quantity', 'unit_price']
                if not all(field in item for field in required_fields):
                    return {'status': 'error', 'message': f'Each cart item must have: {", ".join(required_fields)}'}
                
                if item['quantity'] <= 0:
                    return {'status': 'error', 'message': f'Quantity must be greater than 0 for product {item.get("product_name", item["product_id"])}'}
                
                if item['unit_price'] <= 0:
                    return {'status': 'error', 'message': f'Unit price must be greater than 0 for product {item.get("product_name", item["product_id"])}'}

            # Get current draft cart or create new one
            cart = SaleOrder.search([
                ('partner_id', '=', request.env.user.partner_id.id),
                ('state', '=', 'draft')
            ], limit=1)

            if not cart:
                # Create new cart if none exists
                cart_vals = {
                    'partner_id': request.env.user.partner_id.id,
                    'state': 'draft',
                }
                cart = SaleOrder.create(cart_vals)

            # Clear existing order lines
            if cart.order_line:
                cart.order_line.unlink()

            # Validate and prepare order lines
            Product = request.env['product.product'].sudo()
            ProductUom = request.env['uom.uom'].sudo()
            StockQuant = request.env['stock.quant'].sudo()
            warehouse = cart.warehouse_id
            
            calculated_total = 0
            order_lines_data = []
            order_items = []
            total_items_count = 0
            
            for item in cart_items:
                try:
                    # Validate product exists
                    product = Product.browse(int(item['product_id']))
                    if not product.exists():
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': f'Product with ID {item["product_id"]} not found'}
                    
                    # Ensure product has proper UOM setup
                    if not product.uom_id:
                        # Set default UOM if missing
                        default_uom = ProductUom.search([('category_id.name', '=', 'Unit')], limit=1)
                        if default_uom:
                            product.write({'uom_id': default_uom.id})
                        else:
                            request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                            return {'status': 'error', 'message': f'Product {product.name} has no valid unit of measure'}
                    
                    # Ensure product weight is set (required for shipping calculations)
                    if not hasattr(product, 'weight') or product.weight is False:
                        product.write({'weight': 0.0})
                    
                    # Validate stock availability for stockable products
                    if product.type in ['product', 'consu']:
                        try:
                            quants = StockQuant.search([
                                ('product_id', '=', product.id),
                                ('location_id.usage', '=', 'internal'),
                                ('location_id.warehouse_id', '=', warehouse.id)
                            ])
                            available_qty = sum(quants.mapped('quantity'))
                            
                            if available_qty < item['quantity']:
                                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                                return {
                                    'status': 'error',
                                    'message': f'Insufficient stock for {product.name}. Available: {available_qty}, Requested: {item["quantity"]}'
                                }
                        except Exception as stock_error:
                            _logger.warning(f"Stock check failed for product {product.id}: {str(stock_error)}")
                            # Continue without stock check in case of stock module issues
                    
                    # Calculate line total
                    line_total = item['quantity'] * item['unit_price']
                    calculated_total += line_total
                    total_items_count += item['quantity']
                    
                    # Prepare order line data with safe UOM handling
                    order_line_data = {
                        'product_id': product.id,
                        'name': item.get('product_name', product.name),
                        'product_uom_qty': item['quantity'],
                        'price_unit': item['unit_price'],
                        'product_uom': product.uom_id.id,
                        'order_id': cart.id,
                    }
                    order_lines_data.append((0, 0, order_line_data))
                    
                    # Prepare order item for response
                    order_items.append({
                        'product_id': product.id,
                        'product_name': product.name,
                        'quantity': item['quantity'],
                        'unit_price': item['unit_price'],
                        'line_total': line_total,
                        'product_image': f'/web/image/product.product/{product.id}/image_128' if product.image_128 else None
                    })
                    
                except Exception as item_error:
                    _logger.error(f"Error processing cart item {item}: {str(item_error)}")
                    request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                    return {'status': 'error', 'message': f'Error processing product {item.get("product_name", item["product_id"])}: {str(item_error)}'}
            
            # Validate total amount matches calculated total (with small tolerance for rounding)
            if abs(calculated_total - total_amount) > 0.01:
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                return {
                    'status': 'error', 
                    'message': f'Total amount mismatch. Calculated: {calculated_total}, Provided: {total_amount}'
                }

            # Handle addresses with error handling
            try:
                # Handle shipping address
                shipping_address = None
                shipping_address_id = kwargs.get('shipping_address_id')
                shipping_address_data = kwargs.get('shipping_address')
                
                if shipping_address_id:
                    shipping_address = request.env['res.partner'].sudo().browse(int(shipping_address_id))
                    if not shipping_address.exists() or shipping_address.parent_id.id != request.env.user.partner_id.id:
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': 'Invalid shipping address'}
                elif shipping_address_data:
                    required_address_fields = ['name', 'street', 'city']
                    if not all(field in shipping_address_data for field in required_address_fields):
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': f'Shipping address must have: {", ".join(required_address_fields)}'}
                    
                    address_vals = {
                        'name': shipping_address_data['name'],
                        'street': shipping_address_data['street'],
                        'street2': shipping_address_data.get('street2', ''),
                        'city': shipping_address_data['city'],
                        'zip': shipping_address_data.get('zip', ''),
                        'phone': shipping_address_data.get('phone', ''),
                        'mobile': shipping_address_data.get('mobile', ''),
                        'email': shipping_address_data.get('email', ''),
                        'parent_id': request.env.user.partner_id.id,
                        'type': 'delivery',
                    }
                    
                    if shipping_address_data.get('state_id'):
                        address_vals['state_id'] = int(shipping_address_data['state_id'])
                    if shipping_address_data.get('country_id'):
                        address_vals['country_id'] = int(shipping_address_data['country_id'])
                    
                    shipping_address = request.env['res.partner'].sudo().create(address_vals)
                else:
                    request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                    return {'status': 'error', 'message': 'Shipping address is required'}

                # Handle billing address
                billing_address = None
                billing_address_id = kwargs.get('billing_address_id')
                billing_address_data = kwargs.get('billing_address')
                
                if billing_address_id:
                    billing_address = request.env['res.partner'].sudo().browse(int(billing_address_id))
                    if not billing_address.exists() or billing_address.parent_id.id != request.env.user.partner_id.id:
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': 'Invalid billing address'}
                elif billing_address_data:
                    required_address_fields = ['name', 'street', 'city']
                    if not all(field in billing_address_data for field in required_address_fields):
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': f'Billing address must have: {", ".join(required_address_fields)}'}
                    
                    address_vals = {
                        'name': billing_address_data['name'],
                        'street': billing_address_data['street'],
                        'street2': billing_address_data.get('street2', ''),
                        'city': billing_address_data['city'],
                        'zip': billing_address_data.get('zip', ''),
                        'phone': billing_address_data.get('phone', ''),
                        'mobile': billing_address_data.get('mobile', ''),
                        'email': billing_address_data.get('email', ''),
                        'parent_id': request.env.user.partner_id.id,
                        'type': 'invoice',
                    }
                    
                    if billing_address_data.get('state_id'):
                        address_vals['state_id'] = int(billing_address_data['state_id'])
                    if billing_address_data.get('country_id'):
                        address_vals['country_id'] = int(billing_address_data['country_id'])
                    
                    billing_address = request.env['res.partner'].sudo().create(address_vals)
                else:
                    billing_address = shipping_address

            except Exception as address_error:
                _logger.error(f"Error handling addresses: {str(address_error)}")
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                return {'status': 'error', 'message': f'Error processing addresses: {str(address_error)}'}

            # Update cart with order lines and addresses in separate steps
            try:
                # First update the basic order information
                cart.write({
                    'partner_shipping_id': shipping_address.id,
                    'partner_invoice_id': billing_address.id,
                    'note': kwargs.get('note', ''),
                })
                
                # Then add order lines one by one to isolate any issues
                SaleOrderLine = request.env['sale.order.line'].sudo()
                for line_data in order_lines_data:
                    line_vals = line_data[2]  # Extract the actual line data
                    try:
                        SaleOrderLine.create(line_vals)
                    except Exception as line_error:
                        _logger.error(f"Error creating order line: {str(line_error)}")
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': f'Error adding product to order: {str(line_error)}'}
                
                # Force refresh the cart to recalculate totals
                # cart.invalidate_cache()
                
            except Exception as update_error:
                _logger.error(f"Error updating cart: {str(update_error)}")
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                return {'status': 'error', 'message': f'Error updating order: {str(update_error)}'}

            # Get payment provider
            provider_id = kwargs.get('provider_id')
            if not provider_id:
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                return {'status': 'error', 'message': 'Payment provider is required'}

            provider = request.env['payment.provider'].sudo().browse(int(provider_id))
            if not provider.exists():
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                return {'status': 'error', 'message': 'Invalid payment provider'}

            # Get payment method if provided
            # payment_method_id = kwargs.get('payment_method_id')
            payment_method_id = provider.payment_method_ids.id
            payment_method = None
            if payment_method_id:
                payment_method = request.env['payment.method'].sudo().browse(int(payment_method_id))
                if not payment_method.exists():
                    request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                    return {'status': 'error', 'message': 'Invalid payment method'}
            else:
                # Try to get default payment method for the provider
                payment_method = request.env['payment.method'].sudo().search([
                    ('provider_id', '=', provider.id)
                ], limit=1)
                
                if not payment_method and provider.code != 'cod':
                    request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                    return {'status': 'error', 'message': 'Payment method is required for online payments'}

            # Check payment method
            is_cod = provider.code == 'cod'
            
            try:
                if is_cod:
                    # Cash on Delivery - confirm order directly with error handling
                    try:
                        cart.action_confirm()
                    except Exception as confirm_error:
                        _logger.error(f"Error confirming COD order: {str(confirm_error)}")
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': f'Error confirming order: {str(confirm_error)}'}
                    
                    # Release the savepoint on success
                    request.env.cr.execute('RELEASE SAVEPOINT place_order_start')
                    
                    return {
                        'status': 'success',
                        'message': 'Order placed successfully with Cash on Delivery',
                        'data': {
                            'order_id': cart.id,
                            'order_name': cart.name,
                            'amount_total': cart.amount_total,
                            'amount_untaxed': cart.amount_untaxed,
                            'amount_tax': cart.amount_tax,
                            'currency': cart.currency_id.name,
                            'requires_payment': False,
                            'payment_method': 'Cash on Delivery',
                            'order_items': order_items,
                            'total_items_count': total_items_count,
                            'delivery_address': {
                                'id': shipping_address.id,
                                'name': shipping_address.name,
                                'street': shipping_address.street,
                                'street2': shipping_address.street2 or '',
                                'city': shipping_address.city,
                                'state': shipping_address.state_id.name if shipping_address.state_id else '',
                                'zip': shipping_address.zip or '',
                                'country': shipping_address.country_id.name if shipping_address.country_id else '',
                                'phone': shipping_address.phone or '',
                                'mobile': shipping_address.mobile or '',
                                'email': shipping_address.email or ''
                            },
                            'billing_address': {
                                'id': billing_address.id,
                                'name': billing_address.name,
                                'street': billing_address.street,
                                'street2': billing_address.street2 or '',
                                'city': billing_address.city,
                                'state': billing_address.state_id.name if billing_address.state_id else '',
                                'zip': billing_address.zip or '',
                                'country': billing_address.country_id.name if billing_address.country_id else '',
                                'phone': billing_address.phone or '',
                                'mobile': billing_address.mobile or '',
                                'email': billing_address.email or ''
                            },
                            'order_note': cart.note or '',
                            'order_date': cart.date_order.strftime('%Y-%m-%d %H:%M:%S') if cart.date_order else '',
                        }
                    }
                
                else:
                    # Online Payment - create transaction
                    PaymentTransaction = request.env['payment.transaction'].sudo()
                    
                    try:
                        transaction_vals = {
                            'reference': cart.name,
                            'amount': cart.amount_total,
                            'currency_id': cart.currency_id.id,
                            'provider_id': provider.id,
                            'partner_id': request.env.user.partner_id.id,
                            'sale_order_ids': [(6, 0, [cart.id])],
                            'state': 'draft',
                        }
                        
                        # Add payment method if available
                        if payment_method:
                            transaction_vals['payment_method_id'] = payment_method.id
                        
                        transaction = PaymentTransaction.create(transaction_vals)
                        cart.action_confirm()
                        
                    except Exception as payment_error:
                        _logger.error(f"Error creating payment transaction: {str(payment_error)}")
                        request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                        return {'status': 'error', 'message': f'Error processing payment: {str(payment_error)}'}
                    
                    # Release the savepoint on success
                    request.env.cr.execute('RELEASE SAVEPOINT place_order_start')
                    
                    return {
                        'status': 'success',
                        'message': 'Payment initiated',
                        'data': {
                            'order_id': cart.id,
                            'order_name': cart.name,
                            'amount_total': cart.amount_total,
                            'amount_untaxed': cart.amount_untaxed,
                            'amount_tax': cart.amount_tax,
                            'currency': cart.currency_id.name,
                            'requires_payment': True,
                            'payment_method': provider.name,
                            # 'order_lines': [line[2] for line in order_lines_data],
                            'total_items_count': total_items_count,
                            'order_items': order_items,
                            'payment': {
                                'transaction_id': transaction.id,
                                'reference': transaction.reference,
                                'amount': transaction.amount,
                                'currency': cart.currency_id.name,
                                'provider_id': provider.id,
                                'provider_name': provider.name,
                                'payment_method_id': payment_method.id if payment_method else None,
                                'payment_method_name': payment_method.name if payment_method else provider.name,
                                'payment_url': '/shop/payment/',
                                'return_url': f'/shop/payment/validate?tx_id={transaction.id}',
                                'state': transaction.state
                            },
                            'delivery_address': {
                                'id': shipping_address.id,
                                'name': shipping_address.name,
                                'street': shipping_address.street,
                                'street2': shipping_address.street2 or '',
                                'city': shipping_address.city,
                                'state': shipping_address.state_id.name if shipping_address.state_id else '',
                                'zip': shipping_address.zip or '',
                                'country': shipping_address.country_id.name if shipping_address.country_id else '',
                                'phone': shipping_address.phone or '',
                                'mobile': shipping_address.mobile or '',
                                'email': shipping_address.email or ''
                            },
                            'billing_address': {
                                'id': billing_address.id,
                                'name': billing_address.name,
                                'street': billing_address.street,
                                'street2': billing_address.street2 or '',
                                'city': billing_address.city,
                                'state': billing_address.state_id.name if billing_address.state_id else '',
                                'zip': billing_address.zip or '',
                                'country': billing_address.country_id.name if billing_address.country_id else '',
                                'phone': billing_address.phone or '',
                                'mobile': billing_address.mobile or '',
                                'email': billing_address.email or ''
                            },
                            'order_note': cart.note or '',
                            'order_date': cart.date_order.strftime('%Y-%m-%d %H:%M:%S') if cart.date_order else '',
                        }
                    }
            
            except Exception as process_error:
                _logger.error(f"Error in payment processing: {str(process_error)}")
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
                return {'status': 'error', 'message': f'Error processing order: {str(process_error)}'}

        except Exception as e:
            _logger.error("Error placing order: %s", str(e))
            # Rollback transaction on any error
            try:
                request.env.cr.execute('ROLLBACK TO SAVEPOINT place_order_start')
            except:
                pass
            return {'status': 'error', 'message': f'Order placement failed: {str(e)}'}

    
    @http.route('/api/v1/payment/transaction/create', type='json', auth='user', methods=['POST'], csrf=False)
    def create_payment_transaction(self, **kwargs):
        try:
            data = json.loads(request.httprequest.data)
            
            # Validate required fields
            required_fields = ['payment_method_id', 'metal_id', 'amount']
            if not all(field in data for field in required_fields):
                return {
                    'status': 'error',
                    'message': 'Missing required fields'
                }
            
            # Create the payment transaction
            transaction = request.env['payment.transaction'].create({
                'payment_method_id': data['payment_method_id'],
                'metal_id': data['metal_id'],
                'amount': data['amount'],
                # 'grams': data['grams'],
                'state': 'pending'
            })
            
            # Generate a reference number
            transaction.reference = self._generate_reference(transaction)
            
            return {
                'status': 'success',
                'transaction_id': transaction.id,
                'reference': transaction.reference,
                'message': 'Payment transaction created successfully'
            }
            
        except Exception as e:
            _logger.error("Error creating payment transaction: %s", str(e))
            return {
                'status': 'error',
                'message': str(e)
            }
  
    @http.route('/api/v1/addresses', type='http', auth='user', csrf=False)
    def get_addresses(self):
        """Get user's shipping and billing addresses"""
        try:
            addresses = request.env['res.partner'].sudo().search([
                ('parent_id', '=', request.env.user.partner_id.id),
                ('type', 'in', ['delivery', 'invoice'])
            ])

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': addr.id,
                    'name': addr.name,
                    'type': addr.type,  # 'delivery' for shipping, 'invoice' for billing
                    'street': addr.street,
                    'street2': addr.street2,
                    'city': addr.city,
                    'state_id': addr.state_id.id if addr.state_id else None,
                    'state_name': addr.state_id.name if addr.state_id else None,
                    'zip': addr.zip,
                    'country_id': addr.country_id.id if addr.country_id else None,
                    'country_name': addr.country_id.name if addr.country_id else None,
                    'phone': addr.phone,
                    'mobile': addr.mobile,
                    'email': addr.email,
                    # 'is_default': addr.is_default_address
                } for addr in addresses]
            })
        except Exception as e:
            _logger.error("Error fetching addresses: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/addresses/create', type='json', auth='user', csrf=False, methods=['POST'])
    def create_address(self, **kwargs):
        """Create new shipping or billing address"""
        try:
            required_fields = ['name', 'street', 'city', 'type']
            for field in required_fields:
                if not kwargs.get(field):
                    return {
                        'status': 'error',
                        'message': f'Missing required field: {field}'
                    }

            if kwargs['type'] not in ['delivery', 'invoice']:
                return {
                    'status': 'error',
                    'message': 'Address type must be either delivery or invoice'
                }

            # Prepare address data
            address_data = {
                'parent_id': request.env.user.partner_id.id,
                'type': kwargs['type'],
                'name': kwargs['name'],
                'street': kwargs['street'],
                'street2': kwargs.get('street2'),
                'city': kwargs['city'],
                'state_id': kwargs.get('state_id') if kwargs.get('state_id') else 588,
                'zip': kwargs.get('zip'),
                'country_id': kwargs.get('country_id') if kwargs.get('country_id') else 104,
                'phone': kwargs.get('phone'),
                'mobile': kwargs.get('mobile') if kwargs.get('mobile') else kwargs.get('phone'),
                'email': kwargs.get('email') if kwargs.get('email') else request.env.user.email,
                # 'is_default_address': kwargs.get('is_default', False)
            }

            # Create new address
            new_address = request.env['res.partner'].sudo().create(address_data)

            # If set as default, unset other default addresses of same type
            # if address_data['is_default_address']:
            #     other_addresses = request.env['res.partner'].sudo().search([
            #         ('parent_id', '=', request.env.user.partner_id.id),
            #         ('type', '=', kwargs['type']),
            #         ('id', '!=', new_address.id),
            #         ('is_default_address', '=', True)
            #     ])
            #     other_addresses.write({'is_default_address': False})

            return {
                'status': 'success',
                'message': 'Address created successfully',
                'data': {
                    'id': new_address.id,
                    'name': new_address.name,
                    'type': new_address.type
                }
            }
        except Exception as e:
            _logger.error("Error creating address: %s", str(e))
            return {'status': 'error', 'message': str(e)}

    @http.route('/api/v1/addresses/<int:address_id>', type='json', auth='user', csrf=False, methods=['PUT'])
    def update_address(self, address_id, **kwargs):
        """Update existing address"""
        try:
            address = request.env['res.partner'].sudo().browse(int(address_id))
            
            # Verify address belongs to user
            if not address.exists() or address.parent_id.id != request.env.user.partner_id.id:
                return {'status': 'error', 'message': 'Address not found or access denied'}

            # Prevent changing address type
            kwargs.pop('type', None)
            
            # Update address
            address.write(kwargs)

            # Handle default address setting
            # if kwargs.get('is_default_address'):
            #     other_addresses = request.env['res.partner'].sudo().search([
            #         ('parent_id', '=', request.env.user.partner_id.id),
            #         ('type', '=', address.type),
            #         ('id', '!=', address.id),
            #         ('is_default_address', '=', True)
            #     ])
            #     other_addresses.write({'is_default_address': False})

            return {
                'status': 'success',
                'message': 'Address updated successfully',
                'data': {
                    'id': address.id,
                    'name': address.name,
                    'type': address.type
                }
            }
        except Exception as e:
            _logger.error("Error updating address: %s", str(e))
            return {'status': 'error', 'message': str(e)}

    @http.route('/api/v1/addresses/<int:address_id>', type='http', auth='user', csrf=False, methods=['DELETE'])
    def delete_address(self, address_id):
        """Delete address"""
        try:
            address = request.env['res.partner'].sudo().browse(int(address_id))
            
            # Verify address belongs to user
            if not address.exists() or address.parent_id.id != request.env.user.partner_id.id:
                return json.dumps({
                    'status': 'error',
                    'message': 'Address not found or access denied'
                })

            # Prevent deletion if it's the only address of its type
            same_type_addresses = request.env['res.partner'].sudo().search_count([
                ('parent_id', '=', request.env.user.partner_id.id),
                ('type', '=', address.type)
            ])
            if same_type_addresses <= 1:
                return json.dumps({
                    'status': 'error',
                    'message': f'Cannot delete the only {address.type} address'
                })

            address.unlink()
            return json.dumps({
                'status': 'success',
                'message': 'Address deleted successfully'
            })
        except Exception as e:
            _logger.error("Error deleting address: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    # @http.route('/api/v1/addresses/set_default/<int:address_id>', type='http', auth='user', csrf=False, methods=['POST'])
    # def set_default_address(self, address_id):
    #     """Set address as default for its type"""
    #     try:
    #         address = request.env['res.partner'].sudo().browse(int(address_id))
            
    #         # Verify address belongs to user
    #         if not address.exists() or address.parent_id.id != request.env.user.partner_id.id:
    #             return json.dumps({
    #                 'status': 'error',
    #                 'message': 'Address not found or access denied'
    #             })

    #         # Unset other default addresses of same type
    #         other_addresses = request.env['res.partner'].sudo().search([
    #             ('parent_id', '=', request.env.user.partner_id.id),
    #             ('type', '=', address.type),
    #             ('id', '!=', address.id),
    #             ('is_default_address', '=', True)
    #         ])
    #         other_addresses.write({'is_default_address': False})

    #         # Set this address as default
    #         address.is_default_address = True

    #         return json.dumps({
    #             'status': 'success',
    #             'message': f'Address set as default {address.type} address'
    #         })
    #     except Exception as e:
    #         _logger.error("Error setting default address: %s", str(e))
    #         return json.dumps({
    #             'status': 'error',
    #             'message': str(e)
    #         })

    
    # @http.route('/api/forgot-password', type='json', auth='public', methods=['POST'], csrf=False)
    # def forgot_password(self, **kwargs):
    #     email = kwargs.get('email')
        
    #     if not email:
    #         return {'status': 'error', 'message': 'Email is required'}

    #     # Search for user (res.partner or res.users)
    #     user = request.env['res.users'].sudo().search([('login', '=', email)], limit=1)
        
    #     if not user:
    #         return {'status': 'error', 'message': 'User not found'}

    #     # Trigger Odoo password reset email (send a reset link)
    #     user.sudo().action_reset_password()  # Built-in Odoo method

    #     return {
    #         'status': 'success',
    #         'message': 'Password reset link has been sent to your email'
    #     }

    
    # @http.route('/api/forgot-password', type='json', auth='public', methods=['POST'], csrf=False)
    # def forgot_password(self, **post):
    #     email = post.get('email')

    #     if not email:
    #         return {'status': 'error', 'message': 'Email is required'}

    #     user = request.env['res.users'].sudo().search([('login', '=', email)], limit=1)

    #     if not user:
    #         return {'status': 'error', 'message': 'User not found'}

    #     user.sudo().action_reset_password()

    #     return {
    #         'status': 'success',
    #         'message': f'Password reset link sent to {email}'
    #     }
    

    # @http.route('/api/forgot-password', type='json', auth='public', methods=['POST'], csrf=False)
    # def api_forgot_password(self, **post):
    #     try:
    #         email = post.get('email')
            
    #         if not email:
    #             return {'error': 'Email is required', 'status': 'error'}
            
    #         # Same logic as web endpoint
    #         _logger.info(
    #             "API Password reset attempt for <%s> from %s",
    #             email, request.httprequest.remote_addr)
            
    #         # Use the same reset method as web
    #         request.env['res.users'].sudo().reset_password(email)
            
    #         return {
    #             'status': 'success',
    #             'message': _("Password reset instructions sent to your email")
    #         }
            
    #     except UserError as e:
    #         _logger.warning(f"Password reset error for {email}: {e}")
    #         return {'error': e.args[0], 'status': 'error'}
    #     except Exception as e:
    #         _logger.error(f"Unexpected error in password reset: {str(e)}")
    #         return {'error': _("Could not reset your password"), 'status': 'error'} 


    
    # @http.route('/web/signup', type='http', auth="public", methods=['POST'], csrf=False, website=True)
    # def signup(self, **post):
    #     try:
    #         # Extract data from the request
    #         values = {
    #             'name': post.get('name', '').strip(),
    #             'login': post.get('email', '').strip(),
    #             'mobile': post.get('contactNumber', '').strip(),
    #             'password': post.get('password', '').strip(),
    #             'confirm_password': post.get('confirmPassword', '').strip(),
    #         }

    #         # Validate required fields
    #         if not all([values['name'], values['login'], values['password'], values['confirm_password']]):
    #             return Response(
    #                 json.dumps({'error': 'Missing required fields'}),
    #                 status=400,
    #                 mimetype='application/json'
    #             )

    #         # Validate password match
    #         if values['password'] != values['confirm_password']:
    #             return Response(
    #                 json.dumps({'error': 'Passwords do not match'}),
    #                 status=400,
    #                 mimetype='application/json'
    #             )

    #         # Check if user already exists
    #         if request.env['res.users'].sudo().search_count([('login', '=', values['login'])]):
    #             return Response(
    #                 json.dumps({'error': 'Email already registered'}),
    #                 status=400,
    #                 mimetype='application/json'
    #             )

    #         # Create the user
    #         user = request.env['res.users'].sudo().with_context(create_user=True).create({
    #             'name': values['name'],
    #             'login': values['login'],
    #             'password': values['password'],
    #             'mobile': values['mobile'],
    #         })

    #         # Return success response
    #         return Response(
    #             json.dumps({
    #                 'success': True,
    #                 'message': 'User created successfully',
    #                 'user_id': user.id
    #             }),
    #             status=200,
    #             mimetype='application/json'
    #         )

    #     except Exception as e:
    #         _logger.error("Signup error: %s", str(e))
    #         return Response(
    #             json.dumps({'error': 'Internal server error'}),
    #             status=500,
    #             mimetype='application/json'
    #         )

    # @http.route('/web/api/signup', type='json', auth='public', website=True, methods=['POST'], csrf=False)
    # def api_signup(self, **kwargs):
    #     try:
    #         # Extract data from JSON request
    #         data = request.jsonrequest
    #         params = data.get("params", {})
            
    #         login = params.get("login")
    #         password = params.get("password")
    #         phone = params.get("phone")
    #         name = params.get("name")

    #         # Validate required fields
    #         if not all([login, password, phone, name]):
    #             return {
    #                 "success": False,
    #                 "message": "All fields (login, password, phone, name) are required."
    #             }

    #         # Check if user exists
    #         if request.env["res.users"].sudo().search([("login", "=", login)]):
    #             return {
    #                 "success": False,
    #                 "message": "Email already exists."
    #             }

    #         # Create user
    #         user = request.env["res.users"].sudo().create({
    #             "name": name,
    #             "login": login,
    #             "password": password,
    #             "phone": phone,
    #         })

    #         return {
    #             "success": True,
    #             "message": "User created successfully.",
    #             "user_id": user.id
    #         }

    #     except Exception as e:
    #         _logger.error("Error in signup API: %s", str(e))
    #         return {
    #             "success": False,
    #             "message": f"Error: {str(e)}"
    #         }


    # @http.route('/app/otp/signup', type='json', auth='public', website=True, csrf=False)
    # def app_otp_signup(self, **kw):
    #     """
    #     Step 1: Initial signup request
    #     - Validates if email already exists
    #     - Generates and sends OTP
    #     - Returns success or error
    #     """
    #     qcontext = request.params.copy()
    #     name = str(qcontext.get('name', ''))
    #     email = str(qcontext.get('email', ''))
    #     phone = str(qcontext.get('phone', ''))
    #     password = str(qcontext.get('password', ''))
        
    #     # Validate required fields
    #     if not all([name, email, password]):
    #         return {
    #             'success': False,
    #             'message': 'Name, email, and password are required fields'
    #         }
        
    #     # Check if user already exists
    #     existing_user = request.env['res.users'].sudo().search([('login', '=', email)], limit=1)
    #     if existing_user:
    #         return {
    #             'success': False,
    #             'message': 'A user with this email already exists'
    #         }
        
    #     # Find or create OTP verification record
    #     otp_record = request.env['otp.verification'].sudo().search([('email', '=', email)], order="create_date desc", limit=1)
    #     if not otp_record:
    #         otp_record = request.env['otp.verification'].sudo().create({
    #             'email': email,
    #             'name': name,
    #             'phone': phone,
    #             'temp_password': password  # Store password temporarily until verification
    #         })
    #     else:
    #         otp_record.write({
    #             'name': name,
    #             'phone': phone,
    #             'temp_password': password
    #         })
        
    #     # Generate OTP
    #     OTP = self.generate_otp(4)
    #     otp_record.write({'otp': OTP})
        
    #     # Default company for sending emails
    #     default_company = request.env['res.company'].sudo().search([], limit=1)
        
    #     # Send OTP via email
    #     mail_body = """\
    #                     <html>
    #                         <body>
    #                             <p>
    #                                 Dear <b>%s</b>,
    #                                     <br>
    #                                     <p> 
    #                                         To complete your account signup, 
    #                                         <br>Please use the following One-Time Password (OTP): <b>%s</b>
    #                                     </p>
    #                                 Thanks & Regards.
    #                             </p>
    #                         </body>
    #                     </html>
    #                 """ % (name, OTP)
    #     mail = request.env['mail.mail'].sudo().create({
    #         'subject': _('Verify Your Account - OTP Required'),
    #         'email_from': default_company.email,
    #         'email_to': email,
    #         'body_html': mail_body,
    #     })
    #     mail.send()
        
    #     # Initialize delivery methods list
    #     delivery_methods = ['email']
        
    #     # Send OTP via WhatsApp and SMS if phone is available
    #     if phone:
    #         try:
    #             # Get WhatsApp template from configuration
    #             whatsapp_template = request.env['whatsapp.template'].sudo().search([
    #                 ('name', '=', 'otp_verification')
    #             ], limit=1)
                
    #             if whatsapp_template:
    #                 # Prepare parameters for WhatsApp template
    #                 params = {
    #                     'user_name': name,
    #                     'otp': OTP
    #                 }
                    
    #                 # Send WhatsApp message using the template
    #                 whatsapp_result = request.env['whatsapp.message'].sudo().send_whatsapp_message(
    #                     to_number=phone,
    #                     template_id=whatsapp_template.id,
    #                     params=params
    #                 )
                    
    #                 if whatsapp_result:
    #                     delivery_methods.append('whatsapp')
                    
    #             # Also send SMS as fallback or additional verification
    #             sms_template = _("""Dear %s, Your OTP for account signup is: %s. This code will expire in 10 minutes.""") % (name, OTP)
                
    #             # Send SMS using SMS module
    #             sms_result = request.env['sms.api'].sudo().send_sms(
    #                 phone=phone,
    #                 message=sms_template
    #             )
                
    #             if sms_result:
    #                 delivery_methods.append('sms')
                    
    #         except Exception as e:
    #             # Log the error but continue with email OTP
    #             _logger.error("Failed to send OTP via WhatsApp/SMS: %s", str(e))
        
    #     return {
    #         'success': True,
    #         'email': email,
    #         'delivery_methods': delivery_methods,
    #         'message': 'OTP has been sent for verification'
    #     }
    
    
    
    # @http.route('/api/signup', type='http', auth='public', methods=['POST'], csrf=False)
    # def app_otp_signup(self):
    #     """
    #     Simple signup endpoint with direct HTTP access
    #     """
    #     headers = {'Content-Type': 'application/json'}
        
    #     try:
    #         # Parse JSON data from request
    #         data = json.loads(request.httprequest.data.decode('utf-8'))
    #         params = data.get('params', {})
            
    #         # Extract fields
    #         name = params.get('name', '')
    #         email = params.get('email', '')
    #         phone = params.get('phone', '')
    #         password = params.get('password', '')
            
    #         # Log received data for debugging
    #         _logger.info("Received signup request with data: %s", params)
            
    #         # Validate required fields
    #         if not all([name, email, password]):
    #             response = {
    #                 'success': False,
    #                 'message': 'Name, email, and password are required fields'
    #             }
    #             return Response(json.dumps(response), headers=headers, status=400)
            
    #         # Check if user already exists
    #         existing_user = request.env['res.users'].sudo().search([('login', '=', email)], limit=1)
    #         if existing_user:
    #             response = {
    #                 'success': False,
    #                 'message': 'A user with this email already exists'
    #             }
    #             return Response(json.dumps(response), headers=headers, status=409)
            
    #         # Generate OTP
    #         OTP = self.generate_otp(4)
            
    #         # Store OTP and user data (simplified for testing)
    #         vals = {
    #             'name': name,
    #             'email': email,
    #             'phone': phone,
    #             'otp': OTP,
    #         }
            
    #         # Create OTP record if the model exists, otherwise just return OTP for testing
    #         try:
    #             otp_record = request.env['otp.verification'].sudo().create(vals)
    #             _logger.info("Created OTP record: %s", otp_record.id)
    #         except Exception as e:
    #             _logger.error("Could not create OTP record: %s", str(e))
    #             # Continue anyway for testing
            
    #         # For testing, return OTP directly
    #         response = {
    #             'success': True,
    #             'email': email,
    #             'otp': OTP,  # In production, you would remove this
    #             'message': 'OTP has been generated for testing'
    #         }
    #         return Response(json.dumps(response), headers=headers, status=200)
            
    #     except json.JSONDecodeError as e:
    #         _logger.error("JSON decode error: %s", str(e))
    #         response = {
    #             'success': False,
    #             'message': 'Invalid JSON format in request body'
    #         }
    #         return Response(json.dumps(response), headers=headers, status=400)
            
    #     except Exception as e:
    #         _logger.error("Error in signup API: %s", str(e))
    #         response = {
    #             'success': False,
    #             'message': f'Server error: {str(e)}'
    #         }
    #         return Response(json.dumps(response), headers=headers, status=500)


    # @http.route('/api/forgot-password', type='json', auth='public', methods=['POST'], csrf=False)
    # def api_forgot_password(self, **kwargs):
    #     try:
    #         # Get raw JSON data
    #         data = json.loads(request.httprequest.data)
    #         email = data.get('login')
            
    #         if not email:
    #             return {"jsonrpc": "2.0", "id": None, "result": {
    #                 "error": "Email is required", 
    #                 "status": "error"
    #             }}
            
    #         _logger.info(f"Password reset requested for: {email}")
            
    #         # Use Odoo's standard password reset
    #         request.env['res.users'].sudo().reset_password(email)
            
    #         return {
    #             "jsonrpc": "2.0",
    #             "id": None,
    #             "result": {
    #                 "status": "success",
    #                 "message": "Password reset instructions sent to your email"
    #             }
    #         }
            
    #     except Exception as e:
    #         _logger.error(f"Password reset error: {str(e)}")
    #         return {
    #             "jsonrpc": "2.0",
    #             "id": None,
    #             "result": {
    #                 "error": str(e),
    #                 "status": "error"
    #             }
    #         }

    @http.route('/api/forgot-password', type='json', auth='public', methods=['POST'], csrf=False)
    def api_forgot_password(self, **kwargs):
        try:
            # Get raw JSON data
            data = json.loads(request.httprequest.data)
            
            # Try to get login from root or from params
            email = data.get('login') or (data.get('params') and data.get('params').get('login'))
            
            if not email:
                return {"jsonrpc": "2.0", "id": None, "result": {
                    "error": "Email is required", 
                    "status": "error"
                }}
            
            _logger.info(f"Password reset requested for: {email}")
            
            # Use Odoo's standard password reset
            request.env['res.users'].sudo().reset_password(email)
            
            return {
                "jsonrpc": "2.0",
                "id": None,
                "result": {
                    "status": "success",
                    "message": "Password reset instructions sent to your email"
                }
            }
            
        except Exception as e:
            _logger.error(f"Password reset error: {str(e)}")
            return {
                "jsonrpc": "2.0",
                "id": None,
                "result": {
                    "error": str(e),
                    "status": "error"
                }
            }
    
    @http.route('/metal/api/test', type='json', auth='none')
    def test(self):
        return {"status": "working"}

    
    # @http.route('/api/v1/create-razorpay-order', type='json', auth='public', methods=['POST'], csrf=False)   
    # def create_razorpay_order(self, **post):
    #     try:
    #         data = json.loads(request.httprequest.data)
    #         amount = int(data.get('amount'))  # Amount in paise
            
    #         client = razorpay.Client(auth=("rzp_test_YOUR_API_KEY", "YOUR_API_SECRET"))
            
    #         order = client.order.create({
    #             'amount': amount,
    #             'currency': 'INR',
    #             'receipt': data.get('receipt', 'order_rcpt'),
    #             'payment_capture': 1
    #         })
            
    #         return {
    #             'status': 'success',
    #             'id': order['id'],
    #             'amount': order['amount'],
    #             'currency': order['currency']
    #         }
            
    #     except Exception as e:
    #         _logger.error("Razorpay order creation failed: %s", str(e))
    #         return {
    #             'status': 'error',
    #             'message': str(e)
    #         }
   
    # @http.route('/api/v1/confirm-razorpay-payment', type='json', auth='public', methods=['POST'], csrf=False)
    # def confirm_razorpay_payment(self, **post):
    #     try:
    #         data = json.loads(request.httprequest.data)
            
    #         # Verify payment signature
    #         client = razorpay.Client(auth=("rzp_test_YOUR_API_KEY", "YOUR_API_SECRET"))
    #         params = {
    #             'razorpay_order_id': data['razorpay_order_id'],
    #             'razorpay_payment_id': data['razorpay_payment_id'],
    #             'razorpay_signature': data['razorpay_signature']
    #         }
            
    #         if not client.utility.verify_payment_signature(params):
    #             raise ValueError("Invalid payment signature")
            
    #         # Create sale order in Odoo
    #         order = request.env['sale.order'].sudo().create({
    #             'partner_id': request.env.user.partner_id.id,
    #             'amount_total': data['amount'],
    #             # Add other order fields as needed
    #         })
            
    #         # Add order lines from cart_items
    #         for item in data.get('cart_items', []):
    #             request.env['sale.order.line'].sudo().create({
    #                 'order_id': order.id,
    #                 'product_id': item['product_id'],
    #                 'product_uom_qty': item['quantity'],
    #                 'price_unit': item['price'],
    #             })
            
    #         # Confirm the order
    #         order.action_confirm()
            
    #         return {
    #             'status': 'success',
    #             'order_id': order.id,
    #             'message': 'Order created successfully'
    #         }
            
    #     except Exception as e:
    #         _logger.error("Payment confirmation failed: %s", str(e))
    #         return {
    #             'status': 'error',
    #             'message': str(e)
    #         }
        


    @http.route('/api/v1/countries', type='http', auth='user', csrf=False)
    def get_countries(self):
        """Get list of countries"""
        try:
            Country = request.env['res.country'].sudo()
            countries = Country.search([])

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': country.id,
                    'name': country.name,
                    'code': country.code,
                    'phone_code': country.phone_code,
                    'currency_id': country.currency_id.id if country.currency_id else None,
                    'currency_name': country.currency_id.name if country.currency_id else None,
                } for country in countries]
            })
        except Exception as e:
            _logger.error("Error fetching countries: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/states/<int:country_id>', type='http', auth='user', csrf=False)
    def get_states(self, country_id):
        """Get states for a specific country"""
        try:
            State = request.env['res.country.state'].sudo()
            states = State.search([('country_id', '=', country_id)])

            if not states:
                return json.dumps({
                    'status': 'success',
                    'data': []
                })

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': state.id,
                    'name': state.name,
                    'code': state.code,
                    'country_id': state.country_id.id
                } for state in states]
            })
        except Exception as e:
            _logger.error("Error fetching states: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    # @http.route('/api/v1/location_data', type='http', auth='user', csrf=False)
    # def get_location_data(self):
    #     """Get combined countries and states data"""
    #     try:
    #         Country = request.env['res.country'].sudo()
    #         State = request.env['res.country.state'].sudo()

    #         # Get all countries
    #         countries = Country.search([])
            
    #         # Prepare response data
    #         location_data = {}
    #         for country in countries:
    #             states = State.search([('country_id', '=', country.id)])
    #             location_data[country.id] = {
    #                 'country': {
    #                     'id': country.id,
    #                     'name': country.name,
    #                     'code': country.code,
    #                     'phone_code': country.phone_code,
    #                     'currency_id': country.currency_id.id if country.currency_id else None,
    #                     'currency_name': country.currency_id.name if country.currency_id else None,
    #                 },
    #                 'states': [{
    #                     'id': state.id,
    #                     'name': state.name,
    #                     'code': state.code
    #                 } for state in states]
    #             }

    #         return json.dumps({
    #             'status': 'success',
    #             'data': location_data
    #         })
    #     except Exception as e:
    #         _logger.error("Error fetching location data: %s", str(e))
    #         return json.dumps({
    #             'status': 'error',
    #             'message': str(e)}

    @http.route('/api/v1/my_account', type='http', auth='user', csrf=False)
    def get_my_account(self):
        """Get user account details including profile, wallet and vault information"""
        try:
            user = request.env.user
            partner = user.partner_id

            # Get user's vault account
            vault_account = request.env['user.vault.account'].sudo().search([
                ('user_id', '=', user.id)
            ], limit=1)

            # Get metal balances
            metal_balances = []
            if vault_account:
                for balance in vault_account.metal_balances_ids:
                    metal_balances.append({
                        'metal_id': balance.metal_id.id,
                        'metal_name': balance.metal_id.name,
                        'balance': balance.balance,
                        'current_price': balance.metal_current_price,
                        'wallet_amount': balance.wallet_amount
                    })

            # Prepare base URL for image
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            
            # Build response data
            account_data = {
                'user': {
                    'id': user.id,
                    'name': user.name,
                    'email': user.email,
                    'phone': partner.phone or partner.mobile,
                    'image_url': f"{base_url}/web/image/res.partner/{partner.id}/image_1920" if partner.image_1920 else None,
                },
                'billing_address': {
                    'name': partner.name,
                    'street': partner.street,
                    'street2':partner.street2,
                    'city': partner.city,
                    'state': partner.state_id.name if partner.state_id else None,
                    # 'zip': invoice.partner_id.zip,
                    'country': partner.country_id.name if partner.country_id else None,
                    # 'phone': invoice.partner_id.phone,
                    # 'email': invoice.partner_id.email
                },
                'vault': {
                    'total_invested': vault_account.total_invested_amount if vault_account else 0.0,
                    'total_wallet': vault_account.total_wallet_amount if vault_account else 0.0,
                    'metal_balances': metal_balances
                },
                'addresses_count': request.env['res.partner'].sudo().search_count([
                    ('parent_id', '=', partner.id),
                    ('type', 'in', ['delivery', 'invoice'])
                ]),
                'orders_count': request.env['sale.order'].sudo().search_count([
                    ('partner_id', '=', partner.id),
                    ('state', 'not in', ['draft', 'cancel'])
                ]),
                'kyc_verified': bool(partner.vat),  # Assuming VAT field is used for KYC verification
                'kyc_document_no': partner.vat if partner.vat else None
            }

            return json.dumps({
                'status': 'success',
                'data': account_data
            })
        except Exception as e:
            _logger.error("Error fetching account details: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })


############changes done by manali api of change password 
    # @http.route('/api/change_password', type='json', auth='user', methods=['POST'], website=True)
    # def change_password(self, **post):
    #     try:
    #         # Get the current user
    #         user = request.env.user
            

    #         _logger.info("=== Password Change Debug ===")
    #         _logger.info("Authenticated User: %s (ID: %s)", user.login, user.id)
            
    #         data = json.loads(request.httprequest.data)
    #         _logger.info("Raw Request Data: %s", data)
            
    #         password = data.get('password')
    #         _logger.info("Password received: %s", password)
            
            
    #         # Extract data from the request
    #         # data = json.loads(request.httprequest.data)
    #         # password = data.get('password')
    #         new_password = data.get('new_password')
    #         verify_password = data.get('verify_password')
            
    #         # Validate inputs
    #         if not all([password, new_password, verify_password]):
    #             return {
    #                 'success': False,
    #                 'error': 'All fields are required (password, new_password, verify_password)'
    #             }
                
    #         if new_password != verify_password:
    #             return {
    #                 'success': False,
    #                 'error': 'New password and verification password do not match'
    #             }
                
    #         # Verify current password
    #         if not user._check_credentials(password, {'interactive': True}):
    #             return {
    #                 'success': False,
    #                 'error': 'Current password is incorrect'
    #             }
                
    #         # Change the password
    #         user.change_password(password, new_password)
            
    #         return {
    #             'success': True,
    #             'message': 'Password changed successfully'
    #         }
            
    #     except Exception as e:
    #         _logger.error("Password change error: %s", str(e))
    #         return {
    #             'success': False,
    #             'error': str(e)
    #         }        

    @http.route('/api/change_password', type='json', auth='user', methods=['POST'], website=True)
    def change_password(self, **post):
        try:
            user = request.env.user
            _logger.info("=== Password Change Debug ===")
            _logger.info("Authenticated User: %s (ID: %s)", user.login, user.id)
            
            data = json.loads(request.httprequest.data)
            _logger.info("Raw Request Data: %s", data)
            
            # Alternative password verification method
            # from passlib.context import CryptContext
            # crypt_context = CryptContext(schemes=['pbkdf2_sha512'])
            
            # password = data.get('password')
            new_password = data.get('new_password')
            verify_password = data.get('verify_password')
            
            # Validate inputs
            # if not all([password, new_password, verify_password]):
            if not all([ new_password, verify_password]):
                return {
                    'success': False,
                    'error': 'All fields are required'
                }
                
            if new_password != verify_password:
                return {
                    'success': False,
                    'error': 'New passwords do not match'
                }
            
            # Direct password verification
            # if not crypt_context.verify(password, user.password_crypt):
            #     _logger.error("Password verification failed for user %s", user.login)
            #     return {
            #         'success': False,
            #         'error': 'Current password is incorrect'
            #     }
            
            # Change password directly
            user.password = new_password
            
            return {
                'success': True,
                'message': 'Password changed successfully'
            }
        
        except Exception as e:
            _logger.error("Password change error: %s", str(e))
            return {
                'success': False,
                'error': str(e)
            }

    @http.route('/api/v1/invoices', type='http', auth='user', csrf=False)
    def get_invoices(self):
        """Get list of user's invoices"""
        try:
            # Get all invoices linked to the user's partner
            invoices = request.env['account.move'].sudo().search([
                ('partner_id', '=', request.env.user.partner_id.id),
                ('state', 'not in', ['draft', 'cancel']),
                ('move_type', '=', 'out_invoice')
            ], order='invoice_date desc')

            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': inv.id,
                    'name': inv.name,
                    'invoice_date': inv.invoice_date.strftime('%Y-%m-%d') if inv.invoice_date else None,
                    'invoice_due_date': inv.invoice_date_due.strftime('%Y-%m-%d') if inv.invoice_date_due else None,                    'amount_residual': inv.amount_residual, #added by manali
                    'amount_total': inv.amount_total,
                    'amount_tax': inv.amount_tax,
                    'amount_untaxed': inv.amount_untaxed,
                    'currency': inv.currency_id.name,
                    'state': inv.state,
                    'payment_state': inv.payment_state,
                    'invoice_payment_term': inv.invoice_payment_term_id.name if inv.invoice_payment_term_id else None,
                    # 'download_url': f"{base_url}/api/v1/invoices/{inv.id}/download"
                } for inv in invoices]
            })
        except Exception as e:
            _logger.error("Error fetching invoices: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/invoices/<int:invoice_id>', type='http', auth='user', csrf=False)
    def get_invoice_details(self, invoice_id):
        """Get detailed information for a specific invoice"""
        try:
            invoice = request.env['account.move'].sudo().search([
                ('id', '=', invoice_id),
                ('partner_id', '=', request.env.user.partner_id.id),
                # ('move_type', '=', 'out_invoice')
            ], limit=1)

            if not invoice:
                return json.dumps({
                    'status': 'error',
                    'message': 'Invoice not found or access denied'
                })

            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')

            # Get related order if exists
            order = invoice.invoice_line_ids.mapped('sale_line_ids.order_id')
            order_data = None
            if order:
                order_data = {
                    'id': order.id,
                    'name': order.name,
                    'date_order': order.date_order.strftime('%Y-%m-%d %H:%M:%S') if order.date_order else None,
                    'state': order.state
                }

            productData =  [{
                    'product_name': line.product_id.name,
                    'quantity': line.quantity,
                    'unit_price': line.price_unit,
                    'subtotal': line.price_subtotal,
                    'tax_amount': line.price_total - line.price_subtotal,
                    'total': line.price_total
                } for line in invoice.invoice_line_ids]

            
            # Calculate products summary data
            products_summary = {
                'total_count': len(productData),
                'total_subtotal': sum(line['subtotal'] for line in productData),
                'total_amount': sum(line['total'] for line in productData)
            }



            invoice_data = {
                'id': invoice.id,
                'name': invoice.name,
                'invoice_date': invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else None,
                'due_date': invoice.invoice_date_due.strftime('%Y-%m-%d') if invoice.invoice_date_due else None,
                'amount_total': invoice.amount_total,
                'amount_tax': invoice.amount_tax,
                'amount_untaxed': invoice.amount_untaxed,
                'currency': invoice.currency_id.name,
                'state': invoice.state,
                'payment_state': invoice.payment_state,
                'invoice_payment_term': invoice.invoice_payment_term_id.name if invoice.invoice_payment_term_id else None,
                'download_url': f"{base_url}/api/v1/invoices/{invoice.id}/download",
                'related_order': order_data,
                'billing_address': {
                    'name': invoice.partner_id.name,
                    'street': invoice.partner_id.street,
                    'street2': invoice.partner_id.street2,
                    'city': invoice.partner_id.city,
                    'state': invoice.partner_id.state_id.name if invoice.partner_id.state_id else None,
                    'zip': invoice.partner_id.zip,
                    'country': invoice.partner_id.country_id.name if invoice.partner_id.country_id else None,
                    'phone': invoice.partner_id.phone,
                    'email': invoice.partner_id.email
                },
                'lines': productData,
                'product_summary':products_summary
            }

            return json.dumps({
                'status': 'success',
                'data': invoice_data
            })
        except Exception as e:
            _logger.error("Error fetching invoice details: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/invoices/<int:invoice_id>/download', type='http', auth='user')
    def download_invoice(self, invoice_id):
        """Download invoice PDF"""
        try:
            invoice = request.env['account.move'].sudo().search([
                ('id', '=', invoice_id),
                ('partner_id', '=', request.env.user.partner_id.id),
                # ('move_type', '=', 'out_invoice')
            ], limit=1)

            if not invoice:
                return request.not_found()

            # Generate PDF report
            # pdf = request.env.ref('account.account_invoices').sudo()._render_qweb_pdf([invoice.id])[0]
            pdf = request.env.ref('account.move').sudo()._render_qweb_pdf([invoice.id])[0]

            # Prepare response with PDF
            pdfhttpheaders = [
                ('Content-Type', 'application/pdf'),
                ('Content-Length', len(pdf)),
                ('Content-Disposition', f'attachment; filename=Invoice_{invoice.name}.pdf;')
            ]
            return request.make_response(pdf, headers=pdfhttpheaders)
        
        except Exception as e:
            _logger.error("Error downloading invoice: %s", str(e))
            return request.not_found()

    @http.route('/api/v1/user/profile', type='http', auth='user', csrf=False)
    def get_user_profile(self):
        """Get user profile information"""
        try:
            user = request.env.user
            partner = user.partner_id
            
            # Get base URL for image
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            
            return json.dumps({
                'status': 'success',
                'data': {
                    'id': user.id,
                    'name': user.name,
                    'email': user.email,
                    'login': user.login,
                    'phone': partner.phone,
                    'company_name': partner.company_name,
                    'l10n_in_pan':partner.l10n_in_pan,
                    'mobile': partner.mobile,
                    'street': partner.street,
                    'street2': partner.street2,
                    'city': partner.city,
                    'state_id': partner.state_id.id if partner.state_id else None,
                    'state_name': partner.state_id.name if partner.state_id else None,
                    'zip': partner.zip,
                    'country_id': partner.country_id.id if partner.country_id else None,
                    'country_name': partner.country_id.name if partner.country_id else None,
                    'image_url': f"{base_url}/web/image/res.partner/{partner.id}/image_1920" if partner.image_1920 else None,
                    'vat': partner.vat,  # Tax ID / PAN number
                    'lang': user.lang,
                    'tz': user.tz,
                }
            })
        except Exception as e:
            _logger.error("Error fetching user profile: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/user/profile/update', type='json', auth='user', csrf=False, methods=['POST'])
    def update_user_profile(self, **kwargs):
        """Update user profile information"""
        try:
            user = request.env.user
            partner = user.partner_id
            
            # Fields that can be updated for res.partner
            partner_fields = [
                'name', 'phone', 'mobile', 'street', 'street2', 
                'city', 'state_id', 'zip', 'country_id', 'email', 
                'l10n_in_pan',#added by manali 
                # 'state_name','country_name', 
                'vat'  # Tax ID / PAN number
            ]
            
            # Fields that can be updated for res.users
            user_fields = ['lang', 'tz']
            
            # Update partner fields
            partner_vals = {
                field: kwargs.get(field)
                for field in partner_fields
                if field in kwargs and kwargs.get(field) is not None
            }

            # Debug log to see what we're about to update
            _logger.info("Updating partner fields: %s", partner_vals)

            # Update partner fields
            # if partner_vals:
            #     partner.write(partner_vals)

            
            # Update user fields
            user_vals = {
                field: kwargs.get(field)
                for field in user_fields
                if field in kwargs and kwargs.get(field) is not None
            }
            
            # Validate email format if provided
            if 'email' in partner_vals:
                if not re.match(r"[^@]+@[^@]+\.[^@]+", partner_vals['email']):
                    return {
                        'status': 'error',
                        'message': 'Invalid email format'
                    }
            
            # Validate phone numbers if provided
            for phone_field in ['phone', 'mobile']:
                if phone_field in partner_vals:
                    if not re.match(r'^\+?[0-9]{10,12}$', partner_vals[phone_field]):
                        return {
                            'status': 'error',
                            'message': f'Invalid {phone_field} number format'
                        }
            
            if partner_vals:
                partner.write(partner_vals)
            if user_vals:
                user.write(user_vals)
            
            return {
                'status': 'success',
                'message': 'Profile updated successfully',
                'data': {
                    'id': user.id,
                    'name': partner.name,
                    'email': partner.email,
                    'phone': partner.phone,
                    'mobile': partner.mobile,
                    'l10n_in_pan':partner.l10n_in_pan, #aded by manali
                    'company_name':partner.company_name,  #aded by manali
                    'state_id': partner.state_id.id if partner.state_id else None, #aded by manali
                    'state_name': partner.state_id.name if partner.state_id else None, #aded by manali
                    'country_id': partner.country_id.id if partner.country_id else None, #aded by manali
                    'country_name': partner.country_id.name if partner.country_id else None, #aded by manali
                    'street':partner.street,
                    'city':partner.city,
                    'zip':partner.zip,
                    # 'country':partner.country
                }
            }
        # ///added in code  :  1600 to 1607 : added fields for changing in user information 
        except Exception as e:
            _logger.error("Error updating user profile: %s", str(e))
            return {
                'status': 'error',
                'message': str(e)
            }

class PartnerNomineeContactController(http.Controller):

    @http.route('/create_nominee_contact', type='json', auth="user", methods=['POST'])
    def create_child_contact(self, **kwargs):
        """Create a new child contact."""
        name = kwargs.get('name')
        email = kwargs.get('email')
        phone = kwargs.get('phone')
        relation = kwargs.get('relation')
        parent_id = request.env.user.partner_id.id  # Example parent ID (assuming user is the parent)

        child_contact = request.env['res.partner'].create({
            'name': name,
            'email': email,
            'phone': phone,
            'parent_id': parent_id,  # Link to parent
            'relation': relation,
            'comment': "Nominee Details",
            'type': 'contact'
        })

        return {'success': True, 'child_id': child_contact.id}

    @http.route('/update_nominee_contact', type='json', auth="user", methods=['POST'])
    def update_child_contact(self, **kwargs):
        """Update an existing child contact."""
        child_id = kwargs.get('child_id')
        name = kwargs.get('name')
        email = kwargs.get('email')
        phone = kwargs.get('phone')
        relation = kwargs.get('relation')

        child_contact = request.env['res.partner'].browse(int(child_id))
        _logger.info("child_contact : %s", child_contact)
        if child_contact.exists():
            child_contact.write({
                'name': name,
                'email': email,
                'phone': phone,
                'relation': relation
            })
            return {'success': True}
        else:
            return {'success': False, 'error': 'Nominee contact not found'}

    @http.route('/delete_nominee_contact', type='json', auth="user", methods=['POST'])
    def delete_child_contact(self, **kwargs):
        """Delete an existing child contact."""
        child_id = kwargs.get('child_id')

        child_contact = request.env['res.partner'].browse(int(child_id))
        if child_contact.exists():
            child_contact.unlink()
            return {'success': True}
        else:
            return {'success': False, 'error': 'Nominee contact not found'}

    @http.route('/api/v1/nominees', type='http', auth="user", csrf=False)
    def list_nominees(self):
        """Get list of all nominees for the current user with optional search filter"""
        try:
            # Get search query from request parameters
            search_query = request.params.get('search', '')
            
            # Build domain with base conditions
            domain = [
                ('parent_id', '=', request.env.user.partner_id.id),
                ('type', '=', 'contact')
            ]
            
            # Add search filter if provided
            if search_query:
                domain += [
                    ('name', 'ilike', search_query)
                ]

            _logger.info("Domain: ", domain)

            nominees = request.env['res.partner'].sudo().search(domain)

            return json.dumps({
                'status': 'success',
                'data': [{
                    'id': nominee.id,
                    'name': nominee.name,
                    'phone': nominee.phone,
                } for nominee in nominees],
                'total_count': len(nominees)
            })
        except Exception as e:
            _logger.error("Error fetching nominees list: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })

    @http.route('/api/v1/nominees/<int:nominee_id>', type='http', auth="user", csrf=False)
    def get_nominee_details(self, nominee_id):
        """Get detailed information for a specific nominee"""
        try:
            nominee = request.env['res.partner'].sudo().search([
                ('id', '=', nominee_id),
                ('parent_id', '=', request.env.user.partner_id.id),
                ('type', '=', 'contact')
            ], limit=1)

            if not nominee:
                return json.dumps({
                    'status': 'error',
                    'message': 'Nominee not found or access denied'
                })

            return json.dumps({
                'status': 'success',
                'data': {
                    'id': nominee.id,
                    'name': nominee.name,
                    'email': nominee.email,
                    'phone': nominee.phone,
                    'relation': nominee.relation,
                    }
            })
        except Exception as e:
            _logger.error("Error fetching nominee details: %s", str(e))
            return json.dumps({
                'status': 'error',
                'message': str(e)
            })


