#!/usr/bin/env python3
"""
Complete Test Runner for AI Module Generator
===========================================

This script runs the complete testing workflow:
1. Tests module generation
2. Tests module installation
3. Analyzes logs and frontend functionality
4. Applies fixes if needed
5. Updates the generator with fixes

Usage:
    python3 run_complete_test.py                    # Run full test suite
    python3 run_complete_test.py --module <name>    # Test specific module
    python3 run_complete_test.py --fix-only <name>  # Only run fixes for module

Author: AI Assistant
Version: 1.0.0
"""

import sys
import argparse
import logging
from datetime import datetime

from comprehensive_module_test import ComprehensiveModuleTest
from module_fix_automation import ModuleFixAutomation
from odoo_config import ODOO_CONFIG

def setup_logging():
    """Setup logging for the test runner"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'/tmp/ai_module_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger('TestRunner')

def run_full_test_suite():
    """Run the complete test suite"""
    logger = setup_logging()
    logger.info("🚀 STARTING COMPLETE AI MODULE GENERATOR TEST SUITE")
    logger.info("=" * 70)
    
    try:
        # Step 1: Run comprehensive module test
        logger.info("📋 Step 1: Running comprehensive module test...")
        test_suite = ComprehensiveModuleTest()
        test_success = test_suite.run_complete_test()
        
        if test_success:
            logger.info("✅ Comprehensive test passed!")
            return True
        else:
            logger.warning("⚠️  Comprehensive test found issues")
            
            # Step 2: Try to fix issues automatically
            logger.info("📋 Step 2: Attempting automatic fixes...")
            if test_suite.test_module_name:
                module_name = f"ovakil_{test_suite.test_module_name}"
                fix_automation = ModuleFixAutomation()
                fix_success = fix_automation.run_fix_workflow(module_name)
                
                if fix_success:
                    logger.info("✅ Automatic fixes applied successfully!")
                    
                    # Step 3: Re-run test to verify fixes
                    logger.info("📋 Step 3: Re-running test to verify fixes...")
                    retest_suite = ComprehensiveModuleTest()
                    retest_success = retest_suite.run_complete_test()
                    
                    if retest_success:
                        logger.info("🎉 All tests passed after fixes!")
                        return True
                    else:
                        logger.error("❌ Tests still failing after fixes")
                        return False
                else:
                    logger.error("❌ Automatic fixes failed")
                    return False
            else:
                logger.error("❌ No test module to fix")
                return False
                
    except Exception as e:
        logger.error(f"❌ Test suite failed: {str(e)}")
        return False
    finally:
        # Cleanup
        try:
            if 'test_suite' in locals():
                test_suite.cleanup_test_module()
        except:
            pass

def test_specific_module(module_name: str):
    """Test a specific existing module"""
    logger = setup_logging()
    logger.info(f"🧪 TESTING SPECIFIC MODULE: {module_name}")
    logger.info("=" * 50)
    
    try:
        # Run fix automation on the specific module
        fix_automation = ModuleFixAutomation()
        success = fix_automation.run_fix_workflow(module_name)
        
        if success:
            logger.info(f"✅ Module {module_name} tested and fixed successfully!")
        else:
            logger.error(f"❌ Module {module_name} testing failed")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Module testing failed: {str(e)}")
        return False

def fix_only_module(module_name: str):
    """Only run fixes for a specific module"""
    logger = setup_logging()
    logger.info(f"🔧 FIXING MODULE: {module_name}")
    logger.info("=" * 40)
    
    try:
        fix_automation = ModuleFixAutomation()
        success = fix_automation.run_fix_workflow(module_name)
        
        if success:
            logger.info(f"✅ Module {module_name} fixed successfully!")
        else:
            logger.error(f"❌ Module {module_name} fixing failed")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Module fixing failed: {str(e)}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Complete Test Runner for AI Module Generator',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 run_complete_test.py                           # Run full test suite
  python3 run_complete_test.py --module ovakil_legal     # Test specific module
  python3 run_complete_test.py --fix-only ovakil_legal   # Only fix specific module
        """
    )
    
    parser.add_argument(
        '--module',
        type=str,
        help='Test a specific existing module'
    )
    
    parser.add_argument(
        '--fix-only',
        type=str,
        help='Only run fixes for a specific module (no testing)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        if args.fix_only:
            # Only run fixes
            success = fix_only_module(args.fix_only)
        elif args.module:
            # Test specific module
            success = test_specific_module(args.module)
        else:
            # Run full test suite
            success = run_full_test_suite()
        
        # Print final result
        print("\n" + "=" * 60)
        if success:
            print("🎉 ALL OPERATIONS COMPLETED SUCCESSFULLY!")
            print("✅ AI Module Generator is working correctly!")
        else:
            print("❌ SOME OPERATIONS FAILED!")
            print("🔧 Manual intervention may be required!")
        print("=" * 60)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Operation failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
