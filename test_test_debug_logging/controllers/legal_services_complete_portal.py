from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal
from odoo.exceptions import AccessError, MissingError
import logging

_logger = logging.getLogger(__name__)


class LegalServicesCompletePortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        """Add Complete Legal Services Application count to portal home"""
        values = super()._prepare_home_portal_values(counters)

        if 'legal_services_complete' in counters:
            partner = request.env.user.partner_id
            legal_services_complete_count = request.env['ovakil_legal_services_complete.legal_services_complete'].search_count([
                '|', '|',
                ('customer_email', '=', partner.email),
                ('partner_id', '=', partner.id),
                ('create_uid', '=', request.env.user.id)
            ]) if partner.email else 0
            values['legal_services_complete_count'] = legal_services_complete_count

        return values

    @http.route(['/my/legal-services-complete'], type='http', auth="user", website=True)
    def portal_legal_services_complete_list(self, **kw):
        """Display Complete Legal Services Application list in customer portal"""
        partner = request.env.user.partner_id

        if not partner.email:
            return request.render('portal.portal_my_home')

        # Base domain for user's records
        domain = [
            '|', '|',
            ('customer_email', '=', partner.email),
            ('partner_id', '=', partner.id),
            ('create_uid', '=', request.env.user.id)
        ]

        # Handle search functionality
        search_query = kw.get('search', '').strip()
        if search_query:
            # Enhanced search across multiple fields including text fields
            search_domain = [
                '|', '|', '|', '|', '|',
                ('name', 'ilike', search_query),
                ('customer_name', 'ilike', search_query),
                ('customer_email', 'ilike', search_query),
                ('client_name', 'ilike', search_query),
                ('client_email', 'ilike', search_query),
                ('case_details', 'ilike', search_query)
            ]
            domain = ['&'] + domain + search_domain

        # Handle state filter
        state_filter = kw.get('state', '').strip()
        if state_filter:
            domain.append(('state', '=', state_filter))

        legal_services_complete_records = request.env['ovakil_legal_services_complete.legal_services_complete'].search(domain, order='create_date desc')

        # Get available states for filter dropdown
        all_states = request.env['ovakil_legal_services_complete.legal_services_complete']._fields['state'].selection

        values = {
            'legal_services_complete_records': legal_services_complete_records,
            'page_name': 'Complete Legal Services Application',
            'search_query': search_query,
            'state_filter': state_filter,
            'available_states': all_states,
        }

        return request.render(f'test_test_debug_logging.portal_legal_services_complete_list', values)

    @http.route(['/my/legal-services-complete/<int:record_id>'], type='http', auth="user", website=True)
    def portal_legal_services_complete_detail(self, record_id, **kw):
        """Display Complete Legal Services Application detail in customer portal"""
        try:
            legal_services_complete_sudo = self._document_check_access('ovakil_legal_services_complete.legal_services_complete', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {
            'legal_services_complete': legal_services_complete_sudo,
            'page_name': 'Complete Legal Services Application',
        }

        return request.render(f'test_test_debug_logging.portal_legal_services_complete_detail', values)

    @http.route(['/my/legal-services-complete/<int:record_id>/edit'], type='http', auth="user", website=True)
    def portal_legal_services_complete_edit(self, record_id, **kw):
        """Edit Complete Legal Services Application in customer portal (only for draft state)"""
        try:
            legal_services_complete_sudo = self._document_check_access('ovakil_legal_services_complete.legal_services_complete', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if record is in initial state (editable) - feedback_submitted is the initial state
        editable_state = 'feedback_submitted'

        if legal_services_complete_sudo.state != editable_state:
            return request.redirect('/my/legal-services-complete/' + str(record_id))

        values = {
            'legal_services_complete': legal_services_complete_sudo,
            'page_name': 'Complete Legal Services Application',
            'is_edit_mode': True,
        }

        return request.render(f'test_test_debug_logging.portal_legal_services_complete_edit', values)

    @http.route(['/my/legal-services-complete/<int:record_id>/update'], type='http', auth="user", website=True, methods=['POST'], csrf=False)
    def portal_legal_services_complete_update(self, record_id, **kw):
        """Update Complete Legal Services Application from customer portal"""
        try:
            legal_services_complete_sudo = self._document_check_access('ovakil_legal_services_complete.legal_services_complete', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if record is in initial state (editable) - feedback_submitted is the initial state
        editable_state = 'feedback_submitted'

        if legal_services_complete_sudo.state != editable_state:
            return request.redirect('/my/legal-services-complete/' + str(record_id))

        # Extract form data and update record
        update_values = {}

        # Process form fields
        for field_name, field_value in kw.items():
            if field_name.startswith('csrf_token') or field_name in ['submit']:
                continue
            if field_value:
                update_values[field_name] = field_value

        # Update the record
        if update_values:
            legal_services_complete_sudo.sudo().write(update_values)

        return request.redirect('/my/legal-services-complete/' + str(record_id) + '?updated=1')

    @http.route(['/my/legal-services-complete/<int:record_id>/download-pdf'], type='http', auth="user", website=True)
    def portal_legal_services_complete_download_pdf(self, record_id, **kw):
        """Download AI Response PDF for paid customers"""
        try:
            legal_services_complete_sudo = self._document_check_access('ovakil_legal_services_complete.legal_services_complete', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if customer has paid
        if legal_services_complete_sudo.payment_status != 'paid':
            return request.render('portal.portal_error', {
                'error_title': _('Access Denied'),
                'error_message': _('PDF download is only available for paid customers. Please complete your payment first.'),
            })

        # ✨ ALWAYS regenerate PDF based on current AI response content
        try:
            # If no AI response exists, generate it first
            if not legal_services_complete_sudo.ai_response:
                _logger.info("Generating AI response for portal download - record %s", record_id)
                legal_services_complete_sudo.action_get_ai_response()
                # Refresh the record to get updated data
                legal_services_complete_sudo = legal_services_complete_sudo.browse(record_id)

            # ALWAYS generate fresh PDF based on current AI response content
            _logger.info("Generating fresh PDF from current AI response for portal download - record %s", record_id)
            legal_services_complete_sudo.action_generate_ai_pdf()
            # Refresh the record to get updated data
            legal_services_complete_sudo = legal_services_complete_sudo.browse(record_id)

        except Exception as e:
            _logger.error("Error generating AI response/PDF for record %s: %s", record_id, str(e))
            return request.render('portal.portal_error', {
                'error_title': _('Generation Error'),
                'error_message': _('Failed to generate AI response or PDF. Please try again later or contact support.'),
            })

        # Final check if PDF is available after generation attempts
        if not legal_services_complete_sudo.ai_response_pdf:
            return request.render('portal.portal_error', {
                'error_title': _('PDF Not Available'),
                'error_message': _('AI Response PDF could not be generated. Please contact support.'),
            })

        # Return the PDF file
        pdf_data_base64 = legal_services_complete_sudo.ai_response_pdf
        filename = legal_services_complete_sudo.ai_response_pdf_filename or 'legal_services_complete_ai_response_%s.pdf' % record_id

        # Decode base64 PDF data to binary
        import base64
        try:
            pdf_data = base64.b64decode(pdf_data_base64)
        except Exception as e:
            _logger.error("Error decoding PDF data for record %s: %s", record_id, str(e))
            return request.render('portal.portal_error', {
                'error_title': _('PDF Decode Error'),
                'error_message': _('Failed to decode PDF data. Please try regenerating the PDF.'),
            })

        return request.make_response(
            pdf_data,
            headers=[
                ('Content-Type', 'application/pdf'),
                ('Content-Disposition', 'attachment; filename="%s"' % filename),
                ('Content-Length', len(pdf_data)),
            ]
        )
