id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_legal_services_complete_user,Complete Legal Services Application User Access,model_ovakil_legal_services_complete_legal_services_complete,base.group_user,1,1,1,1
access_legal_services_complete_manager,Complete Legal Services Application Manager Access,model_ovakil_legal_services_complete_legal_services_complete,base.group_system,1,1,1,1
access_legal_document_line_user,Legal Document Line User Access,model_legal_document_line,base.group_user,1,1,1,1
access_legal_document_line_manager,Legal Document Line Manager Access,model_legal_document_line,base.group_system,1,1,1,1
access_consultation_note_user,Consultation Note User Access,model_consultation_note,base.group_user,1,1,1,1
access_consultation_note_manager,Consultation Note Manager Access,model_consultation_note,base.group_system,1,1,1,1
access_legal_payment_transaction_user,Legal Payment Transaction User Access,model_legal_payment_transaction,base.group_user,1,1,1,1
access_legal_payment_transaction_manager,Legal Payment Transaction Manager Access,model_legal_payment_transaction,base.group_system,1,1,1,1
access_case_update_user,Case Update User Access,model_case_update,base.group_user,1,1,1,1
access_case_update_manager,Case Update Manager Access,model_case_update,base.group_system,1,1,1,1
