<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Index Template -->
        <template id="index" name="Complete Legal Services Application Index">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <h1 class="mt-4">Complete Legal Services Application</h1>
                                <p class="lead">Comprehensive legal services management system with client portal, document management, and payment integration</p>

                                <div class="row mt-4">
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">Complete Legal Services Application</h5>
                                                <p class="card-text">Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI</p>
                                                <a href="/legal-services-complete" class="btn btn-primary">Access Form</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Complete Legal Services Application Website Template -->
        <template id="legal_services_complete_website_form" name="Complete Legal Services Application Form">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Enhanced Form Styling -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                    <link rel="stylesheet" href="/ovakil_legal_services_complete/static/src/css/enhanced_forms.css"/>
                </t>
                <div id="wrap" class="enhanced-form-wrapper">
                    <!-- Progress Steps (only show if step-by-step is enabled) -->
                    <div class="container" t-if="true">
                        <div class="form-progress-steps">
                            <div class="step-item active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-label">Client Information</div>
                            </div>
                            <div class="step-item" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-label">Service Details</div>
                            </div>
                            <div class="step-item" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-label">Review &amp; Submit</div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-lg-10 offset-lg-1">
                                <!-- Form Header Card -->
                                <div class="form-header-card">
                                    <div class="form-header-content">
                                        <h1 class="form-title">Complete Legal Services Application</h1>
                                        <p class="form-description">Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI</p>
                                        <div class="form-meta">
                                            <span class="form-meta-item">
                                                <i class="fas fa-clock"></i>
                                                Estimated time: 5-10 minutes
                                            </span>
                                            <span class="form-meta-item">
                                                <i class="fas fa-shield-alt"></i>
                                                Secure &amp; Confidential
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Form Card -->
                                <div class="main-form-card">
                                    <form id="enhanced-form" action="/legal-services-complete/submit" method="post" class="enhanced-form" novalidate="novalidate">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                        <!-- Step 1: Client Information -->
                                        <div class="form-section" data-step="1">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-user"></i>
                                                Client Information
                                            </h3>
                                            <div class="form-content">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="client_name" class="required">Full Name</label>
                                                <input type="text" class="form-control" name="client_name" required="required" placeholder="Enter full name"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid full name.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="client_email" class="required">Email Address</label>
                                                <input type="text" class="form-control" name="client_email" required="required" placeholder="Enter email address"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="client_phone" class="required">Phone Number</label>
                                                <input type="text" class="form-control" name="client_phone" required="required" placeholder="Enter phone number"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid phone number.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6"></div>
                                            </div>
                                            <!-- Step Navigation -->
                                            <div class="step-navigation">
                                                <div></div> <!-- Empty div for spacing -->
                                                <button type="button" class="btn btn-primary btn-next-step">
                                                    Next: Service Details <i class="fas fa-arrow-right"></i>
                                                </button>
                                            </div>
                                            </div>
                                        </div>

                                        <!-- Step 2: Service Details -->
                                        <div class="form-section" data-step="2" style="display: none;">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-gavel"></i>
                                                Service Details
                                            </h3>
                                            <div class="form-content">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="service_type" class="required">Legal Service Required</label>
                                                <select class="form-control" name="service_type" required="required"
                                                        data-field-type="selection" data-required="true">
                                                    <option value="">Select...</option>
                                                    <option value="business_registration">Business Registration</option>
                                                    <option value="legal_notice">Legal Notice</option>
                                                    <option value="contract_drafting">Contract Drafting</option>
                                                    <option value="court_representation">Court Representation</option>
                                                    <option value="property_documentation">Property Documentation</option>
                                                    <option value="trademark_registration">Trademark Registration</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="urgency_level" class="required">Urgency Level</label>
                                                <select class="form-control" name="urgency_level" required="required"
                                                        data-field-type="selection" data-required="true">
                                                    <option value="">Select...</option>
                                                    <option value="standard">Standard (7-10 days)</option>
                                                    <option value="priority">Priority (3-5 days)</option>
                                                    <option value="urgent">Urgent (1-2 days)</option>
                                                    <option value="emergency">Emergency (Same day)</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="case_details" class="required">Case Details &amp; Requirements</label>
                                                <textarea class="form-control" name="case_details" required="required" placeholder="Enter case details &amp; requirements" rows="4"
                                                          data-field-type="text" data-required="true"></textarea>
                                                <div class="invalid-feedback">Please provide a valid case details &amp; requirements.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Preferred Lawyer</label>
                                                <div class="many2one-searchable">
                                                    <input type="text" class="form-control"
                                                           placeholder="Search for preferred lawyer..."
                                                           data-widget="many2one_searchable" data-model="res.users"
                                                           data-field-type="many2one" data-required="false"
                                                           data-label="Preferred Lawyer"/>
                                                    <i class="fas fa-search search-icon"></i>
                                                    <div class="many2one-dropdown-results"></div>
                                                </div>
                                                    <input type="hidden" name="assigned_lawyer" />
                                                </div>
                                                <div class="invalid-feedback">Please select a valid preferred lawyer.</div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Required Documents</label>
                                                <input type="hidden" name="document_lines" 
                                                       data-widget="one2many_list"
                                                       data-field-type="one2many" data-required="false" />
                                                <div class="invalid-feedback">Please add at least one required documents.</div>
                                                </div>
                                                <div class="valid-feedback">Items added successfully!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Consultation Notes</label>
                                                <input type="hidden" name="consultation_notes" 
                                                       data-widget="one2many_list"
                                                       data-field-type="one2many" data-required="false" />
                                                <div class="invalid-feedback">Please add at least one consultation notes.</div>
                                                </div>
                                                <div class="valid-feedback">Items added successfully!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Payment History</label>
                                                <input type="hidden" name="payment_history" 
                                                       data-widget="one2many_list"
                                                       data-field-type="one2many" data-required="false" />
                                                <div class="invalid-feedback">Please add at least one payment history.</div>
                                                </div>
                                                <div class="valid-feedback">Items added successfully!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Case Progress Updates</label>
                                                <input type="hidden" name="case_updates" 
                                                       data-widget="one2many_list"
                                                       data-field-type="one2many" data-required="false" />
                                                <div class="invalid-feedback">Please add at least one case progress updates.</div>
                                                </div>
                                                <div class="valid-feedback">Items added successfully!</div>
                                            </div>
                                            </div>
                                            <!-- Step Navigation -->
                                            <div class="step-navigation">
                                                <button type="button" class="btn btn-secondary btn-prev-step">
                                                    <i class="fas fa-arrow-left"></i> Previous: Client Information
                                                </button>
                                                <button type="button" class="btn btn-primary btn-next-step">
                                                    Next: Review &amp; Submit <i class="fas fa-arrow-right"></i>
                                                </button>
                                            </div>
                                            </div>
                                        </div>

                                        <!-- Step 3: Review & Submit -->
                                        <div class="form-section" data-step="3" style="display: none;">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-check-circle"></i>
                                                Review &amp; Submit
                                            </h3>

                                            <!-- Review Summary -->
                                            <div class="review-summary">
                                                <div class="review-header">
                                                    <div class="review-header-content">
                                                        <div class="review-icon">
                                                            <i class="fas fa-clipboard-check"></i>
                                                        </div>
                                                        <div class="review-text">
                                                            <h4>Review Your Information</h4>
                                                            <p>Please verify all details before submitting your application</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="review-content">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="review-section">
                                                                <div class="review-section-header">
                                                                    <i class="fas fa-user"></i>
                                                                    <h6>Personal Information</h6>
                                                                    <button type="button" class="btn-edit-section" data-step="1">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                </div>
                                                                <div id="review-client-info" class="review-section-content">
                                                                    <!-- Review content will be populated by JavaScript -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="review-section">
                                                                <div class="review-section-header">
                                                                    <i class="fas fa-gavel"></i>
                                                                    <h6>Service Details</h6>
                                                                    <button type="button" class="btn-edit-section" data-step="2">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                </div>
                                                                <div id="review-service-details" class="review-section-content">
                                                                    <!-- Review content will be populated by JavaScript -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Summary Stats -->
                                                <div class="review-stats">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="stat-card">
                                                                <div class="stat-icon">
                                                                    <i class="fas fa-check-circle"></i>
                                                                </div>
                                                                <div class="stat-content">
                                                                    <span class="stat-number" id="completed-fields">0</span>
                                                                    <span class="stat-label">Fields Completed</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="stat-card">
                                                                <div class="stat-icon">
                                                                    <i class="fas fa-clock"></i>
                                                                </div>
                                                                <div class="stat-content">
                                                                    <span class="stat-number">~5 min</span>
                                                                    <span class="stat-label">Processing Time</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="stat-card">
                                                                <div class="stat-icon">
                                                                    <i class="fas fa-shield-alt"></i>
                                                                </div>
                                                                <div class="stat-content">
                                                                    <span class="stat-number">100%</span>
                                                                    <span class="stat-label">Secure</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Submit Section -->
                                            <div class="form-submit-section mt-4">
                                                <div class="text-center">
                                                    <button type="submit" class="btn btn-submit-enhanced btn-lg"
                                                            data-original-text="Submit {self._escape_xml(form.name)}">
                                                        <i class="fas fa-paper-plane"></i>
                                                        Submit {self._escape_xml(form.name)}
                                                    </button>
                                                    <p class="form-text mt-3">
                                                        <i class="fas fa-lock"></i>
                                                        Your information is secure and will be processed confidentially.
                                                    </p>
                                                </div>
                                            </div>

                                            <!-- Step Navigation -->
                                            <div class="step-navigation">
                                                <button type="button" class="btn btn-secondary btn-prev-step">
                                                    <i class="fas fa-arrow-left"></i> Previous: Service Details
                                                </button>
                                                <div></div> <!-- Empty div for spacing -->
                                            </div>
                                        </div>
                                </form>

                                </div>

                                <!-- Success Section (Hidden initially) -->
                                <div class="main-form-card" id="success-section" style="display: none;">
                                    <div class="text-center py-5">
                                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        <h2 class="mt-3 mb-3">Thank You!</h2>
                                        <p class="lead">Your {self._escape_xml(form.name.lower())} has been submitted successfully.</p>
                                        <p class="text-muted">You will receive a confirmation email shortly.</p>
                                        <a href="/{self.module_name}" class="btn btn-primary mt-3">
                                            <i class="fas fa-home"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Form JavaScript -->
                    <script src="/{self.custom_prefix}_{self.module_name}/static/src/js/enhanced_forms.js"></script>
                    <script>
                        // Initialize enhanced form handler with step-by-step configuration
                        document.addEventListener('DOMContentLoaded', function() {{
                            if (typeof EnhancedFormHandler !== 'undefined') {{
                                const formHandler = new EnhancedFormHandler();
                                // Configure step-by-step mode
                                formHandler.enableStepByStep = {str(form.enable_step_form).lower()};
                                if (!formHandler.enableStepByStep) {{
                                    // Show all form sections if not step-by-step
                                    const sections = document.querySelectorAll('.form-section');
                                    sections.forEach(section => section.style.display = 'block');
                                    // Hide progress steps if not step-by-step
                                    const progressSteps = document.querySelector('.form-progress-steps');
                                    if (progressSteps) progressSteps.style.display = 'none';
                                }}
                            }}
                        }});
                    </script>
                </div>
            </t>
        </template>

        <!-- Legal Services Success Template -->
        <template id="legal_services_complete_success" name="Legal Services Complete Success">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8 offset-lg-2">
                                <h1 class="mt-4 text-success">Thank You!</h1>
                                <p class="lead">Your legal services application has been submitted successfully.</p>
                                <a href="/legal-services-complete" class="btn btn-primary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Portal Templates for Complete Legal Services Application -->
        <!-- Customer Portal List Template -->
        <template id="portal_legal_services_complete_list" name="Complete Legal Services Application Portal List">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Complete Legal Services Application</t>
                </t>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Your Complete Legal Services Application Records</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="legal_services_complete_records">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Payment Status</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="legal_services_complete_records" t-as="record">
                                                    <tr>
                                                        <td><a t-attf-href="/my/legal-services-complete/#{record.id}"><t t-esc="record.name"/></a></td>
                                                        <td><span class="badge bg-primary text-white" t-field="record.state"/></td>
                                                        <td>
                                                            <span t-attf-class="badge #{record.payment_status == 'paid' and 'bg-success text-white' or record.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                                <span t-field="record.payment_status"/>
                                                            </span>
                                                        </td>
                                                        <td><t t-esc="record.create_date" t-options="{'widget': 'date'}"/></td>
                                                        <td>
                                                            <a t-attf-href="/my/legal-services-complete/#{record.id}" class="btn btn-sm btn-primary">View</a>
                                                            <t t-if="record.state == 'feedback_submitted'">
                                                                <a t-attf-href="/my/legal-services-complete/#{record.id}/edit" class="btn btn-sm btn-secondary">Edit</a>
                                                            </t>
                                                            <t t-if="record.payment_status == 'pending' and record.payment_url">
                                                                <a t-att-href="record.payment_url" class="btn btn-sm btn-warning">Pay Now</a>
                                                            </t>
                                                            <t t-elif="record.payment_status == 'pending'">
                                                                <span class="btn btn-sm btn-outline-warning disabled">Payment Pending</span>
                                                            </t>
                                                            <t t-if="record.payment_status == 'paid'">
                                                                <a t-attf-href="/my/legal-services-complete/#{record.id}/download-pdf" class="btn btn-sm btn-success">
                                                                    <i class="fa fa-download"/> Download PDF
                                                                </a>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        <p>You don't have any complete legal services application records yet.</p>
                                        <a href="/legal-services-complete" class="btn btn-primary">Create New Record</a>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Portal Detail Template -->
        <template id="portal_legal_services_complete_detail" name="Complete Legal Services Application Portal Detail">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4><t t-esc="legal_services_complete.name"/></h4>
                                <div>
                                    <t t-if="legal_services_complete.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/edit" class="btn btn-secondary">Edit</a>
                                    </t>
                                    <t t-if="legal_services_complete.payment_status == 'paid'">
                                        <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/download-pdf" class="btn btn-success">
                                            <i class="fa fa-download"/> Download PDF
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Basic Information</h5>
                                        <p><strong>Name:</strong> <t t-esc="legal_services_complete.name"/></p>
                                        <p><strong>Status:</strong> <span class="badge bg-primary text-white" t-field="legal_services_complete.state"/></p>
                                        <p><strong>Created:</strong> <t t-esc="legal_services_complete.create_date" t-options="{'widget': 'datetime'}"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Rate:</strong> $<t t-esc="legal_services_complete.payment_rate"/></p>
                                        <p><strong>Payment Amount:</strong> $<t t-esc="legal_services_complete.payment_amount"/></p>
                                        <p><strong>Payment Status:</strong>
                                            <span t-attf-class="badge #{legal_services_complete.payment_status == 'paid' and 'bg-success text-white' or legal_services_complete.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                <span t-field="legal_services_complete.payment_status"/>
                                            </span>
                                        </p>
                                        <t t-if="legal_services_complete.payment_method">
                                            <p><strong>Payment Method:</strong>
                                                <span class="badge bg-info text-white" t-field="legal_services_complete.payment_method"/>
                                            </p>
                                        </t>
                                        <t t-if="legal_services_complete.payment_url and legal_services_complete.payment_status == 'pending'">
                                            <p><a t-att-href="legal_services_complete.payment_url" class="btn btn-warning">Pay Now</a></p>
                                        </t>
                                    </div>
                                </div>

                                <!-- AI Response Section (only for paid customers) -->
                                <t t-if="legal_services_complete.payment_status == 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">AI Response</h5>
                                                    <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/download-pdf" class="btn btn-light btn-sm">
                                                        <i class="fa fa-download"/> Download PDF
                                                    </a>
                                                </div>
                                                <div class="card-body">
                                                    <t t-raw="legal_services_complete.ai_response"/>

                                                    <!-- PDF Download Section -->
                                                    <div class="text-center mt-4 pt-3 border-top">
                                                        <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/download-pdf" class="btn btn-success btn-lg">
                                                            <i class="fa fa-download"/> Download PDF
                                                        </a>
                                                        <p class="text-muted mt-2">
                                                            <small>Click to download the AI response as PDF document</small>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                <t t-elif="legal_services_complete.payment_status != 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <h5>AI Response Available After Payment</h5>
                                                <p>Complete your payment to access the AI-generated response and download the PDF report.</p>
                                                <t t-if="legal_services_complete.payment_url">
                                                    <a t-att-href="legal_services_complete.payment_url" class="btn btn-warning">Complete Payment</a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>