from odoo import api, fields, models, _


class ConsultationNote(models.Model):
    """Model for Consultation Notes"""
    _name = 'ovakil_legal_services_complete.consultation.note'
    _description = 'Consultation Notes'
    _order = 'sequence, id' if 'sequence' in ['sequence'] else 'create_date desc'

    name = fields.Char(string='Name', required=True)
    description = fields.Text(string='Description')
    sequence = fields.Integer(string='Sequence', default=10)
    legal_service_id = fields.Many2one('ovakil_legal_services_complete.legal_services_complete',
                                      string='Complete Legal Services Application', ondelete='cascade')
    active = fields.Boolean(string='Active', default=True)
