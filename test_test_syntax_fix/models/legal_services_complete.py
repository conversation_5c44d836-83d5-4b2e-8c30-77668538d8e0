from odoo import api, fields, models, _


class LegalServicesComplete(models.Model):
    """
    Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI Model
    Generated from template: Complete Legal Services Portal
    """
    _name = 'ovakil_legal_services_complete.legal_services_complete'
    _description = 'Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(string='Name', required=True, tracking=True)
    client_name = fields.Char(string='Full Name', required=True)
    client_email = fields.Char(string='Email Address', required=True)
    client_phone = fields.Char(string='Phone Number', required=True)
    service_type = fields.Selection([('business_registration', 'Business Registration'), ('legal_notice', 'Legal Notice'), ('contract_drafting', 'Contract Drafting'), ('court_representation', 'Court Representation'), ('property_documentation', 'Property Documentation'), ('trademark_registration', 'Trademark Registration')], string='Legal Service Required', required=True)
    urgency_level = fields.Selection([('standard', 'Standard (7-10 days)'), ('priority', 'Priority (3-5 days)'), ('urgent', 'Urgent (1-2 days)'), ('emergency', 'Emergency (Same day)')], string='Urgency Level', required=True)
    case_details = fields.Text(string='Case Details & Requirements', required=True)
    assigned_lawyer = fields.Many2one('res.users', string='Preferred Lawyer')
    document_lines = fields.One2many('legal.document.line', string='Required Documents', 'legal_service_id')
    consultation_notes = fields.One2many('consultation.note', string='Consultation Notes', 'legal_service_id')
    payment_history = fields.One2many('payment.transaction', string='Payment History', 'legal_service_id')
    case_updates = fields.One2many('case.update', string='Case Progress Updates', 'legal_service_id')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('consultation_scheduled', 'Consultation Scheduled'),
        ('payment_pending', 'Payment Pending'),
        ('in_progress', 'In Progress'),
        ('document_review', 'Document Review'),
        ('completed', 'Completed')
    ], string='State', default='draft', tracking=True)
    active = fields.Boolean(string='Active', default=True)
    color = fields.Integer(string='Color', default=0, help='Color for kanban view')
    customer_email = fields.Char(string='Customer Email', help='Customer email for portal access')
    partner_id = fields.Many2one('res.partner', string='Customer', help='Related customer partner')
    # Payment Fields
    payment_rate = fields.Float(string='Payment Rate', default=0.0, tracking=True, help='Rate for this record (can be overridden)')
    payment_amount = fields.Float(string='Payment Amount', compute='_compute_payment_amount', store=True, help='Computed payment amount based on rate')
    rate_changed_by = fields.Many2one('res.users', string='Rate Changed By', readonly=True, help='User who last changed the rate')
    rate_changed_date = fields.Datetime(string='Rate Changed Date', readonly=True, help='When the rate was last changed')
    sales_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True, help='Related sales order for payment')
    payment_status = fields.Selection([
        ('none', 'No Payment Required'),
        ('pending', 'Payment Pending'),
        ('paid', 'Payment Completed'),
        ('failed', 'Payment Failed'),
    ], string='Payment Status', default='none', tracking=True, compute='_compute_payment_status', store=True)
    payment_url = fields.Char(string='Payment URL', readonly=True, help='Customer portal payment link')
    payment_method = fields.Selection([
        ('online', 'Online Payment'),
        ('cash', 'Cash Payment'),
        ('bank_transfer', 'Bank Transfer'),
        ('check', 'Check'),
    ], string='Payment Method', readonly=True, help='Method used for payment')
    cash_collected_by = fields.Many2one('res.users', string='Cash Collected By', readonly=True, help='User who collected cash payment')
    cash_collection_date = fields.Datetime(string='Cash Collection Date', readonly=True, help='When cash was collected')
    # ✨ AI Response Fields
    ai_response = fields.Html(string='AI Response', help='AI-generated response based on record data')
    ai_response_generated = fields.Boolean(string='AI Response Generated', default=False)
    ai_response_pdf = fields.Binary(string='AI Response PDF')
    ai_response_pdf_filename = fields.Char(string='AI Response PDF Filename')
    ai_last_prompt = fields.Text(string='Last AI Prompt', help='Last prompt sent to AI')
    ai_response_last_updated = fields.Datetime(string='AI Response Last Updated', help='When AI response was last modified')
    # ✨ Addon Configuration
    addon_ids = fields.Many2many('product.product', 'ovakil_legal_se_addon_rel', 'service_id', 'addon_id',
                                string='Selected Addons')
    addon_total_amount = fields.Float(string='Addon Total Amount', compute='_compute_addon_total', store=True)
    total_amount = fields.Float(string='Total Amount', compute='_compute_total_amount', store=True)
    # ✨ Admin Approval Configuration
    requires_admin_approval = fields.Boolean(string='Requires Admin Approval', default=False,
                                           help='If checked, submission will require admin approval before payment')
    admin_approval_status = fields.Selection([
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected')
    ], string='Admin Approval Status', default='pending')


    @api.depends('payment_rate')
    def _compute_payment_amount(self):
        """Compute payment amount based on rate"""
        for record in self:
            record.payment_amount = record.payment_rate

    @api.depends('addon_ids')
    def _compute_addon_total(self):
        """Compute total amount for selected addons"""
        for record in self:
            record.addon_total_amount = sum(record.addon_ids.mapped('list_price'))

    @api.depends('payment_amount', 'addon_total_amount')
    def _compute_total_amount(self):
        """Compute total amount including addons"""
        for record in self:
            record.total_amount = record.payment_amount + record.addon_total_amount

    def _compute_payment_status(self):
        """Compute payment status based on sales order state"""
        for record in self:
            # Store old payment status to detect changes
            old_payment_status = record.payment_status

            if not record.sales_order_id:
                record.payment_status = 'none'
                continue

            # Get all posted invoices from the sales order
            invoices = record.sales_order_id.invoice_ids.filtered(lambda inv: inv.state == 'posted' and inv.move_type == 'out_invoice')

            if not invoices:
                # No invoices yet
                if record.sales_order_id.state in ['sale', 'done']:
                    record.payment_status = 'pending'
                else:
                    record.payment_status = 'none'
            else:
                # Check payment status of invoices
                total_amount = sum(invoices.mapped('amount_total'))
                paid_amount = sum(invoices.mapped('amount_residual_signed'))

                if paid_amount <= 0:  # Fully paid (residual is 0 or negative)
                    record.payment_status = 'paid'
                elif paid_amount < total_amount:  # Partially paid
                    record.payment_status = 'pending'
                else:  # Not paid
                    record.payment_status = 'pending'

            # Auto-generate AI response and PDF when payment becomes "paid"
            if old_payment_status != 'paid' and record.payment_status == 'paid':
                record._auto_generate_ai_response_and_pdf()

    def _auto_generate_ai_response_and_pdf(self):
        """Automatically generate AI response and PDF when payment is completed"""
        try:
            # Generate AI response if not already generated
            if not self.ai_response or not self.ai_response_generated:
                self.action_get_ai_response()

            # Generate PDF if not already generated or if AI response was updated
            if not self.ai_response_pdf or self.ai_response:
                self.action_generate_ai_pdf()

        except Exception as e:
            # Log error but don't fail the payment process
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning("Failed to auto-generate AI response and PDF for {}: {}".format(self.name, str(e)))

    @api.model
    def create(self, vals):
        """Override create to automatically link customer"""
        # Auto-link customer based on email if not already set
        if not vals.get('partner_id') and vals.get('customer_email'):
            partner = self._find_or_create_partner(vals.get('customer_email'), vals.get('customer_name'))
            if partner:
                vals['partner_id'] = partner.id

        return super().create(vals)

    def _find_or_create_partner(self, email, name=None):
        """Find existing partner by email or create new one"""
        if not email:
            return False

        # Search for existing partner
        partner = self.env['res.partner'].search([('email', '=', email)], limit=1)

        if not partner and name:
            # Create new partner if not found
            partner = self.env['res.partner'].create({
                'name': name or email,
                'email': email,
                'is_company': False,
                'customer_rank': 1,  # Mark as customer
            })

        return partner
    def action_draft_to_submitted(self):
        """Move from Draft to Submitted"""
        self.state = 'submitted'
    def action_submitted_to_consultation_scheduled(self):
        """Move from Submitted to Consultation Scheduled"""
        self.state = 'consultation_scheduled'
    def action_consultation_scheduled_to_payment_pending(self):
        """Move from Consultation Scheduled to Payment Pending"""
        self.state = 'payment_pending'
    def action_payment_pending_to_in_progress(self):
        """Move from Payment Pending to In Progress"""
        self.state = 'in_progress'
    def action_in_progress_to_document_review(self):
        """Move from In Progress to Document Review"""
        self.state = 'document_review'
    def action_document_review_to_completed(self):
        """Move from Document Review to Completed"""
        self.state = 'completed'

    def action_get_ai_response(self):
        """Get AI response for this record"""
        self.ensure_one()

        # Create a fallback AI response
        fallback_response = self._generate_fallback_ai_response()
        self.write({
            'ai_response': fallback_response,
            'ai_response_generated': True,
        })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'AI Response Generated',
                'message': 'AI response has been generated successfully.',
                'type': 'success',
            }
        }

    def _generate_fallback_ai_response(self):
        """Generate a fallback AI response"""
        self.ensure_one()

        response_parts = [
            "<h2>Thank you for your submission!</h2>",
            "<p>We have received your form submission and appreciate you taking the time to provide this information.</p>",
            "<h3>Submission Summary:</h3>",
            "<ul>",
            "<li><strong>Reference:</strong> {}</li>".format(self.name or 'N/A'),
            "<li><strong>Date:</strong> {}</li>".format(
                self.create_date.strftime('%Y-%m-%d') if self.create_date else 'N/A'),
            "</ul>",
            "<h3>Our Response:</h3>",
            "<p>Your submission is valuable to us and helps us improve our services.</p>",
            "<p><strong>Thank you for choosing our services!</strong></p>",
        ]

        return '\n'.join(response_parts)

    def action_generate_ai_pdf(self):
        """Generate PDF from AI response"""
        self.ensure_one()

        if not self.ai_response:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No AI Response',
                    'message': 'Please generate AI response first.',
                    'type': 'warning',
                }
            }

        try:
            import base64
            # Simple PDF generation - just encode the HTML as base64 for now
            pdf_content = base64.b64encode(self.ai_response.encode('utf-8'))
            filename = "ai_response_{}.pdf".format(self.name or 'record')

            self.write({
                'ai_response_pdf': pdf_content,
                'ai_response_pdf_filename': filename,
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'PDF Generated',
                    'message': 'PDF has been generated: {}'.format(filename),
                    'type': 'success',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'PDF Error',
                    'message': 'Failed to generate PDF: {}'.format(str(e)),
                    'type': 'danger',
                }
            }
        
