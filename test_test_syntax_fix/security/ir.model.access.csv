id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_legal_services_complete_user,Complete Legal Services Application User Access,model_ovakil_legal_services_complete_legal_services_complete,base.group_user,1,1,1,1
access_legal_services_complete_manager,Complete Legal Services Application Manager Access,model_ovakil_legal_services_complete_legal_services_complete,base.group_system,1,1,1,1
access_line_user,Required Documents User Access,model_legal_document_line,base.group_user,1,1,1,1
access_line_manager,Required Documents Manager Access,model_legal_document_line,base.group_system,1,1,1,1
access_note_user,Consultation Notes User Access,model_consultation_note,base.group_user,1,1,1,1
access_note_manager,Consultation Notes Manager Access,model_consultation_note,base.group_system,1,1,1,1
access_transaction_user,Payment History User Access,model_payment_transaction,base.group_user,1,1,1,1
access_transaction_manager,Payment History Manager Access,model_payment_transaction,base.group_system,1,1,1,1
access_update_user,Case Progress Updates User Access,model_case_update,base.group_user,1,1,1,1
access_update_manager,Case Progress Updates Manager Access,model_case_update,base.group_system,1,1,1,1
