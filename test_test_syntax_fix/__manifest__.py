{
    'name': 'Test Syntax Fix',
    'version': '********.0',
    'category': 'Custom',
    'summary': 'Test Syntax Fix',
    'description': """Test Syntax Fix""",
    'author': 'Oneclickvakil',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web', 'mail', 'portal', 'sale', 'website', 'project', 'account', 'payment', 'ai_module_generator', 'ai_chatbot_integration', 'ai_cash_management'],
    'data': ['security/ir.model.access.csv', 'data/data.xml', 'data/cron_jobs.xml', 'reports/legal_services_complete_report.xml', 'views/views.xml', 'views/website_templates.xml', 'views/portal/legal_services_complete_portal.xml', 'views/portal/legal_services_complete_portal_menu.xml'],
    'demo': ['demo/demo.xml'],
    'assets': {
        'web.assets_frontend': [
            'test_test_syntax_fix/static/src/css/enhanced_forms.css',
            'test_test_syntax_fix/static/src/js/enhanced_forms.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
}