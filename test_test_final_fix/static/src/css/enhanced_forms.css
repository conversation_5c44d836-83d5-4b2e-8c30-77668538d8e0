/* Enhanced Forms CSS - Inspired by ai_rti_master design */

/* ===== FORM WRAPPER ===== */
.enhanced-form-wrapper {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* ===== PROGRESS STEPS ===== */
.form-progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin: 0 30px;
}

.step-item:not(:last-child):after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50px;
    width: 60px;
    height: 2px;
    background-color: #ddd;
    z-index: 1;
}

.step-item.active:not(:last-child):after {
    background-color: #007bff;
}

.step-number {
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    background-color: #ddd;
    color: #666;
    text-align: center;
    font-weight: 600;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background-color: #007bff;
    color: white;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
}

.step-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    text-align: center;
}

.step-item.active .step-label {
    color: #007bff;
    font-weight: 600;
}

/* ===== FORM HEADER CARD ===== */
.form-header-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.form-header-content {
    padding: 40px;
    text-align: center;
    background: #ffffff;
    border-bottom: 1px solid #e9ecef;
}

.form-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-description {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.form-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.form-meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
}

.form-meta-item i {
    color: #007bff;
}

/* ===== MAIN FORM CARD ===== */
.main-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.enhanced-form {
    padding: 40px;
}

/* ===== FORM SECTIONS ===== */
.form-section {
    margin-bottom: 30px;
    padding: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.form-section-title {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    padding: 20px 30px;
    background: #007bff;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.form-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255,255,255,0.2);
}

.form-section-title i {
    color: white;
    font-size: 1.3rem;
    opacity: 0.9;
}

/* Form section content padding */
.form-section .row,
.form-section .nice-form-group:not(.step-navigation .nice-form-group),
.form-section .step-navigation,
.form-section .review-summary,
.form-section .form-submit-section {
    margin-left: 30px;
    margin-right: 30px;
}

.form-section .row:first-of-type,
.form-section .nice-form-group:first-of-type:not(.step-navigation .nice-form-group) {
    margin-top: 30px;
}

.form-section .step-navigation:last-of-type,
.form-section .form-submit-section:last-of-type {
    margin-bottom: 30px;
}

/* ===== FORM GROUPS ===== */
.nice-form-group {
    margin-bottom: 25px;
    position: relative;
}

.nice-form-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: block;
    font-size: 0.95rem;
}

.nice-form-group label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: 700;
}

/* ===== FORM CONTROLS ===== */
.form-control,
.nice-form-group input,
.nice-form-group select,
.nice-form-group textarea {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-control:focus,
.nice-form-group input:focus,
.nice-form-group select:focus,
.nice-form-group textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    outline: none;
}

.form-control.is-valid,
.nice-form-group input.is-valid,
.nice-form-group select.is-valid,
.nice-form-group textarea.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.17z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
}

.form-control.is-invalid,
.nice-form-group input.is-invalid,
.nice-form-group select.is-invalid,
.nice-form-group textarea.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
}

/* ===== VALIDATION FEEDBACK ===== */
.invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    font-weight: 500;
}

.was-validated .invalid-feedback,
.is-invalid ~ .invalid-feedback {
    display: block;
}

.valid-feedback {
    display: none;
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 5px;
    font-weight: 500;
}

.was-validated .valid-feedback,
.is-valid ~ .valid-feedback {
    display: block;
}

/* ===== FORM HELP TEXT ===== */
.form-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 5px;
    line-height: 1.4;
}

/* ===== SUBMIT BUTTON ===== */
.form-submit-section {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
}

.btn-submit-enhanced {
    background: #007bff;
    border: none;
    color: white;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-submit-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    background: #0056b3;
}

.btn-submit-enhanced:active {
    transform: translateY(0);
}

/* ===== LOADING STATE ===== */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .enhanced-form-wrapper {
        padding: 10px 0;
    }
    
    .form-progress-steps {
        flex-direction: column;
        gap: 15px;
    }
    
    .step-item {
        margin: 0;
    }
    
    .step-item:not(:last-child):after {
        display: none;
    }
    
    .form-header-content {
        padding: 30px 20px;
    }
    
    .form-title {
        font-size: 2rem;
    }
    
    .enhanced-form {
        padding: 30px 20px;
    }
    
    .form-section {
        padding: 20px;
        margin-bottom: 25px;
    }

    .form-section-title {
        padding: 15px 20px;
        font-size: 1.3rem;
    }

    .form-section .row,
    .form-section .nice-form-group:not(.step-navigation .nice-form-group),
    .form-section .step-navigation,
    .form-section .review-summary,
    .form-section .form-submit-section {
        margin-left: 20px;
        margin-right: 20px;
    }

    .form-meta {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 576px) {
    .form-title {
        font-size: 1.75rem;
    }

    .form-description {
        font-size: 1rem;
    }

    .enhanced-form {
        padding: 20px 15px;
    }
}

/* ===== STEP NAVIGATION ===== */
.step-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #e9ecef;
    margin-top: 30px;
}

.step-navigation .btn {
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.95rem;
}

.step-navigation .btn-next-step {
    background: #007bff;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.step-navigation .btn-next-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.step-navigation .btn-prev-step {
    background: #6c757d;
    color: white;
}

.step-navigation .btn-prev-step:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* ===== REVIEW SECTION ===== */
.review-summary {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 0;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
}

.review-header {
    background: #007bff;
    padding: 25px 30px;
    color: white;
}

.review-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.review-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.review-text h4 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.review-text p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

.review-content {
    padding: 30px;
}

.review-section {
    background: white;
    border-radius: 12px;
    padding: 0;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.review-section-header {
    background: #f8f9fa;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #dee2e6;
}

.review-section-header i {
    color: #007bff;
    font-size: 1.1rem;
}

.review-section-header h6 {
    margin: 0;
    flex: 1;
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.btn-edit-section {
    background: none;
    border: none;
    color: #6c757d;
    padding: 5px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-edit-section:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

.review-section-content {
    padding: 20px;
}

.review-item {
    margin-bottom: 12px;
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.review-item:last-child {
    border-bottom: none;
}

.review-item-label {
    color: #495057;
    font-weight: 600;
    font-size: 0.95rem;
}

.review-item-value {
    color: #2c3e50;
    font-weight: 500;
    text-align: right;
    max-width: 60%;
    word-wrap: break-word;
}

.review-item-empty {
    color: #6c757d;
    font-style: italic;
}

/* ===== REVIEW STATS ===== */
.review-stats {
    padding: 20px 30px 30px;
    background: white;
    margin-top: 20px;
    border-top: 1px solid #dee2e6;
}

.stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 1.2rem;
}

.stat-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* ===== COMPLETED STEPS ===== */
.step-item.completed .step-number {
    background-color: #28a745;
    color: white;
}

.step-item.completed .step-label {
    color: #28a745;
}

/* ===== RELATIONAL FIELDS STYLING ===== */

/* Many2one Fields */
.many2one-field {
    position: relative;
}

.many2one-searchable {
    position: relative;
}

.many2one-searchable input {
    padding-right: 40px;
}

.many2one-searchable .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

.many2one-dropdown-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.many2one-dropdown-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.many2one-dropdown-item:hover {
    background-color: #f8f9fa;
}

.many2one-dropdown-item.selected {
    background-color: #007bff;
    color: white;
}

.many2one-radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.many2one-radio-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.many2one-radio-item:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.many2one-radio-item.selected {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.many2one-radio-item input[type="radio"] {
    margin: 0;
}

/* Many2many Fields */
.many2many-field {
    position: relative;
}

.many2many-tags-container {
    min-height: 45px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: flex-start;
    cursor: text;
    transition: border-color 0.3s ease;
}

.many2many-tags-container:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.many2many-tag {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.many2many-tag .remove-tag {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
}

.many2many-tag .remove-tag:hover {
    opacity: 1;
}

.many2many-input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 120px;
    padding: 4px;
    font-size: 0.95rem;
}

.many2many-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px;
}

.many2many-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.many2many-checkbox-item:hover {
    background-color: #f8f9fa;
}

.many2many-checkbox-item input[type="checkbox"] {
    margin: 0;
}

.many2many-multiselect {
    position: relative;
}

.many2many-multiselect-header {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 10px 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: border-color 0.3s ease;
}

.many2many-multiselect-header:hover {
    border-color: #007bff;
}

.many2many-multiselect-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* One2many Fields */
.one2many-field {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.one2many-list {
    max-height: 400px;
    overflow-y: auto;
}

.one2many-list-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e1e5e9;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.one2many-list-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.one2many-list-item:hover {
    background-color: #f8f9fa;
}

.one2many-list-item:last-child {
    border-bottom: none;
}

.one2many-add-button {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.one2many-add-button:hover {
    background: #218838;
}

.one2many-remove-button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.one2many-remove-button:hover {
    background: #c82333;
}

.one2many-inline-form {
    padding: 15px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
}

.one2many-inline-form .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.one2many-inline-form .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* Loading States */
.field-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.field-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Error States */
.field-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}

.field-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
}
