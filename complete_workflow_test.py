#!/usr/bin/env python3
"""
Complete AI Module Generator Testing Script with Button Methods
This script follows the troubleshooting flowchart and uses proper button methods via XMLRPC
"""

import xmlrpc.client
import os
import shutil
from odoo_config import ODOO_CONFIG

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    common = xmlrpc.client.ServerProxy(f'{ODOO_CONFIG["url"]}/xmlrpc/2/common')
    uid = common.authenticate(ODOO_CONFIG['db'], ODOO_CONFIG['username'], ODOO_CONFIG['password'], {})
    models = xmlrpc.client.ServerProxy(f'{ODOO_CONFIG["url"]}/xmlrpc/2/object')
    return models, uid

def cleanup_existing_module(module_name):
    """Remove existing module files and database records"""
    print(f'🧹 Cleaning up existing module: {module_name}')
    
    # Remove module directory
    module_path = f'/mnt/extra-addons/{module_name}'
    if os.path.exists(module_path):
        shutil.rmtree(module_path)
        print(f'✅ Removed module directory: {module_path}')
    
    # Clean up database records
    models, uid = connect_to_odoo()
    try:
        # Find and delete existing generated module records
        existing_modules = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'generated.module', 'search_read',
            [[('code', '=', module_name)]],
            {'fields': ['id', 'name']}
        )
        
        if existing_modules:
            module_ids = [m['id'] for m in existing_modules]
            models.execute_kw(
                ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                'generated.module', 'unlink', [module_ids]
            )
            print(f'✅ Deleted {len(module_ids)} existing database records')
    except Exception as e:
        print(f'⚠️  Database cleanup warning: {e}')

def generate_module(template_name_filter='legal services'):
    """Generate a new module using AI Module Generator"""
    print('🚀 Starting module generation...')
    
    models, uid = connect_to_odoo()
    
    # Get available templates
    templates = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.template', 'search_read',
        [[]],
        {'fields': ['id', 'name', 'state'], 'limit': 10}
    )
    
    if not templates:
        raise Exception('No templates found')
    
    # Find template
    target_template = None
    for template in templates:
        if template_name_filter.lower() in template['name'].lower():
            target_template = template
            break
    
    if not target_template:
        target_template = templates[0]
    
    print(f'✅ Using template: {target_template["name"]} (ID: {target_template["id"]})')
    
    # Create wizard
    wizard_data = {
        'module_name': 'legal_services_complete',
        'module_title': 'Legal Services Complete',
        'custom_prefix': 'ovakil',
        'template_id': target_template['id'],
    }
    
    wizard_id = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.generator.wizard', 'create', [wizard_data]
    )
    
    print(f'✅ Created wizard record: {wizard_id}')
    
    # Generate module
    result = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.generator.wizard', 'action_generate_module', [wizard_id]
    )
    
    # Get generation log
    wizard_record = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.generator.wizard', 'read', 
        [wizard_id], {'fields': ['generation_log', 'generated_module_path']}
    )
    
    if wizard_record:
        print('📋 Generation completed!')
        print(f'📁 Module path: {wizard_record[0]["generated_module_path"]}')
        return wizard_record[0]["generated_module_path"]
    
    return None

def update_apps_list():
    """Call the Update Apps List button method"""
    print('🔄 Calling Update Apps List button method...')
    
    models, uid = connect_to_odoo()
    
    # This is the exact method called by the "Update Apps List" button
    models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'ir.module.module', 'update_list', []
    )
    
    print('✅ Update Apps List completed successfully')

def install_module(module_name):
    """Call the Install Module button method"""
    print(f'🔧 Installing module: {module_name}')
    
    models, uid = connect_to_odoo()
    
    # Find module
    module_ids = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'ir.module.module', 'search',
        [[('name', '=', module_name)]]
    )
    
    if not module_ids:
        raise Exception(f'Module {module_name} not found')
    
    module_id = module_ids[0]
    
    # Get module state
    module_data = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'ir.module.module', 'read',
        [module_id], {'fields': ['name', 'state', 'summary']}
    )
    
    module = module_data[0]
    print(f'📦 Module: {module["name"]} (State: {module["state"]})')
    
    if module['state'] == 'uninstalled':
        # This is the exact method called by the "Install" button
        models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'ir.module.module', 'button_immediate_install',
            [module_id]
        )
        
        # Check final state
        final_data = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'ir.module.module', 'read',
            [module_id], {'fields': ['state']}
        )
        
        final_state = final_data[0]['state']
        print(f'📊 Installation result: {final_state}')
        
        if final_state == 'installed':
            print('🎉 SUCCESS: Module installed successfully!')
            return True
        else:
            print(f'❌ FAILED: Module state is {final_state}')
            return False
            
    elif module['state'] == 'installed':
        print('ℹ️  Module is already installed')
        return True
    else:
        print(f'⚠️  Module state: {module["state"]}')
        return False

def test_module_functionality(module_name):
    """Test the generated module functionality"""
    print('🧪 Testing module functionality...')

    models, uid = connect_to_odoo()

    # Test main model
    main_model = f'{module_name}.legal_services_complete'

    try:
        # Create test record
        test_data = {
            'name': 'Test Legal Service Application',
            'client_name': 'John Doe',
            'client_email': '<EMAIL>',
            'client_phone': '+1234567890',
            'service_type': 'business_registration',
            'urgency_level': 'standard',
            'case_details': 'Test case for business registration services',
        }

        record_id = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            main_model, 'create', [test_data]
        )

        print(f'✅ Main model test passed (Record ID: {record_id})')

        # Test related models
        related_models = [
            f'{module_name}.legal.document.line',
            f'{module_name}.consultation.note',
            f'{module_name}.payment.transaction',
            f'{module_name}.case.update'
        ]

        for model_name in related_models:
            try:
                count = models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    model_name, 'search_count', [[]]
                )
                print(f'✅ {model_name}: accessible ({count} records)')
            except Exception as e:
                print(f'❌ {model_name}: error - {e}')
                return False

        # Test website form template validation
        print('🌐 Testing website form template...')
        import os
        template_path = f'/mnt/extra-addons/{module_name}/views/website_templates.xml'
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                template_content = f.read()

            # Check for form fields
            required_fields = ['client_name', 'client_email', 'client_phone', 'service_type', 'urgency_level', 'case_details']
            missing_fields = []

            for field in required_fields:
                if f'name="{field}"' not in template_content:
                    missing_fields.append(field)

            if missing_fields:
                print(f'❌ Missing form fields: {missing_fields}')
                return False
            else:
                print(f'✅ All {len(required_fields)} form fields found in website template')

            # Check for form structure
            form_elements = ['<form', 'form-section', 'btn-submit-enhanced']
            for element in form_elements:
                if element not in template_content:
                    print(f'❌ Missing form element: {element}')
                    return False

            print('✅ Website form structure validated')
        else:
            print(f'❌ Website template file not found: {template_path}')
            return False

        print('🎉 All functionality tests passed!')
        return True

    except Exception as e:
        print(f'❌ Functionality test failed: {e}')
        return False

def main():
    """Main testing workflow following the troubleshooting flowchart"""
    print('🚀 AI Module Generator Complete Testing Workflow')
    print('=' * 60)
    
    module_name = 'ovakil_legal_services_complete'
    
    try:
        # Step 1: Cleanup
        cleanup_existing_module(module_name)
        
        # Step 2: Generate module
        module_path = generate_module()
        if not module_path:
            raise Exception('Module generation failed')
        
        # Step 3: Update Apps List (using button method)
        update_apps_list()
        
        # Step 4: Install Module (using button method)
        if not install_module(module_name):
            raise Exception('Module installation failed')
        
        # Step 5: Test functionality
        if not test_module_functionality(module_name):
            raise Exception('Module functionality test failed')
        
        print('\n🎉 SUCCESS: Complete workflow passed!')
        print('✅ Module generation working perfectly')
        print('✅ Installation process working perfectly')
        print('✅ All functionality working perfectly')
        
    except Exception as e:
        print(f'\n❌ WORKFLOW FAILED: {e}')
        return False
    
    return True

if __name__ == '__main__':
    main()
