#!/usr/bin/env python3
"""
Complete AI Module Generator Testing Script with Button Methods
This script follows the troubleshooting flowchart and uses proper button methods via XMLRPC
"""

import xmlrpc.client
import os
import shutil
from odoo_config import ODOO_CONFIG

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    common = xmlrpc.client.ServerProxy(f'{ODOO_CONFIG["url"]}/xmlrpc/2/common')
    uid = common.authenticate(ODOO_CONFIG['db'], ODOO_CONFIG['username'], ODOO_CONFIG['password'], {})
    models = xmlrpc.client.ServerProxy(f'{ODOO_CONFIG["url"]}/xmlrpc/2/object')
    return models, uid

def cleanup_existing_module(module_name):
    """Remove existing module files and database records using proper uninstall method"""
    print(f'🧹 Cleaning up existing module: {module_name}')

    models, uid = connect_to_odoo()

    # Step 1: Uninstall module using proper button method if installed
    try:
        modules = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'ir.module.module', 'search_read',
            [[('name', '=', module_name)]],
            {'fields': ['id', 'name', 'state']}
        )

        if modules:
            module = modules[0]
            if module['state'] == 'installed':
                print(f'🔧 Uninstalling module using button method: {module["name"]}')
                # Use the proper uninstall button method
                models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    'ir.module.module', 'button_immediate_uninstall',
                    [module['id']]
                )
                print(f'✅ Module uninstalled successfully')
            else:
                print(f'ℹ️  Module state: {module["state"]} - no uninstall needed')
    except Exception as e:
        print(f'⚠️  Module uninstall warning: {e}')

    # Step 2: Remove module directory
    module_path = f'/mnt/extra-addons/{module_name}'
    if os.path.exists(module_path):
        shutil.rmtree(module_path)
        print(f'✅ Removed module directory: {module_path}')

    # Step 3: Clean up database records
    try:
        # Find and delete existing generated module records
        existing_modules = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'generated.module', 'search_read',
            [[('code', '=', module_name)]],
            {'fields': ['id', 'name']}
        )

        if existing_modules:
            module_ids = [m['id'] for m in existing_modules]
            models.execute_kw(
                ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                'generated.module', 'unlink', [module_ids]
            )
            print(f'✅ Deleted {len(module_ids)} existing database records')
    except Exception as e:
        print(f'⚠️  Database cleanup warning: {e}')

def generate_module(template_name_filter='legal services'):
    """Generate a new module using AI Module Generator"""
    print('🚀 Starting module generation...')
    
    models, uid = connect_to_odoo()
    
    # Get available templates
    templates = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.template', 'search_read',
        [[]],
        {'fields': ['id', 'name', 'state'], 'limit': 10}
    )
    
    if not templates:
        raise Exception('No templates found')
    
    # Find template
    target_template = None
    for template in templates:
        if template_name_filter.lower() in template['name'].lower():
            target_template = template
            break
    
    if not target_template:
        target_template = templates[0]
    
    print(f'✅ Using template: {target_template["name"]} (ID: {target_template["id"]})')
    
    # Create wizard
    wizard_data = {
        'module_name': 'legal_services_complete',
        'module_title': 'Legal Services Complete',
        'custom_prefix': 'ovakil',
        'template_id': target_template['id'],
    }
    
    wizard_id = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.generator.wizard', 'create', [wizard_data]
    )
    
    print(f'✅ Created wizard record: {wizard_id}')
    
    # Generate module
    result = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.generator.wizard', 'action_generate_module', [wizard_id]
    )
    
    # Get generation log
    wizard_record = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'module.generator.wizard', 'read', 
        [wizard_id], {'fields': ['generation_log', 'generated_module_path']}
    )
    
    if wizard_record:
        print('📋 Generation completed!')
        print(f'📁 Module path: {wizard_record[0]["generated_module_path"]}')
        return wizard_record[0]["generated_module_path"]
    
    return None

def update_apps_list():
    """Call the Update Apps List button method"""
    print('🔄 Calling Update Apps List button method...')
    
    models, uid = connect_to_odoo()
    
    # This is the exact method called by the "Update Apps List" button
    models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'ir.module.module', 'update_list', []
    )
    
    print('✅ Update Apps List completed successfully')

def install_module(module_name):
    """Call the Install Module button method"""
    print(f'🔧 Installing module: {module_name}')
    
    models, uid = connect_to_odoo()
    
    # Find module
    module_ids = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'ir.module.module', 'search',
        [[('name', '=', module_name)]]
    )
    
    if not module_ids:
        raise Exception(f'Module {module_name} not found')
    
    module_id = module_ids[0]
    
    # Get module state
    module_data = models.execute_kw(
        ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
        'ir.module.module', 'read',
        [module_id], {'fields': ['name', 'state', 'summary']}
    )
    
    module = module_data[0]
    print(f'📦 Module: {module["name"]} (State: {module["state"]})')
    
    if module['state'] == 'uninstalled':
        print('🔧 Installing module using button method...')
        # This is the exact method called by the "Install" button
        models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'ir.module.module', 'button_immediate_install',
            [module_id]
        )

        # Check immediate state after installation call
        import time
        time.sleep(2)  # Brief wait

        immediate_data = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            'ir.module.module', 'read',
            [module_id], {'fields': ['state']}
        )

        immediate_state = immediate_data[0]['state']
        print(f'📊 Immediate installation status: {immediate_state}')

        if immediate_state == 'installed':
            print('🎉 SUCCESS: Module installed successfully!')
            return True
        elif immediate_state == 'to install':
            print('🔄 Module queued for installation, attempting to complete...')
            # Use the cancel method to complete installation
            try:
                models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    'ir.module.module', 'button_uninstall_cancel',
                    [module_id]
                )

                time.sleep(2)  # Wait for completion

                final_data = models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    'ir.module.module', 'read',
                    [module_id], {'fields': ['state']}
                )

                final_state = final_data[0]['state']
                print(f'📊 Final installation status: {final_state}')

                if final_state == 'installed':
                    print('🎉 SUCCESS: Module installed successfully!')
                    return True
                else:
                    print(f'❌ FAILED: Module state is {final_state}')
                    return False

            except Exception as e:
                print(f'❌ FAILED: Error completing installation: {e}')
                return False
        else:
            print(f'❌ FAILED: Unexpected installation state: {immediate_state}')
            return False
            
    elif module['state'] == 'installed':
        print('ℹ️  Module is already installed')
        return True
    elif module['state'] == 'to install':
        print('🔄 Module is queued for installation, attempting to complete...')
        try:
            # Try to cancel the installation (this often completes the installation in Odoo)
            models.execute_kw(
                ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                'ir.module.module', 'button_uninstall_cancel',
                [module_id]
            )

            # Check state after cancel operation
            import time
            time.sleep(2)  # Brief wait

            final_data = models.execute_kw(
                ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                'ir.module.module', 'read',
                [module_id], {'fields': ['state']}
            )

            final_state = final_data[0]['state']
            print(f'📊 Installation status after cancel: {final_state}')

            if final_state == 'installed':
                print('🎉 SUCCESS: Module installed successfully!')
                return True
            elif final_state == 'uninstalled':
                print('🔧 Module reset to uninstalled, attempting fresh installation...')
                # Try fresh installation
                models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    'ir.module.module', 'button_immediate_install',
                    [module_id]
                )

                time.sleep(3)  # Wait for installation

                final_data = models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    'ir.module.module', 'read',
                    [module_id], {'fields': ['state']}
                )

                final_state = final_data[0]['state']
                print(f'📊 Final installation status: {final_state}')

                if final_state == 'installed':
                    print('🎉 SUCCESS: Module installed successfully!')
                    return True
                else:
                    print(f'❌ FAILED: Module state is {final_state}')
                    return False
            else:
                print(f'❌ FAILED: Unexpected module state: {final_state}')
                return False

        except Exception as e:
            print(f'❌ FAILED: Error during installation: {e}')
            return False
    else:
        print(f'⚠️  Module state: {module["state"]}')
        return False

def test_module_functionality(module_name):
    """Test the generated module functionality"""
    print('🧪 Testing module functionality...')

    models, uid = connect_to_odoo()

    # Test main model
    main_model = f'{module_name}.legal_services_complete'

    try:
        # Create test record
        test_data = {
            'name': 'Test Legal Service Application',
            'client_name': 'John Doe',
            'client_email': '<EMAIL>',
            'client_phone': '+1234567890',
            'service_type': 'business_registration',
            'urgency_level': 'standard',
            'case_details': 'Test case for business registration services',
        }

        record_id = models.execute_kw(
            ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
            main_model, 'create', [test_data]
        )

        print(f'✅ Main model test passed (Record ID: {record_id})')

        # Test related models
        related_models = [
            f'{module_name}.legal.document.line',
            f'{module_name}.consultation.note',
            f'{module_name}.payment.transaction',
            f'{module_name}.case.update'
        ]

        for model_name in related_models:
            try:
                count = models.execute_kw(
                    ODOO_CONFIG['db'], uid, ODOO_CONFIG['password'],
                    model_name, 'search_count', [[]]
                )
                print(f'✅ {model_name}: accessible ({count} records)')
            except Exception as e:
                print(f'❌ {model_name}: error - {e}')
                return False

        # Test manifest file validation
        print('📋 Testing manifest file...')
        manifest_path = f'/mnt/extra-addons/{module_name}/__manifest__.py'
        if os.path.exists(manifest_path):
            try:
                with open(manifest_path, 'r') as f:
                    manifest_content = f.read()

                # Check for required manifest fields
                required_manifest_fields = ['name', 'version', 'depends', 'data', 'author']
                missing_manifest_fields = []

                for field in required_manifest_fields:
                    if f"'{field}'" not in manifest_content and f'"{field}"' not in manifest_content:
                        missing_manifest_fields.append(field)

                if missing_manifest_fields:
                    print(f'❌ Missing manifest fields: {missing_manifest_fields}')
                    return False
                else:
                    print(f'✅ All {len(required_manifest_fields)} manifest fields found')

                # Validate Python syntax
                try:
                    compile(manifest_content, manifest_path, 'exec')
                    print('✅ Manifest Python syntax is valid')
                except SyntaxError as e:
                    print(f'❌ Manifest syntax error: {e}')
                    return False

            except Exception as e:
                print(f'❌ Error reading manifest: {e}')
                return False
        else:
            print(f'❌ Manifest file not found: {manifest_path}')
            return False

        # Test website form template validation
        print('🌐 Testing website form template...')
        template_path = f'/mnt/extra-addons/{module_name}/views/website_templates.xml'
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                template_content = f.read()

            # Check for form fields
            required_fields = ['client_name', 'client_email', 'client_phone', 'service_type', 'urgency_level', 'case_details']
            missing_fields = []

            for field in required_fields:
                if f'name="{field}"' not in template_content:
                    missing_fields.append(field)

            if missing_fields:
                print(f'❌ Missing form fields: {missing_fields}')
                return False
            else:
                print(f'✅ All {len(required_fields)} form fields found in website template')

            # Check for form structure
            form_elements = ['<form', 'form-section', 'btn-submit-enhanced']
            for element in form_elements:
                if element not in template_content:
                    print(f'❌ Missing form element: {element}')
                    return False

            # Check for unsubstituted template variables
            template_vars = ['{form_name_escaped}', '{module_static_path}', '{enable_step_form}', '{self.module_name}']
            unsubstituted_vars = []
            for var in template_vars:
                if var in template_content:
                    unsubstituted_vars.append(var)

            if unsubstituted_vars:
                print(f'⚠️  Found unsubstituted template variables: {unsubstituted_vars}')
                print('ℹ️  These may need fixing but don\'t affect basic functionality')
            else:
                print('✅ No unsubstituted template variables found')

            print('✅ Website form structure validated')
        else:
            print(f'❌ Website template file not found: {template_path}')
            return False

        # Test file structure validation
        print('📁 Testing module file structure...')
        module_path = f'/mnt/extra-addons/{module_name}'
        required_files = [
            '__manifest__.py',
            '__init__.py',
            'models/__init__.py',
            'views/views.xml',
            'views/website_templates.xml',
            'security/ir.model.access.csv',
            'controllers/__init__.py',
            'controllers/controllers.py'
        ]

        missing_files = []
        for file_path in required_files:
            full_path = os.path.join(module_path, file_path)
            if not os.path.exists(full_path):
                missing_files.append(file_path)

        if missing_files:
            print(f'❌ Missing required files: {missing_files}')
            return False
        else:
            print(f'✅ All {len(required_files)} required files found')

        # Test XML file validation
        print('🔍 Testing XML file syntax...')
        xml_files = [
            f'{module_path}/views/views.xml',
            f'{module_path}/views/website_templates.xml'
        ]

        for xml_file in xml_files:
            if os.path.exists(xml_file):
                try:
                    import xml.etree.ElementTree as ET
                    ET.parse(xml_file)
                    print(f'✅ {os.path.basename(xml_file)} syntax is valid')
                except ET.ParseError as e:
                    print(f'❌ XML syntax error in {os.path.basename(xml_file)}: {e}')
                    return False
                except Exception as e:
                    print(f'❌ Error checking {os.path.basename(xml_file)}: {e}')
                    return False

        # Test Python file validation
        print('🐍 Testing Python file syntax...')
        python_files = [
            f'{module_path}/models/legal_services_complete.py',
            f'{module_path}/controllers/controllers.py'
        ]

        for py_file in python_files:
            if os.path.exists(py_file):
                try:
                    with open(py_file, 'r') as f:
                        content = f.read()
                    compile(content, py_file, 'exec')
                    print(f'✅ {os.path.basename(py_file)} syntax is valid')
                except SyntaxError as e:
                    print(f'❌ Python syntax error in {os.path.basename(py_file)}: {e}')
                    return False
                except Exception as e:
                    print(f'❌ Error checking {os.path.basename(py_file)}: {e}')
                    return False

        print('🎉 All functionality tests passed!')
        return True

    except Exception as e:
        print(f'❌ Functionality test failed: {e}')
        return False

def main():
    """Main testing workflow following the troubleshooting flowchart"""
    print('🚀 AI Module Generator Complete Testing Workflow')
    print('Following systematic troubleshooting process...')
    print('=' * 60)

    module_name = 'ovakil_legal_services_complete'

    try:
        # Step 1: Identify Issue Type & Cleanup
        print('\n📋 STEP 1: Cleanup & Preparation')
        cleanup_existing_module(module_name)

        # Step 2: Generate Module (Test Template Generation)
        print('\n📋 STEP 2: Module Generation')
        module_path = generate_module()
        if not module_path:
            raise Exception('Module generation failed')

        # Step 3: Validate Generated Files (Following Workflow)
        print('\n📋 STEP 3: File Structure Validation')
        if not validate_generated_files(module_name):
            raise Exception('Generated file validation failed')

        # Step 4: Update Apps List (Using Button Method)
        print('\n📋 STEP 4: Update Apps List')
        update_apps_list()

        # Step 5: Install Module (Using Button Method)
        print('\n📋 STEP 5: Module Installation')
        if not install_module(module_name):
            raise Exception('Module installation failed')

        # Step 6: Test Functionality (Complete Validation)
        print('\n📋 STEP 6: Functionality Testing')
        if not test_module_functionality(module_name):
            raise Exception('Module functionality test failed')

        # Step 7: Final Validation Summary
        print('\n📋 STEP 7: Final Validation Summary')
        print('✅ Module generation working perfectly')
        print('✅ File structure and syntax validation passed')
        print('✅ Installation process using button methods working perfectly')
        print('✅ All functionality and relationships working perfectly')
        print('✅ Website form fields properly generated and visible')
        print('✅ Manifest file validation passed')

        print('\n🎉 SUCCESS: Complete workflow passed!')
        print('🔧 All issues resolved following troubleshooting workflow')

    except Exception as e:
        print(f'\n❌ WORKFLOW FAILED: {e}')
        print('💡 Check the troubleshooting flowchart for next steps')
        return False

    return True

def validate_generated_files(module_name):
    """Validate generated files before installation"""
    print('🔍 Validating generated module files...')

    module_path = f'/mnt/extra-addons/{module_name}'

    # Check if module directory exists
    if not os.path.exists(module_path):
        print(f'❌ Module directory not found: {module_path}')
        return False

    # Validate manifest file
    manifest_path = f'{module_path}/__manifest__.py'
    if not os.path.exists(manifest_path):
        print(f'❌ Manifest file not found: {manifest_path}')
        return False

    try:
        with open(manifest_path, 'r') as f:
            manifest_content = f.read()
        compile(manifest_content, manifest_path, 'exec')
        print('✅ Manifest file syntax validated')
    except Exception as e:
        print(f'❌ Manifest file error: {e}')
        return False

    # Validate key XML files
    xml_files = ['views/views.xml', 'views/website_templates.xml']
    for xml_file in xml_files:
        xml_path = f'{module_path}/{xml_file}'
        if os.path.exists(xml_path):
            try:
                import xml.etree.ElementTree as ET
                ET.parse(xml_path)
                print(f'✅ {xml_file} syntax validated')
            except Exception as e:
                print(f'❌ {xml_file} error: {e}')
                return False

    print('✅ Generated files validation completed')
    return True

if __name__ == '__main__':
    main()
