2025-06-13 14:26:37,717 - INFO - Connecting to Odoo server: https://oneclickvakil.com
2025-06-13 14:26:37,790 - INFO - Connected to Odoo 17.0-20240612
2025-06-13 14:26:38,354 - <PERSON>FO - Authenticated successfully. User ID: 2
2025-06-13 14:26:38,354 - INFO - Upgrading module: ovakil_customer_feedback
2025-06-13 14:26:38,912 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 147, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 127, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 391, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 37, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 59, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 65, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 50, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 133, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 464, in call_kw\n    result = _call_kw_model(method, model, args, kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 435, in _call_kw_model\n    result = method(recs, *args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5738, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1640, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3861, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-06-13 14:26:38,914 - ERROR - Error getting module info for 'ovakil_customer_feedback': <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 147, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 127, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 391, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 37, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 59, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 65, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 50, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 133, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 464, in call_kw\n    result = _call_kw_model(method, model, args, kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 435, in _call_kw_model\n    result = method(recs, *args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5738, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1640, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3861, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-06-13 14:26:38,914 - ERROR - Module 'ovakil_customer_feedback' not found
2025-06-18 11:38:32,718 - INFO - Connecting to Odoo server: https://oneclickvakil.com
2025-06-18 11:38:32,777 - INFO - Connected to Odoo 17.0-20240612
2025-06-18 11:38:33,295 - INFO - Authenticated successfully. User ID: 2
2025-06-18 11:38:33,351 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 147, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 127, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 391, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 37, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 59, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 65, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 50, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 133, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 464, in call_kw\n    result = _call_kw_model(method, model, args, kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 435, in _call_kw_model\n    result = method(recs, *args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5738, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1640, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3861, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-06-18 11:38:33,351 - ERROR - Error getting module info for 'ai_module_generator': <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 147, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 127, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 391, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 37, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 59, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 65, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 50, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 133, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 464, in call_kw\n    result = _call_kw_model(method, model, args, kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 435, in _call_kw_model\n    result = method(recs, *args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5738, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1640, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3861, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
