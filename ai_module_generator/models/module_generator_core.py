from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import os
import shutil
import tempfile
import json
import logging
from datetime import datetime
from jinja2 import Template

_logger = logging.getLogger(__name__)


class ModuleGeneratorCore(models.AbstractModel):
    """
    Core module generation engine that handles the creation of complete
    Odoo modules based on templates and configurations.
    """
    _name = 'module.generator.core'
    _description = 'Module Generator Core Engine'

    def generate_module(self, template_id, module_config):
        """
        Main method to generate a complete Odoo module

        Args:
            template_id: ID of the module template
            module_config: Dictionary containing module configuration

        Returns:
            Dictionary with generation results
        """
        template = self.env['module.template'].browse(template_id)
        if not template.exists():
            raise ValidationError(_("Template not found"))

        if template.state != 'active':
            raise ValidationError(_("Template must be active to generate modules"))

        try:
            # Create temporary directory for module generation
            temp_dir = tempfile.mkdtemp(prefix='odoo_module_gen_')

            # Generate module structure
            module_path = self._create_module_structure(template, module_config, temp_dir)

            # Generate all module components
            self._generate_manifest(template, module_config, module_path)
            self._generate_init_files(template, module_config, module_path)
            self._generate_models(template, module_config, module_path)
            self._generate_views(template, module_config, module_path)
            self._generate_controllers(template, module_config, module_path)
            self._generate_security(template, module_config, module_path)
            self._generate_data_files(template, module_config, module_path)
            self._generate_website_templates(template, module_config, module_path)
            self._generate_portal_templates(template, module_config, module_path)
            self._generate_api_endpoints(template, module_config, module_path)

            # Create generated module record
            generated_module = self._create_generated_module_record(template, module_config, module_path)

            # Copy module to addons directory (if specified)
            if module_config.get('install_immediately'):
                self._install_module(module_path, module_config)

            return {
                'success': True,
                'module_path': module_path,
                'generated_module_id': generated_module.id,
                'message': _('Module generated successfully'),
            }

        except Exception as e:
            _logger.error("Error generating module: %s", str(e))
            # Clean up temporary directory
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
            raise UserError(_("Module generation failed: %s") % str(e))

    def _create_module_structure(self, template, module_config, temp_dir):
        """Create the basic module directory structure"""
        module_name = module_config.get('module_name') or template.get_module_name()
        module_path = os.path.join(temp_dir, module_name)

        # Create main directories
        directories = [
            '',
            'models',
            'views',
            'controllers',
            'security',
            'data',
            'static/src/css',
            'static/src/js',
            'static/src/xml',
            'static/description',
            'wizards',
            'reports',
            'demo',
        ]

        for directory in directories:
            dir_path = os.path.join(module_path, directory)
            os.makedirs(dir_path, exist_ok=True)

        return module_path

    def _generate_manifest(self, template, module_config, module_path):
        """Generate the __manifest__.py file"""
        module_name = module_config.get('module_name') or template.get_module_name()

        manifest_template = """
{
    'name': '{{ module_title }}',
    'version': '{{ version }}',
    'category': '{{ category }}',
    'summary': '{{ summary }}',
    'description': '''{{ description }}''',
    'author': '{{ author }}',
    'website': '{{ website }}',
    'depends': {{ depends }},
    'data': [
        # Security
        'security/{{ module_name }}_security.xml',
        'security/ir.model.access.csv',

        # Data
        'data/{{ module_name }}_sequence.xml',
        'data/{{ module_name }}_data.xml',

        # Views
        {% for form in forms %}
        'views/{{ form.code }}_views.xml',
        {% endfor %}
        'views/{{ module_name }}_menus.xml',

        # Website Templates
        'views/website_templates.xml',
        'views/portal_templates.xml',

        # Reports
        'reports/{{ module_name }}_reports.xml',
    ],
    'assets': {
        'web.assets_backend': [
            '{{ module_name }}/static/src/css/{{ module_name }}.css',
            '{{ module_name }}/static/src/js/{{ module_name }}.js',
        ],
        'website.assets_frontend': [
            '{{ module_name }}/static/src/css/website_{{ module_name }}.css',
            '{{ module_name }}/static/src/js/website_{{ module_name }}.js',
        ],
    },
    'demo': [
        'demo/{{ module_name }}_demo.xml',
    ],
    'installable': True,
    'application': {{ is_application }},
    'auto_install': False,
    'license': 'LGPL-3',
}
"""

        template_obj = Template(manifest_template)
        manifest_content = template_obj.render(
            module_name=module_name,
            module_title=module_config.get('title', template.name),
            version=template.module_version,
            category=template.module_category,
            summary=module_config.get('summary', template.description),
            description=module_config.get('description', template.description),
            author=template.module_author,
            website=template.module_website,
            depends=self._get_module_dependencies(template),
            forms=template.form_builder_ids,
            is_application='True' if module_config.get('is_application', True) else 'False',
        )

        manifest_path = os.path.join(module_path, '__manifest__.py')
        with open(manifest_path, 'w', encoding='utf-8') as f:
            f.write(manifest_content)

    def _generate_init_files(self, template, module_config, module_path):
        """Generate __init__.py files"""
        module_name = module_config.get('module_name') or template.get_module_name()

        # Main __init__.py
        main_init = """from . import models
from . import controllers
from . import wizards
"""
        with open(os.path.join(module_path, '__init__.py'), 'w') as f:
            f.write(main_init)

        # Models __init__.py
        models_init = ""
        for form in template.form_builder_ids:
            models_init += f"from . import {form.code}\n"

        with open(os.path.join(module_path, 'models', '__init__.py'), 'w') as f:
            f.write(models_init)

        # Controllers __init__.py
        controllers_init = f"from . import {module_name}_controllers\nfrom . import api_controllers\n"
        with open(os.path.join(module_path, 'controllers', '__init__.py'), 'w') as f:
            f.write(controllers_init)

    def _generate_models(self, template, module_config, module_path):
        """Generate model files for each form"""
        for form in template.form_builder_ids:
            self._generate_single_model(template, form, module_config, module_path)

    def _generate_single_model(self, template, form, module_config, module_path):
        """Generate a single model file"""
        model_template = """
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class {{ model_class }}(models.Model):
    \"\"\"{{ model_description }}\"\"\"
    _name = '{{ model_name }}'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']
    _description = '{{ model_description }}'
    _order = 'create_date desc'

    # Basic Information
    name = fields.Char(string='Reference', required=True, copy=False, readonly=True,
                      default=lambda self: _('New'))

    {% for field in fields %}
    {% if field.field_type == 'one2many' %}
    {{ field.name }} = fields.One2many(
        '{{ field.relation_model }}',
        '{{ field.relation_field or (form.model_name.replace(".", "_") + "_id") }}',
        string='{{ field.field_description }}',
        {% if field.required %}required=True,{% endif %}
        {% if field.readonly %}readonly=True,{% endif %}
        {% if field.help_text %}help='{{ field.help_text }}',{% endif %}
        {% if field.domain %}domain={{ field.domain }},{% endif %}
        tracking=True
    )
    {% elif field.field_type == 'many2one' %}
    {{ field.name }} = fields.Many2one(
        '{{ field.relation_model }}',
        string='{{ field.field_description }}',
        {% if field.required %}required=True,{% endif %}
        {% if field.readonly %}readonly=True,{% endif %}
        {% if field.help_text %}help='{{ field.help_text }}',{% endif %}
        {% if field.default_value %}default='{{ field.default_value }}',{% endif %}
        {% if field.domain %}domain={{ field.domain }},{% endif %}
        tracking=True
    )
    {% elif field.field_type == 'many2many' %}
    {{ field.name }} = fields.Many2many(
        '{{ field.relation_model }}',
        string='{{ field.field_description }}',
        {% if field.required %}required=True,{% endif %}
        {% if field.readonly %}readonly=True,{% endif %}
        {% if field.help_text %}help='{{ field.help_text }}',{% endif %}
        {% if field.domain %}domain={{ field.domain }},{% endif %}
        tracking=True
    )
    {% else %}
    {{ field.name }} = fields.{{ field.field_type|title }}(
        string='{{ field.field_description }}',
        {% if field.required %}required=True,{% endif %}
        {% if field.readonly %}readonly=True,{% endif %}
        {% if field.help_text %}help='{{ field.help_text }}',{% endif %}
        {% if field.default_value %}default='{{ field.default_value }}',{% endif %}
        {% if field.field_type == 'selection' and field.selection_options %}
        selection={{ field.selection_options }},
        {% endif %}
        tracking=True
    )
    {% endif %}
    {% endfor %}

    # Workflow States
    state = fields.Selection([
        {% for state in workflow_states %}
        ('{{ state.code }}', '{{ state.name }}'),
        {% endfor %}
    ], string='Status', default='{{ default_state }}', tracking=True)

    # System Fields
    user_id = fields.Many2one('res.users', string='Responsible User',
                             default=lambda self: self.env.user, tracking=True)
    partner_id = fields.Many2one('res.partner', string='Customer', tracking=True)
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company)

    # Sales Order Integration
    {% if form.create_sales_order %}
    sale_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True, tracking=True)
    sale_order_state = fields.Selection(related='sale_order_id.state', string='Order Status', readonly=True)
    invoice_id = fields.Many2one('account.move', string='Invoice', readonly=True, tracking=True)
    invoice_status = fields.Selection(related='sale_order_id.invoice_status', string='Invoice Status', readonly=True)
    {% endif %}

    # Payment Fields
    {% if form.enable_payment %}
    payment_amount = fields.Float(string='Payment Amount', default={{ form.product_price or 0.0 }})
    payment_status = fields.Selection([
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ], string='Payment Status', default='pending', tracking=True)
    payment_transaction_id = fields.Many2one('payment.transaction', string='Payment Transaction')
    payment_required = fields.Boolean(string='Payment Required', default={{ 'True' if form.payment_required else 'False' }})
    {% endif %}

    # Document Generation Fields
    {% if form.enable_document_generation %}
    document_generated = fields.Boolean(string='Document Generated', default=False)
    document_content = fields.Html(string='Document Content')
    document_pdf = fields.Binary(string='Document PDF')
    document_pdf_filename = fields.Char(string='PDF Filename')
    {% endif %}

    # ✨ AI Response Fields (Always included)
    ai_response = fields.Html(string='AI Response', help='AI-generated response based on record data')
    ai_response_generated = fields.Boolean(string='AI Response Generated', default=False)
    ai_response_pdf = fields.Binary(string='AI Response PDF')
    ai_response_pdf_filename = fields.Char(string='AI Response PDF Filename')
    ai_last_prompt = fields.Text(string='Last AI Prompt', help='Last prompt sent to AI')

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', _('New')) == _('New'):
                vals['name'] = self.env['ir.sequence'].next_by_code('{{ model_name }}') or _('New')
        records = super().create(vals_list)

        # Create sales order for each record if enabled
        {% if form.create_sales_order %}
        for record in records:
            record._create_sales_order()
        {% endif %}

        return records

    def _compute_access_url(self):
        super()._compute_access_url()
        for record in self:
            record.access_url = '/my/{{ form.code }}/%s' % record.id

    {% for state in workflow_states %}
    {% if state.code != 'draft' %}
    def action_{{ state.code }}(self):
        \"\"\"Move to {{ state.name }} state\"\"\"
        self.ensure_one()
        self.write({'state': '{{ state.code }}'})
        self.message_post(body=_('Status changed to {{ state.name }}'))
    {% endif %}
    {% endfor %}

    {% if form.enable_payment %}
    def action_create_payment(self):
        \"\"\"Create payment transaction\"\"\"
        self.ensure_one()
        # Payment creation logic will be implemented here
        pass
    {% endif %}

    {% if form.enable_document_generation %}
    def action_generate_document(self):
        \"\"\"Generate document using AI\"\"\"
        self.ensure_one()
        # Document generation logic will be implemented here
        pass
    {% endif %}

    {% if form.create_sales_order %}
    def _create_sales_order(self):
        \"\"\"Create sales order for this form submission\"\"\"
        self.ensure_one()

        if self.sale_order_id:
            return self.sale_order_id

        # Ensure customer exists
        if not self.partner_id:
            raise UserError(_('Customer information is required to create sales order'))

        # Check if customer has complete billing information
        if not self._check_customer_billing_info():
            return self._request_customer_billing_info()

        # Create sales order
        sale_order_vals = {
            'partner_id': self.partner_id.id,
            'partner_invoice_id': self.partner_id.id,
            'partner_shipping_id': self.partner_id.id,
            'origin': self.name,
            'client_order_ref': self.name,
            'state': 'draft',
            'order_line': [(0, 0, {
                'product_id': {{ form.product_id.id if form.product_id else 'False' }},
                'name': '{{ form.name }} - ' + self.name,
                'product_uom_qty': 1,
                'price_unit': {{ form.product_price or 0.0 }},
            })],
        }

        sale_order = self.env['sale.order'].create(sale_order_vals)
        self.sale_order_id = sale_order.id

        # Confirm the sales order if payment is not required
        {% if not form.payment_required %}
        sale_order.action_confirm()
        {% endif %}

        return sale_order

    def _check_customer_billing_info(self):
        \"\"\"Check if customer has complete billing information\"\"\"
        self.ensure_one()

        if not self.partner_id:
            return False

        required_fields = ['name', 'email', 'phone', 'street', 'city', 'country_id']
        for field in required_fields:
            if not getattr(self.partner_id, field, None):
                return False

        return True

    def _request_customer_billing_info(self):
        \"\"\"Request customer to complete billing information\"\"\"
        self.ensure_one()

        # This will redirect to customer portal to complete billing info
        return {
            'type': 'ir.actions.act_url',
            'url': f'/my/account?form_submission_id={self.id}',
            'target': 'self',
        }

    def action_create_payment_link(self):
        \"\"\"Create payment link for the sales order\"\"\"
        self.ensure_one()

        if not self.sale_order_id:
            self._create_sales_order()

        if not self.sale_order_id:
            raise UserError(_('Sales order must be created before generating payment link'))

        # Create payment link
        payment_link = self.env['payment.link.wizard'].create({
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'amount': self.sale_order_id.amount_total,
            'currency_id': self.sale_order_id.currency_id.id,
            'partner_id': self.partner_id.id,
            'description': f'Payment for {self.name}',
        })

        return payment_link.action_generate_link()

    def action_view_sales_order(self):
        \"\"\"View the related sales order\"\"\"
        self.ensure_one()

        if not self.sale_order_id:
            raise UserError(_('No sales order found for this submission'))

        return {
            'name': _('Sales Order'),
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
    {% endif %}

    # ✨ AI Response Methods
    def action_get_ai_response(self):
        \"\"\"Get AI response for this record\"\"\"
        self.ensure_one()

        # Get AI prompt configuration
        ai_prompt_configs = self.env['ai.prompt.config'].search([
            ('form_builder_id.code', '=', '{{ form.code }}'),
            ('prompt_type', '=', 'system_prompt'),
            ('active', '=', True)
        ], limit=1)

        if not ai_prompt_configs:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'No AI Configuration',
                    'message': 'No AI prompt configuration found for this form.',
                    'type': 'warning',
                }}
            }}

        ai_config = ai_prompt_configs[0]

        try:
            # Prepare record data for AI prompt
            record_data = self._prepare_ai_data()

            # Execute AI prompt
            ai_response = ai_config.execute_prompt(record_data)

            # Store AI response
            self.write({{
                'ai_response': ai_response,
                'ai_response_generated': True,
                'ai_last_prompt': ai_config._format_prompt(record_data),
            }})

            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'AI Response Generated',
                    'message': 'AI response has been generated successfully.',
                    'type': 'success',
                }}
            }}

        except Exception as e:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'AI Error',
                    'message': f'Failed to generate AI response: {{str(e)}}',
                    'type': 'danger',
                }}
            }}

    def _prepare_ai_data(self):
        \"\"\"Prepare record data for AI prompt\"\"\"
        self.ensure_one()

        data = {{
            'name': self.name or '',
            'state': self.state or '',
            'create_date': self.create_date.strftime('%Y-%m-%d %H:%M:%S') if self.create_date else '',
        }}

        # Add all form fields
        {% for field in fields %}
        {% if field.field_type in ['char', 'text'] %}
        data['{{ field.name }}'] = getattr(self, '{{ field.name }}', '') or ''
        {% elif field.field_type in ['integer', 'float'] %}
        data['{{ field.name }}'] = getattr(self, '{{ field.name }}', 0) or 0
        {% elif field.field_type == 'boolean' %}
        data['{{ field.name }}'] = getattr(self, '{{ field.name }}', False)
        {% elif field.field_type == 'selection' %}
        data['{{ field.name }}'] = getattr(self, '{{ field.name }}', '') or ''
        {% elif field.field_type == 'many2one' %}
        field_value = getattr(self, '{{ field.name }}', False)
        data['{{ field.name }}'] = field_value.name if field_value else ''
        {% else %}
        data['{{ field.name }}'] = str(getattr(self, '{{ field.name }}', '')) or ''
        {% endif %}
        {% endfor %}

        return data

    def action_generate_ai_pdf(self):
        \"\"\"Generate PDF from AI response\"\"\"
        self.ensure_one()

        if not self.ai_response:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'No AI Response',
                    'message': 'Please generate AI response first.',
                    'type': 'warning',
                }}
            }}

        try:
            # Generate PDF from AI response
            pdf_content = self._generate_pdf_from_html(self.ai_response)
            filename = f"ai_response_{{self.name or 'record'}}.pdf"

            self.write({{
                'ai_response_pdf': pdf_content,
                'ai_response_pdf_filename': filename,
            }})

            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'PDF Generated',
                    'message': f'PDF has been generated: {{filename}}',
                    'type': 'success',
                }}
            }}

        except Exception as e:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'PDF Generation Failed',
                    'message': f'Failed to generate PDF: {{str(e)}}',
                    'type': 'danger',
                }}
            }}

    def _generate_pdf_from_html(self, html_content):
        \"\"\"Generate PDF from HTML content\"\"\"
        # Use Odoo's built-in PDF generation
        try:
            # Create a simple HTML template
            html_template = f\"\"\"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8"/>
                <title>AI Response - {{self.name}}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ text-align: center; margin-bottom: 30px; }}
                    .content {{ line-height: 1.6; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>AI Response Report</h1>
                    <p>Record: {{self.name}}</p>
                    <p>Generated: {{fields.Datetime.now().strftime('%Y-%m-%d %H:%M:%S')}}</p>
                </div>
                <div class="content">
                    {{html_content}}
                </div>
            </body>
            </html>
            \"\"\"

            # Use Odoo's report engine to generate PDF
            pdf = self.env['ir.actions.report']._render_qweb_pdf(
                'ai_response_report', [self.id], data={{'html_content': html_template}}
            )[0]

            import base64
            return base64.b64encode(pdf)

        except Exception as e:
            # Fallback: return HTML as text
            import base64
            return base64.b64encode(html_content.encode('utf-8'))
"""

        # Prepare template data
        model_class = ''.join(word.capitalize() for word in form.code.split('_'))
        workflow_states = template.workflow_state_ids.sorted('sequence')
        default_state = workflow_states[0].code if workflow_states else 'draft'

        template_obj = Template(model_template)
        model_content = template_obj.render(
            model_class=model_class,
            model_name=form.model_name,
            model_description=form.description or form.name,
            fields=form.field_definition_ids.sorted('sequence'),
            workflow_states=workflow_states,
            default_state=default_state,
            form=form,
        )

        model_path = os.path.join(module_path, 'models', f'{form.code}.py')
        with open(model_path, 'w', encoding='utf-8') as f:
            f.write(model_content)

    def _get_module_dependencies(self, template):
        """Get the list of module dependencies"""
        base_deps = ['base', 'web', 'mail', 'portal', 'website', 'project', 'account', 'payment']

        # Add dependencies based on template configuration
        for form in template.form_builder_ids:
            if form.create_sales_order:
                base_deps.extend(['sale', 'sale_management'])
            if form.enable_payment:
                base_deps.extend(['website_payment', 'payment_custom'])
            if form.enable_document_generation:
                base_deps.extend(['ai_document_generation'])

        # Remove duplicates and return as string representation
        unique_deps = list(set(base_deps))
        return str(unique_deps)

    def _generate_views(self, template, module_config, module_path):
        """Generate view files for each form"""
        for form in template.form_builder_ids:
            self._generate_form_views(template, form, module_config, module_path)

        # Generate menu file
        self._generate_menu_file(template, module_config, module_path)

    def _generate_form_views(self, template, form, module_config, module_path):
        """Generate views for a single form"""
        view_template = """<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- {{ form.name }} Views -->

    <!-- Tree View -->
    <record id="{{ form.code }}_view_tree" model="ir.ui.view">
        <field name="name">{{ form.name }} Tree</field>
        <field name="model">{{ form.model_name }}</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                {% for field in visible_fields %}
                <field name="{{ field.name }}"{% if field.tree_view_optional != 'show' %} optional="{{ field.tree_view_optional }}"{% endif %}/>
                {% endfor %}
                <field name="state" decoration-info="state=='draft'" decoration-success="state=='approved'"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="{{ form.code }}_view_form" model="ir.ui.view">
        <field name="name">{{ form.name }} Form</field>
        <field name="model">{{ form.model_name }}</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_submit" type="object" string="Submit"
                            class="btn-primary" states="draft"/>
                    <button name="action_approve" type="object" string="Approve"
                            class="btn-primary" states="submitted" groups="base.group_user"/>
                    <!-- ✨ AI Response Buttons -->
                    <button name="action_get_ai_response" type="object" string="Get AI Response"
                            class="btn-secondary" icon="fa-magic"
                            invisible="ai_response_generated == True"/>
                    <button name="action_generate_ai_pdf" type="object" string="Generate PDF"
                            class="btn-info" icon="fa-file-pdf-o"
                            invisible="ai_response_generated == False"
                            groups="base.group_user"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,approved"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            {% for field in left_fields %}
                            <field name="{{ field.name }}"{% if field.widget %} widget="{{ field.widget }}"{% endif %}{% if field.required %} required="1"{% endif %}/>
                            {% endfor %}
                        </group>
                        <group>
                            {% for field in right_fields %}
                            <field name="{{ field.name }}"{% if field.widget %} widget="{{ field.widget }}"{% endif %}{% if field.required %} required="1"{% endif %}/>
                            {% endfor %}
                        </group>
                    </group>
                    {% if bottom_fields %}
                    <group>
                        {% for field in bottom_fields %}
                        <field name="{{ field.name }}"{% if field.widget %} widget="{{ field.widget }}"{% endif %}{% if field.required %} required="1"{% endif %}/>
                        {% endfor %}
                    </group>
                    {% endif %}
                    <!-- ✨ AI Response Section -->
                    <notebook>
                        <page string="AI Response" invisible="ai_response_generated == False">
                            <group>
                                <field name="ai_response" widget="html" nolabel="1"/>
                                <field name="ai_response_pdf" filename="ai_response_pdf_filename"
                                       invisible="ai_response_pdf == False"/>
                                <field name="ai_response_pdf_filename" invisible="1"/>
                                <field name="ai_last_prompt" readonly="1" string="Last Prompt Used"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="{{ form.code }}_view_search" model="ir.ui.view">
        <field name="name">{{ form.name }} Search</field>
        <field name="model">{{ form.model_name }}</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                {% for field in searchable_fields %}
                <field name="{{ field.name }}"/>
                {% endfor %}
                <filter string="Draft" name="draft" domain="[('state','=','draft')]"/>
                <filter string="Submitted" name="submitted" domain="[('state','=','submitted')]"/>
                <filter string="Approved" name="approved" domain="[('state','=','approved')]"/>
                <separator/>
                <filter string="My Records" name="my_records" domain="[('user_id','=',uid)]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by':'state'}"/>
                    <filter string="Responsible User" name="group_user" context="{'group_by':'user_id'}"/>
                    <filter string="Creation Date" name="group_create_date" context="{'group_by':'create_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="{{ form.code }}_action" model="ir.actions.act_window">
        <field name="name">{{ form.name }}</field>
        <field name="res_model">{{ form.model_name }}</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="{{ form.code }}_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first {{ form.name|lower }}!
            </p>
        </field>
    </record>
</odoo>"""

        # Organize fields by position
        all_fields = form.field_definition_ids.sorted('sequence')
        visible_fields = all_fields.filtered(lambda f: f.tree_view_visible)
        left_fields = all_fields.filtered(lambda f: f.form_view_position in ['top', 'middle'])[:len(all_fields)//2]
        right_fields = all_fields.filtered(lambda f: f.form_view_position in ['top', 'middle'])[len(all_fields)//2:]
        bottom_fields = all_fields.filtered(lambda f: f.form_view_position == 'bottom')
        searchable_fields = all_fields.filtered(lambda f: f.search_view_filter)

        template_obj = Template(view_template)
        view_content = template_obj.render(
            form=form,
            visible_fields=visible_fields,
            left_fields=left_fields,
            right_fields=right_fields,
            bottom_fields=bottom_fields,
            searchable_fields=searchable_fields,
        )

        view_path = os.path.join(module_path, 'views', f'{form.code}_views.xml')
        with open(view_path, 'w', encoding='utf-8') as f:
            f.write(view_content)

    def _generate_menu_file(self, template, module_config, module_path):
        """Generate the menu structure file"""
        module_name = module_config.get('module_name') or template.get_module_name()

        menu_template = """<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Main Menu -->
    <menuitem id="{{ module_name }}_main_menu"
              name="{{ module_title }}"
              sequence="10"/>

    {% for form in forms %}
    <!-- {{ form.name }} Menu -->
    <menuitem id="{{ form.code }}_menu"
              name="{{ form.name }}"
              parent="{{ module_name }}_main_menu"
              action="{{ form.code }}_action"
              sequence="{{ loop.index * 10 }}"/>
    {% endfor %}

    <!-- Configuration Menu -->
    <menuitem id="{{ module_name }}_config_menu"
              name="Configuration"
              parent="{{ module_name }}_main_menu"
              sequence="100"
              groups="base.group_system"/>
</odoo>"""

        template_obj = Template(menu_template)
        menu_content = template_obj.render(
            module_name=module_name,
            module_title=module_config.get('title', template.name),
            forms=template.form_builder_ids,
        )

        menu_path = os.path.join(module_path, 'views', f'{module_name}_menus.xml')
        with open(menu_path, 'w', encoding='utf-8') as f:
            f.write(menu_content)

    def _generate_controllers(self, template, module_config, module_path):
        """Generate controller files"""
        module_name = module_config.get('module_name') or template.get_module_name()

        # Generate main controllers
        self._generate_main_controllers(template, module_config, module_path)

        # Generate API controllers
        self._generate_api_controllers(template, module_config, module_path)

    def _generate_main_controllers(self, template, module_config, module_path):
        """Generate main website and portal controllers"""
        module_name = module_config.get('module_name') or template.get_module_name()

        controller_template = """from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.addons.website.controllers.main import Website
from odoo.exceptions import AccessError, MissingError, ValidationError
import json
import logging

_logger = logging.getLogger(__name__)


class {{ module_class }}Website(http.Controller):
    \"\"\"Website controllers for {{ module_name }}\"\"\"

    {% for form in forms %}
    @http.route(['/{{ module_name }}/{{ form.code }}'], type='http', auth='public', website=True)
    def {{ form.code }}_form(self, **kwargs):
        \"\"\"Display {{ form.name }} form\"\"\"
        values = {}

        if request.httprequest.method == 'POST':
            return self._process_{{ form.code }}_submission(kwargs)

        # Get form configuration
        values.update({
            'form_title': '{{ form.name }}',
            'form_description': '{{ form.description or "" }}',
            'fields': self._get_{{ form.code }}_fields(),
        })

        return request.render('{{ module_name }}.{{ form.code }}_form_template', values)

    def _process_{{ form.code }}_submission(self, post_data):
        \"\"\"Process {{ form.name }} form submission\"\"\"
        try:
            # Validate form data
            validated_data = self._validate_{{ form.code }}_data(post_data)

            # Create submission record
            submission = request.env['{{ form.model_name }}'].sudo().create(validated_data)

            # Redirect to thank you page
            return request.redirect(f'/{{ module_name }}/{{ form.code }}/thank-you?submission_id={submission.id}')

        except ValidationError as e:
            return request.render('{{ module_name }}.{{ form.code }}_form_template', {
                'error': str(e),
                'form_data': post_data,
                'form_title': '{{ form.name }}',
                'fields': self._get_{{ form.code }}_fields(),
            })

    def _validate_{{ form.code }}_data(self, data):
        \"\"\"Validate {{ form.name }} form data\"\"\"
        validated = {}

        # Add partner_id if user is logged in
        if request.env.user and request.env.user != request.env.ref('base.public_user'):
            validated['partner_id'] = request.env.user.partner_id.id

        {% for field in form.field_definition_ids %}
        {% if field.website_form_visible %}
        # Validate {{ field.name }}
        if '{{ field.name }}' in data:
            {% if field.required or field.website_form_required %}
            if not data.get('{{ field.name }}'):
                raise ValidationError(_('{{ field.field_description }} is required'))
            {% endif %}
            validated['{{ field.name }}'] = data['{{ field.name }}']
        {% endif %}
        {% endfor %}

        return validated

    def _get_{{ form.code }}_fields(self):
        \"\"\"Get {{ form.name }} form fields configuration\"\"\"
        return [
            {% for field in form.field_definition_ids %}
            {% if field.website_form_visible %}
            {
                'name': '{{ field.name }}',
                'label': '{{ field.field_description }}',
                'type': '{{ field.field_type }}',
                'required': {{ 'True' if field.website_form_required else 'False' }},
                'placeholder': '{{ field.website_form_placeholder or "" }}',
                {% if field.field_type == 'selection' %}
                'options': {{ field.get_selection_options() }},
                {% endif %}
            },
            {% endif %}
            {% endfor %}
        ]

    @http.route(['/{{ module_name }}/{{ form.code }}/thank-you'], type='http', auth='public', website=True)
    def {{ form.code }}_thank_you(self, submission_id=None, **kwargs):
        \"\"\"Thank you page for {{ form.name }}\"\"\"
        values = {
            'form_title': '{{ form.name }}',
            'submission_id': submission_id,
        }

        if submission_id:
            try:
                submission = request.env['{{ form.model_name }}'].sudo().browse(int(submission_id))
                if submission.exists():
                    values['submission'] = submission
            except:
                pass

        return request.render('{{ module_name }}.{{ form.code }}_thank_you_template', values)
    {% endfor %}


class {{ module_class }}Portal(CustomerPortal):
    \"\"\"Portal controllers for {{ module_name }}\"\"\"

    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)

        {% for form in forms %}
        {% if form.portal_access %}
        if '{{ form.code }}_count' in counters:
            {{ form.code }}_count = request.env['{{ form.model_name }}'].search_count([
                ('partner_id', '=', request.env.user.partner_id.id)
            ]) if request.env.user.partner_id else 0
            values['{{ form.code }}_count'] = {{ form.code }}_count
        {% endif %}
        {% endfor %}

        return values
    {% endfor %}
"""

        # Prepare template data
        module_class = ''.join(word.capitalize() for word in module_name.split('_'))

        template_obj = Template(controller_template)
        controller_content = template_obj.render(
            module_name=module_name,
            module_class=module_class,
            forms=template.form_builder_ids,
        )

        controller_path = os.path.join(module_path, 'controllers', f'{module_name}_controllers.py')
        with open(controller_path, 'w', encoding='utf-8') as f:
            f.write(controller_content)

    def _generate_api_controllers(self, template, module_config, module_path):
        """Generate REST API controllers"""
        module_name = module_config.get('module_name') or template.get_module_name()

        api_template = """from odoo import http, _
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)


class {{ module_class }}API(http.Controller):
    \"\"\"REST API controllers for {{ module_name }}\"\"\"

    {% for form in forms %}
    {% if form.api_enabled %}
    @http.route(['/api/{{ module_name }}/{{ form.code }}'], type='json', auth='user', methods=['GET'])
    def api_get_{{ form.code }}(self, **kwargs):
        \"\"\"Get {{ form.name }} submissions\"\"\"
        try:
            domain = [('partner_id', '=', request.env.user.partner_id.id)]

            # Add filters from kwargs
            if 'state' in kwargs:
                domain.append(('state', '=', kwargs['state']))

            records = request.env['{{ form.model_name }}'].search(domain)

            return {
                'success': True,
                'data': [{
                    'id': rec.id,
                    'name': rec.name,
                    'state': rec.state,
                    'create_date': rec.create_date.isoformat() if rec.create_date else None,
                    {% for field in form.field_definition_ids %}
                    {% if field.api_visible %}
                    '{{ field.name }}': getattr(rec, '{{ field.name }}', None),
                    {% endif %}
                    {% endfor %}
                } for rec in records]
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route(['/api/{{ module_name }}/{{ form.code }}'], type='json', auth='user', methods=['POST'])
    def api_create_{{ form.code }}(self, **kwargs):
        \"\"\"Create {{ form.name }} submission\"\"\"
        try:
            # Validate required fields
            {% for field in form.field_definition_ids %}
            {% if field.required %}
            if '{{ field.name }}' not in kwargs:
                return {'success': False, 'error': '{{ field.field_description }} is required'}
            {% endif %}
            {% endfor %}

            # Add partner_id
            kwargs['partner_id'] = request.env.user.partner_id.id

            # Create record
            record = request.env['{{ form.model_name }}'].create(kwargs)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    {% endif %}
    {% endfor %}
"""

        # Prepare template data
        module_class = ''.join(word.capitalize() for word in module_name.split('_'))

        template_obj = Template(api_template)
        api_content = template_obj.render(
            module_name=module_name,
            module_class=module_class,
            forms=template.form_builder_ids,
        )

        api_path = os.path.join(module_path, 'controllers', 'api_controllers.py')
        with open(api_path, 'w', encoding='utf-8') as f:
            f.write(api_content)

    def _generate_security(self, template, module_config, module_path):
        """Generate security files"""
        module_name = module_config.get('module_name') or template.get_module_name()

        # Generate security groups
        self._generate_security_groups(template, module_config, module_path)

        # Generate access rights
        self._generate_access_rights(template, module_config, module_path)

    def _generate_data_files(self, template, module_config, module_path):
        """Generate data files"""
        module_name = module_config.get('module_name') or template.get_module_name()

        # Generate sequences
        self._generate_sequences(template, module_config, module_path)

        # Generate initial data
        self._generate_initial_data(template, module_config, module_path)

    def _generate_website_templates(self, template, module_config, module_path):
        """Generate website templates for public forms with enhanced step-by-step functionality"""
        module_name = module_config.get('module_name') or template.get_module_name()

        website_template = """<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Website Templates for {{ module_name }} -->

    {% for form in forms %}
    {% if form.website_published %}

    {% if form.enable_step_form %}
    <!-- {{ form.name }} Enhanced Step-by-Step Form Template -->
    <template id="{{ form.code }}_website_form" name="{{ form.name }} Enhanced Form">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Enhanced Form Styling -->
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                <style>
                    .enhanced-form-wrapper {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                        padding: 40px 0;
                    }
                    .form-progress-steps {
                        display: flex;
                        justify-content: center;
                        margin-bottom: 40px;
                        background: white;
                        padding: 30px;
                        border-radius: 15px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                    }
                    .step-item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        position: relative;
                        flex: 1;
                        max-width: 150px;
                    }
                    .step-number {
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                        background: #e9ecef;
                        color: #6c757d;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                        font-size: 18px;
                        margin-bottom: 10px;
                        transition: all 0.3s ease;
                    }
                    .step-item.active .step-number {
                        background: #007bff;
                        color: white;
                        transform: scale(1.1);
                    }
                    .step-item.completed .step-number {
                        background: #28a745;
                        color: white;
                    }
                    .step-label {
                        font-size: 14px;
                        font-weight: 600;
                        color: #495057;
                        text-align: center;
                    }
                    .form-header-card {
                        background: white;
                        border-radius: 15px;
                        padding: 40px;
                        margin-bottom: 30px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                        text-align: center;
                    }
                    .form-title {
                        color: #2c3e50;
                        font-size: 2.5rem;
                        font-weight: 700;
                        margin-bottom: 15px;
                    }
                    .form-description {
                        color: #6c757d;
                        font-size: 1.1rem;
                        margin-bottom: 25px;
                    }
                    .form-meta {
                        display: flex;
                        justify-content: center;
                        gap: 30px;
                        flex-wrap: wrap;
                    }
                    .form-meta-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        color: #495057;
                        font-size: 0.9rem;
                    }
                    .main-form-card {
                        background: white;
                        border-radius: 15px;
                        padding: 40px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                        margin-bottom: 30px;
                    }
                    .form-section {
                        margin-bottom: 40px;
                    }
                    .form-section-title {
                        color: #2c3e50;
                        font-size: 1.4rem;
                        font-weight: 600;
                        margin-bottom: 25px;
                        padding-bottom: 10px;
                        border-bottom: 2px solid #e9ecef;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    .nice-form-group {
                        margin-bottom: 25px;
                    }
                    .nice-form-group label {
                        font-weight: 600;
                        color: #495057;
                        margin-bottom: 8px;
                        display: block;
                    }
                    .nice-form-group label.required::after {
                        content: " *";
                        color: #dc3545;
                    }
                    .form-control {
                        border: 2px solid #e9ecef;
                        border-radius: 8px;
                        padding: 12px 15px;
                        font-size: 1rem;
                        transition: all 0.3s ease;
                    }
                    .form-control:focus {
                        border-color: #007bff;
                        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                    }
                    .btn-submit-enhanced {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border: none;
                        color: white;
                        padding: 15px 40px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border-radius: 50px;
                        transition: all 0.3s ease;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }
                    .btn-submit-enhanced:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                        color: white;
                    }
                    .form-submit-section {
                        text-align: center;
                        padding: 30px 0;
                        border-top: 2px solid #e9ecef;
                        margin-top: 40px;
                    }
                </style>
            </t>
            <div id="wrap" class="enhanced-form-wrapper">
                <!-- Progress Steps -->
                <div class="container">
                    <div class="form-progress-steps">
                        <div class="step-item active">
                            <div class="step-number">1</div>
                            <div class="step-label">Form Details</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-label">Review</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-label">Complete</div>
                        </div>
                    </div>
                </div>

                <div class="container">
                    <div class="row">
                        <div class="col-lg-10 offset-lg-1">
                            <!-- Form Header Card -->
                            <div class="form-header-card">
                                <div class="form-header-content">
                                    <h1 class="form-title">{{ form.name }}</h1>
                                    <p class="form-description">{{ form.description or 'Please fill out the form below' }}</p>
                                    <div class="form-meta">
                                        <span class="form-meta-item">
                                            <i class="fas fa-clock"></i>
                                            Estimated time: 5-10 minutes
                                        </span>
                                        <span class="form-meta-item">
                                            <i class="fas fa-shield-alt"></i>
                                            Secure &amp; Confidential
                                        </span>
                                        {% if form.enable_payment %}
                                        <span class="form-meta-item">
                                            <i class="fas fa-credit-card"></i>
                                            Payment: ${{ form.default_payment_rate or 0 }}
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Main Form Card -->
                            <div class="main-form-card">
                                <form id="enhanced-form" action="/{{ module_name }}/{{ form.code }}/submit" method="post" class="enhanced-form" novalidate="novalidate">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <!-- Basic Information Section -->
                                    <div class="form-section">
                                        <h3 class="form-section-title">
                                            <i class="fas fa-user"></i>
                                            Basic Information
                                        </h3>

                                        {% for field in form.field_definition_ids %}
                                        {% if field.website_form_visible and field.form_view_position in ['top', 'middle'] %}
                                        <div class="nice-form-group">
                                            <label for="{{ field.name }}"{% if field.website_form_required %} class="required"{% endif %}>{{ field.field_description }}</label>

                                            {% if field.field_type == 'text' %}
                                            <textarea class="form-control" name="{{ field.name }}"
                                                     {% if field.website_form_required %}required="required"{% endif %}
                                                     placeholder="{{ field.website_form_placeholder or 'Enter ' + field.field_description|lower }}" rows="4"
                                                     data-field-type="text" data-required="{{ 'true' if field.website_form_required else 'false' }}"></textarea>

                                            {% elif field.field_type == 'selection' %}
                                            <select class="form-control" name="{{ field.name }}"
                                                   {% if field.widget %}data-widget="{{ field.widget }}"{% endif %}
                                                   {% if field.website_form_required %}required="required"{% endif %}
                                                   data-field-type="selection" data-required="{{ 'true' if field.website_form_required else 'false' }}">
                                                <option value="">Select {{ field.field_description }}...</option>
                                                {% for option in field.get_selection_options() %}
                                                <option value="{{ option[0] }}">{{ option[1] }}</option>
                                                {% endfor %}
                                            </select>

                                            {% elif field.field_type == 'many2one' %}
                                            <select class="form-control" name="{{ field.name }}"
                                                   data-widget="{{ field.widget or 'many2one_dropdown' }}" data-model="{{ field.relation_model }}"
                                                   {% if field.website_form_required %}required="required"{% endif %}
                                                   data-field-type="many2one" data-required="{{ 'true' if field.website_form_required else 'false' }}"
                                                   data-label="{{ field.field_description }}">
                                                <option value="">Select {{ field.field_description }}...</option>
                                                <!-- Options will be populated dynamically -->
                                            </select>

                                            {% elif field.field_type == 'many2many' %}
                                            <select name="{{ field.name }}" class="form-control" multiple="multiple"
                                                   data-widget="{{ field.widget or 'many2many_tags' }}" data-model="{{ field.relation_model }}"
                                                   {% if field.website_form_required %}required="required"{% endif %}
                                                   data-field-type="many2many" data-required="{{ 'true' if field.website_form_required else 'false' }}">
                                                <!-- Options will be populated dynamically -->
                                            </select>

                                            {% elif field.field_type == 'boolean' %}
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" name="{{ field.name }}" value="1"
                                                      data-field-type="boolean" data-required="{{ 'true' if field.website_form_required else 'false' }}"/>
                                                <label class="form-check-label">{{ field.help_text or field.field_description }}</label>
                                            </div>

                                            {% elif field.field_type == 'binary' %}
                                            <input type="file" class="form-control-file" name="{{ field.name }}"
                                                  {% if field.website_form_required %}required="required"{% endif %}
                                                  data-field-type="binary" data-required="{{ 'true' if field.website_form_required else 'false' }}"/>

                                            {% else %}
                                            <input type="{% if field.field_type == 'email' %}email{% elif field.field_type == 'phone' %}tel{% elif field.field_type == 'url' %}url{% elif field.field_type == 'date' %}date{% elif field.field_type == 'datetime' %}datetime-local{% elif field.field_type in ['integer', 'float', 'monetary'] %}number{% else %}text{% endif %}"
                                                   class="form-control" name="{{ field.name }}"
                                                   {% if field.website_form_required %}required="required"{% endif %}
                                                   data-field-type="{{ field.field_type }}" data-required="{{ 'true' if field.website_form_required else 'false' }}"
                                                   placeholder="{{ field.website_form_placeholder or 'Enter ' + field.field_description|lower }}"/>
                                            {% endif %}

                                            {% if field.help_text %}
                                            <div class="invalid-feedback">Please provide a valid {{ field.field_description|lower }}.</div>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>

                                    {% if form.addon_ids %}
                                    <!-- Available Add-ons Section -->
                                    <div class="form-section">
                                        <h3 class="form-section-title">
                                            <i class="fas fa-plus-circle"></i>
                                            Available Add-ons
                                        </h3>
                                        {% for addon in form.addon_ids %}
                                        <div class="addon-item">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" name="addon_{{ addon.id }}" value="{{ addon.id }}"
                                                      {% if addon.is_required %}checked="checked" disabled="disabled"{% endif %}/>
                                                <label class="form-check-label">
                                                    <strong>{{ addon.name }}</strong>
                                                    {% if addon.price > 0 %}
                                                    <span class="badge badge-info">+${{ addon.price }}</span>
                                                    {% endif %}
                                                    {% if addon.is_required %}
                                                    <span class="badge badge-warning">Required</span>
                                                    {% endif %}
                                                </label>
                                            </div>
                                            {% if addon.description %}
                                            <p class="text-muted small">{{ addon.description }}</p>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <!-- Submit Section -->
                                    <div class="form-submit-section">
                                        <button type="submit" class="btn btn-submit-enhanced" data-original-text="Submit {{ form.name }}">
                                            <i class="fas fa-paper-plane"></i>
                                            Submit {{ form.name }}
                                        </button>
                                        <p class="form-text mt-3">
                                            <i class="fas fa-lock"></i>
                                            Your information is secure and will be processed confidentially.
                                        </p>
                                    </div>
                                </form>
                            </div>

                            {% if form.faq_ids %}
                            <!-- FAQs Section -->
                            <div class="main-form-card">
                                <h3 class="form-section-title">
                                    <i class="fas fa-question-circle"></i>
                                    Frequently Asked Questions
                                </h3>
                                {% for faq in form.faq_ids %}
                                <div class="faq-item">
                                    <div class="faq-question" onclick="toggleFAQ(this)">
                                        <strong>{{ faq.question }}</strong>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="faq-answer">
                                        <p>{{ faq.answer }}</p>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <!-- Success Section (Hidden initially) -->
                            <div class="main-form-card" id="success-section" style="display: none;">
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                    <h2 class="mt-3 mb-3">Thank You!</h2>
                                    <p class="lead">Your {{ form.name|lower }} has been submitted successfully.</p>
                                    <p class="text-muted">You will receive a confirmation email shortly.</p>
                                    <a href="/{{ module_name }}" class="btn btn-primary mt-3">
                                        <i class="fas fa-home"></i>
                                        Back to Home
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Form JavaScript -->
                <script>
                    function toggleFAQ(element) {
                        const answer = element.nextElementSibling;
                        const icon = element.querySelector('i');

                        if (answer.style.display === 'block') {
                            answer.style.display = 'none';
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        } else {
                            answer.style.display = 'block';
                            icon.classList.remove('fa-chevron-down');
                            icon.classList.add('fa-chevron-up');
                        }
                    }
                </script>
            </div>
        </t>
    </template>

    {% else %}
    <!-- {{ form.name }} Standard Form Template -->
    <template id="{{ form.code }}_website_form" name="{{ form.name }} Form">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h2 class="card-title mb-0">{{ form.name }}</h2>
                                    {% if form.description %}
                                    <p class="card-text text-muted">{{ form.description }}</p>
                                    {% endif %}
                                </div>
                                <div class="card-body">
                                    <form method="post" action="/{{ module_name }}/{{ form.code }}/submit" class="s_website_form" enctype="multipart/form-data">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                        {% for field in form.field_definition_ids %}
                                        {% if field.website_form_visible %}
                                        <div class="form-group row">
                                            <label class="col-lg-3 col-md-4 col-form-label" for="{{ field.name }}">
                                                {{ field.field_description }}
                                                {% if field.website_form_required %}<span class="text-danger">*</span>{% endif %}
                                            </label>
                                            <div class="col-lg-7 col-md-8">
                                                {% if field.field_type == 'text' %}
                                                <textarea class="form-control" name="{{ field.name }}" id="{{ field.name }}"
                                                         {% if field.website_form_required %}required="required"{% endif %}
                                                         placeholder="{{ field.website_form_placeholder or '' }}" rows="3"></textarea>
                                                {% elif field.field_type == 'selection' %}
                                                <select class="form-control" name="{{ field.name }}" id="{{ field.name }}"
                                                       {% if field.website_form_required %}required="required"{% endif %}>
                                                    <option value="">-- Select {{ field.field_description }} --</option>
                                                    {% for option in field.get_selection_options() %}
                                                    <option value="{{ option[0] }}">{{ option[1] }}</option>
                                                    {% endfor %}
                                                </select>
                                                {% elif field.field_type == 'boolean' %}
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" name="{{ field.name }}" id="{{ field.name }}" value="1"/>
                                                    <label class="form-check-label" for="{{ field.name }}">{{ field.help_text or field.field_description }}</label>
                                                </div>
                                                {% elif field.field_type == 'binary' %}
                                                <input type="file" class="form-control-file" name="{{ field.name }}" id="{{ field.name }}"
                                                      {% if field.website_form_required %}required="required"{% endif %}/>
                                                {% else %}
                                                <input type="{% if field.field_type == 'email' %}email{% elif field.field_type == 'phone' %}tel{% elif field.field_type == 'url' %}url{% elif field.field_type == 'date' %}date{% elif field.field_type == 'datetime' %}datetime-local{% elif field.field_type in ['integer', 'float', 'monetary'] %}number{% else %}text{% endif %}"
                                                       class="form-control" name="{{ field.name }}" id="{{ field.name }}"
                                                       {% if field.website_form_required %}required="required"{% endif %}
                                                       placeholder="{{ field.website_form_placeholder or '' }}"/>
                                                {% endif %}
                                                {% if field.help_text %}
                                                <small class="form-text text-muted">{{ field.help_text }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}

                                        <div class="form-group row">
                                            <div class="offset-lg-3 col-lg-7 offset-md-4 col-md-8">
                                                <button type="submit" class="btn btn-primary btn-lg">Submit {{ form.name }}</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
    {% endif %}

    <!-- {{ form.name }} Thank You Template -->
    <template id="{{ form.code }}_thank_you_template" name="{{ form.name }} Thank You">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card mt-4">
                                <div class="card-body text-center">
                                    <div class="mb-4">
                                        <i class="fa fa-check-circle fa-5x text-success"></i>
                                    </div>
                                    <h2 class="card-title">Thank You!</h2>
                                    <p class="card-text">Your {{ form.name|lower }} has been submitted successfully.</p>
                                    <t t-if="submission_id">
                                        <p class="card-text">
                                            <strong>Reference Number:</strong>
                                            <span class="badge badge-info">
                                                <t t-esc="submission.name if submission else submission_id"/>
                                            </span>
                                        </p>
                                        {% if form.enable_payment %}
                                        <p class="card-text">
                                            {% if form.payment_type == 'admin_approval' %}
                                            We will review your request and send you payment details shortly.
                                            {% else %}
                                            Please proceed with payment to complete your request.
                                            {% endif %}
                                        </p>
                                        {% endif %}
                                    </t>
                                    <div class="mt-4">
                                        <a href="/my/account" class="btn btn-primary">View in My Account</a>
                                        <a href="/" class="btn btn-secondary ml-2">Back to Home</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
    {% endif %}
    {% endfor %}
</odoo>"""

        template_obj = Template(website_template)
        website_content = template_obj.render(
            module_name=module_name,
            forms=template.form_builder_ids,
        )

        website_path = os.path.join(module_path, 'views', 'website_templates.xml')
        with open(website_path, 'w', encoding='utf-8') as f:
            f.write(website_content)

    def _generate_portal_templates(self, template, module_config, module_path):
        """Generate portal templates for customer access"""
        module_name = module_config.get('module_name') or template.get_module_name()

        portal_template = """<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Portal Templates for {{ module_name }} -->

    {% for form in forms %}
    {% if form.portal_access %}
    <!-- {{ form.name }} Portal List Template -->
    <template id="portal_my_{{ form.code }}" name="My {{ form.name }}">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">{{ form.name }}</t>
            </t>

            <t t-if="not {{ form.code }}_records">
                <div class="alert alert-warning mt-3" role="alert">
                    <p class="mb-0">
                        <strong>No {{ form.name|lower }} found.</strong>
                    </p>
                    <p class="mb-0">
                        <a href="/{{ module_name }}/{{ form.code }}" class="btn btn-primary btn-sm mt-2">
                            Create New {{ form.name }}
                        </a>
                    </p>
                </div>
            </t>

            <t t-if="{{ form.code }}_records" t-call="portal.portal_table">
                <thead>
                    <tr class="active">
                        <th>Reference</th>
                        <th>Date</th>
                        <th>Status</th>
                        {% if form.create_sales_order %}
                        <th>Order Status</th>
                        <th>Payment Status</th>
                        {% endif %}
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <t t-foreach="{{ form.code }}_records" t-as="{{ form.code }}">
                        <tr>
                            <td>
                                <a t-attf-href="/my/{{ form.code }}/#{{{ form.code }}.id}">
                                    <t t-esc="{{ form.code }}.name"/>
                                </a>
                            </td>
                            <td>
                                <span t-field="{{ form.code }}.create_date" t-options="{'widget': 'date'}"/>
                            </td>
                            <td>
                                <span class="badge badge-pill"
                                      t-attf-class="badge-{{'success' if {{ form.code }}.state == 'approved' else 'info' if {{ form.code }}.state == 'submitted' else 'secondary'}}">
                                    <t t-esc="{{ form.code }}.state.title()"/>
                                </span>
                            </td>
                            {% if form.create_sales_order %}
                            <td>
                                <t t-if="{{ form.code }}.sale_order_id">
                                    <span class="badge badge-pill"
                                          t-attf-class="badge-{{'success' if {{ form.code }}.sale_order_state == 'sale' else 'warning' if {{ form.code }}.sale_order_state == 'draft' else 'info'}}">
                                        <t t-esc="{{ form.code }}.sale_order_state.title()"/>
                                    </span>
                                </t>
                                <t t-else="">
                                    <span class="text-muted">No Order</span>
                                </t>
                            </td>
                            <td>
                                <t t-if="{{ form.code }}.payment_status">
                                    <span class="badge badge-pill"
                                          t-attf-class="badge-{{'success' if {{ form.code }}.payment_status == 'paid' else 'danger' if {{ form.code }}.payment_status == 'failed' else 'warning'}}">
                                        <t t-esc="{{ form.code }}.payment_status.title()"/>
                                    </span>
                                </t>
                                <t t-else="">
                                    <span class="text-muted">No Payment</span>
                                </t>
                            </td>
                            {% endif %}
                            <td>
                                <a t-attf-href="/my/{{ form.code }}/#{{{ form.code }}.id}"
                                   class="btn btn-sm btn-secondary">
                                    <i class="fa fa-eye"/> View
                                </a>
                                {% if form.enable_payment %}
                                <t t-if="{{ form.code }}.payment_status == 'pending' and {{ form.code }}.sale_order_id">
                                    <a t-attf-href="/my/orders/#{{{ form.code }}.sale_order_id.id}"
                                       class="btn btn-sm btn-primary ml-1">
                                        <i class="fa fa-credit-card"/> Pay Now
                                    </a>
                                </t>
                                {% endif %}
                            </td>
                        </tr>
                    </t>
                </tbody>
            </t>
        </t>
    </template>

    <!-- {{ form.name }} Portal Detail Template -->
    <template id="portal_{{ form.code }}_detail" name="{{ form.name }} Detail">
        <t t-call="portal.portal_layout">
            <t t-call="portal.portal_record_layout">
                <t t-set="card_header">
                    <div class="row no-gutters">
                        <div class="col-12">
                            <h5 class="d-flex mb-1 mb-md-0">
                                <span t-field="{{ form.code }}.name"/>
                                <small class="text-muted d-none d-md-inline"> (#<span t-field="{{ form.code }}.id"/>)</small>
                            </h5>
                        </div>
                    </div>
                </t>
                <t t-set="card_body">
                    <div class="row">
                        <div class="col-12 col-lg-6">
                            <strong>Status:</strong>
                            <span class="badge badge-pill ml-1"
                                  t-attf-class="badge-{{'success' if {{ form.code }}.state == 'approved' else 'info' if {{ form.code }}.state == 'submitted' else 'secondary'}}">
                                <t t-esc="{{ form.code }}.state.title()"/>
                            </span>
                        </div>
                        <div class="col-12 col-lg-6">
                            <strong>Date:</strong>
                            <span t-field="{{ form.code }}.create_date" t-options="{'widget': 'date'}"/>
                        </div>
                    </div>

                    {% if form.create_sales_order %}
                    <t t-if="{{ form.code }}.sale_order_id">
                        <hr/>
                        <div class="row">
                            <div class="col-12 col-lg-6">
                                <strong>Sales Order:</strong>
                                <a t-attf-href="/my/orders/#{{{ form.code }}.sale_order_id.id}">
                                    <t t-esc="{{ form.code }}.sale_order_id.name"/>
                                </a>
                            </div>
                            <div class="col-12 col-lg-6">
                                <strong>Order Status:</strong>
                                <span class="badge badge-pill ml-1"
                                      t-attf-class="badge-{{'success' if {{ form.code }}.sale_order_state == 'sale' else 'warning' if {{ form.code }}.sale_order_state == 'draft' else 'info'}}">
                                    <t t-esc="{{ form.code }}.sale_order_state.title()"/>
                                </span>
                            </div>
                        </div>

                        {% if form.enable_payment %}
                        <div class="row mt-2">
                            <div class="col-12 col-lg-6">
                                <strong>Payment Status:</strong>
                                <span class="badge badge-pill ml-1"
                                      t-attf-class="badge-{{'success' if {{ form.code }}.payment_status == 'paid' else 'danger' if {{ form.code }}.payment_status == 'failed' else 'warning'}}">
                                    <t t-esc="{{ form.code }}.payment_status.title()"/>
                                </span>
                            </div>
                            <div class="col-12 col-lg-6">
                                <t t-if="{{ form.code }}.payment_status == 'pending'">
                                    <a t-attf-href="/my/orders/#{{{ form.code }}.sale_order_id.id}"
                                       class="btn btn-primary btn-sm">
                                        <i class="fa fa-credit-card"/> Pay Now
                                    </a>
                                </t>
                            </div>
                        </div>
                        {% endif %}
                    </t>
                    {% endif %}

                    <hr/>
                    <h6>Form Details</h6>
                    <div class="row">
                        {% for field in form.field_definition_ids %}
                        {% if field.portal_visible %}
                        <div class="col-12 col-lg-6 mb-2">
                            <strong>{{ field.field_description }}:</strong>
                            <t t-if="field.field_type == 'boolean'">
                                <span t-if="{{ form.code }}.{{ field.name }}" class="text-success">
                                    <i class="fa fa-check"/> Yes
                                </span>
                                <span t-else="" class="text-muted">
                                    <i class="fa fa-times"/> No
                                </span>
                            </t>
                            <t t-elif="field.field_type == 'binary'">
                                <t t-if="{{ form.code }}.{{ field.name }}">
                                    <a t-attf-href="/web/content/{{ form.model_name }}/#{{{ form.code }}.id}/{{ field.name }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fa fa-download"/> Download
                                    </a>
                                </t>
                                <span t-else="" class="text-muted">No file</span>
                            </t>
                            <t t-else="">
                                <span t-field="{{ form.code }}.{{ field.name }}"/>
                            </t>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </t>
            </t>
        </t>
    </template>
    {% endif %}
    {% endfor %}

    <!-- Portal Menu Items -->
    {% for form in forms %}
    {% if form.portal_access %}
    <template id="portal_my_home_menu_{{ form.code }}" name="{{ form.name }} Link" inherit_id="portal.portal_my_home" priority="40">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="title">{{ form.name }}</t>
                <t t-set="url" t-value="'/my/{{ form.code }}'"/>
                <t t-set="placeholder_count" t-value="'{{ form.code }}_count'"/>
            </t>
        </xpath>
    </template>
    {% endif %}
    {% endfor %}
</odoo>"""

        template_obj = Template(portal_template)
        portal_content = template_obj.render(
            module_name=module_name,
            forms=template.form_builder_ids,
        )

        portal_path = os.path.join(module_path, 'views', 'portal_templates.xml')
        with open(portal_path, 'w', encoding='utf-8') as f:
            f.write(portal_content)

    def _generate_api_endpoints(self, template, module_config, module_path):
        """Generate REST API endpoints"""
        # Implementation for API endpoints
        pass

    def _install_module(self, module_path, module_config):
        """Install the generated module"""
        # Implementation for module installation
        pass

    def _create_generated_module_record(self, template, module_config, module_path):
        """Create a record for the generated module"""
        module_name = module_config.get('module_name') or template.get_module_name()

        generated_module = self.env['generated.module'].create({
            'name': module_config.get('title', template.name),
            'code': module_name,
            'template_id': template.id,
            'module_path': module_path,
            'generation_config': json.dumps(module_config),
            'state': 'generated',
        })

        return generated_module
