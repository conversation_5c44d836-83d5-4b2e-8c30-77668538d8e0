<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Generate Sequence Numbers For Case Registration, Evidence And Trial-->
    <data noupdate="1">
        <record id="sequence_case_registration" model="ir.sequence">
            <field name="name">Case Sequence</field>
            <field name="code">case_registration</field>
            <field name="prefix">CASE000</field>
            <field name="company_id" eval="False"/>
        </record>
        <record id="sequence_case_evidence" model="ir.sequence">
            <field name="name">Case Evidence Sequence</field>
            <field name="code">case_evidence</field>
            <field name="prefix">EV000</field>
            <field name="company_id" eval="False"/>
        </record>
        <record id="sequence_legal_trial" model="ir.sequence">
            <field name="name">Legal Case Sequence</field>
            <field name="code">legal_trial</field>
            <field name="prefix">TR000</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
</odoo>
