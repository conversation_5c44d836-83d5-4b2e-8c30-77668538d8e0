<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Complete Legal Services Application Tree View -->
    <record id="legal_services_complete_tree_view" model="ir.ui.view">
        <field name="name">Complete Legal Services Application Tree</field>
        <field name="model">ovakil_legal_services_complete.legal_services_complete</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
<field name="client_name" optional="show"/> 
<field name="client_email" optional="show"/> 
<field name="client_phone" optional="show"/> 
<field name="assigned_lawyer" optional="hide"/> 
<field name="document_lines" optional="hide"/> 
<field name="consultation_notes" optional="hide"/> 
<field name="payment_history" optional="hide"/> 
<field name="case_updates" optional="hide"/> 
<field name="state"/>
<field name="create_date"/>
            </tree>
        </field>
    </record>
    <!-- Complete Legal Services Application Form View -->
    <record id="legal_services_complete_form_view" model="ir.ui.view">
        <field name="name">Complete Legal Services Application Form</field>
        <field name="model">ovakil_legal_services_complete.legal_services_complete</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_draft_to_submitted" type="object" string="Draft → Submitted"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_submitted_to_consultation_scheduled" type="object" string="Submitted → Consultation Scheduled"
                            class="btn-primary" invisible="state != 'submitted'"/>
                    <button name="action_consultation_scheduled_to_payment_pending" type="object" string="Consultation Scheduled → Payment Pending"
                            class="btn-primary" invisible="state != 'consultation_scheduled'"/>
                    <button name="action_payment_pending_to_in_progress" type="object" string="Payment Pending → In Progress"
                            class="btn-primary" invisible="state != 'payment_pending'"/>
                    <button name="action_in_progress_to_document_review" type="object" string="In Progress → Document Review"
                            class="btn-primary" invisible="state != 'in_progress'"/>
                    <button name="action_document_review_to_completed" type="object" string="Document Review → Completed"
                            class="btn-primary" invisible="state != 'document_review'"/>
                    <!-- ✨ Admin Approval Buttons -->
                    <button name="action_approve_admin" type="object" string="Approve"
                            class="btn-success" invisible="requires_admin_approval == False or admin_approval_status != 'pending'"
                            groups="base.group_system"/>
                    <button name="action_reject_admin" type="object" string="Reject"
                            class="btn-danger" invisible="requires_admin_approval == False or admin_approval_status != 'pending'"
                            groups="base.group_system"/>
                    <button name="action_request_payment" type="object" string="Request Payment"
                            class="btn-warning" invisible="payment_status != 'none'"/>
                    <button name="action_record_cash_payment" type="object" string="Record Cash Payment"
                            class="btn-success" invisible="payment_status != 'pending'"/>
                    <button name="action_refresh_payment_status" type="object" string="Refresh Payment Status"
                            class="btn-info" invisible="payment_status not in ['pending', 'none']"/>
                    <button name="action_view_sales_order" type="object" string="View Sales Order"
                            class="btn-info" invisible="not sales_order_id"/>
                    <button name="action_open_payment_portal" type="object" string="Open Payment Portal"
                            class="btn-secondary" invisible="not payment_url"/>
                    <!-- ✨ AI Response Buttons -->
                    <button name="action_get_ai_response" type="object" string="Get AI Response"
                            class="btn-secondary" icon="fa-magic"
                            invisible="ai_response_generated == True"/>
                    <button name="action_generate_ai_pdf" type="object" string="Regenerate PDF"
                            class="btn-info" icon="fa-file-pdf-o"
                            invisible="ai_response == False"
                            help="Generate fresh PDF from current AI response content"
                            groups="base.group_user"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,consultation_scheduled,payment_pending,in_progress,document_review,completed"/>
                </header>
                <sheet>
                    <group>
                        <group name="basic_info">
                            <field name="name"/>
                        <field name="client_name"/> 
                        <field name="client_email"/> 
                        <field name="client_phone"/> 
                        <field name="service_type"/> 
                        <field name="urgency_level"/> 
                        </group>
                        <group name="relational_info">
                        <field name="assigned_lawyer" domain="[(&apos;active&apos;, &apos;=&apos;, True)]" options="{'no_create': False, 'no_open': False}"/> 
                        <field name="document_lines" widget="one2many_list"/> 
                        <field name="consultation_notes" widget="one2many_list"/> 
                        <field name="payment_history" widget="one2many_list"/> 
                        <field name="case_updates" widget="one2many_list"/> 
                            <field name="active"/>
                        </group>
                    </group>
                    <group name="payment_info" string="Payment Information">
                        <group>
                            <field name="payment_rate"/>
                            <field name="payment_amount" readonly="1"/>
                            <field name="payment_status"/>
                        </group>
                        <group>
                            <field name="rate_changed_by" readonly="1"/>
                            <field name="rate_changed_date" readonly="1"/>
                            <field name="sales_order_id" readonly="1"/>
                            <field name="payment_url" readonly="1"/>
                        </group>
                    </group>
                    <group name="addon_info" string="Add-on Services">
                        <group>
                            <field name="addon_ids" widget="many2many_tags" options="{'no_create': False}"/>
                            <field name="addon_total_amount" readonly="1"/>
                            <field name="total_amount" readonly="1" class="oe_subtotal_footer_separator"/>
                        </group>
                        <group>
                            <field name="requires_admin_approval"/>
                            <field name="admin_approval_status" invisible="requires_admin_approval == False"/>
                        </group>
                    </group>
                        <field name="case_details" widget="text"/> 

                    <!-- ✨ AI Response Section -->
                    <!-- Hidden fields for AI functionality -->
                    <field name="ai_response_generated" invisible="1"/>
                    <field name="ai_response_pdf" invisible="1"/>
                    <field name="ai_response_pdf_filename" invisible="1"/>

                    <notebook>
                        <page string="AI Response &amp; Document Draft">
                            <group>
                                <div class="alert alert-info" invisible="ai_response_generated == True">
                                    <p><strong>AI Response &amp; Document Draft</strong></p>
                                    <p>Use this section to:</p>
                                    <ul>
                                        <li>Generate AI response using the "Get AI Response" button above</li>
                                        <li>Manually create or edit document content</li>
                                        <li>Format content using the rich text editor</li>
                                        <li>Generate PDF for customer download</li>
                                    </ul>
                                </div>
                                <field name="ai_response" widget="html" nolabel="1"
                                       placeholder="Click 'Get AI Response' button to generate AI content, or manually type your document content here..."/>
                                <separator string="AI Response Details" invisible="ai_response_generated == False"/>
                                <group invisible="ai_response_generated == False">
                                    <field name="ai_last_prompt" readonly="1" string="Last AI Prompt Used"/>
                                    <field name="ai_response_pdf" filename="ai_response_pdf_filename"
                                           invisible="ai_response_pdf == False"/>
                                    <field name="ai_response_pdf_filename" invisible="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <!-- Complete Legal Services Application Kanban View -->
    <record id="legal_services_complete_kanban_view" model="ir.ui.view">
        <field name="name">Complete Legal Services Application Kanban</field>
        <field name="model">ovakil_legal_services_complete.legal_services_complete</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column" quick_create="false">
                <field name="id"/>
                <field name="name"/>
                <field name="state"/>
                <field name="color"/>

                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="{{kanban_color(record.color.raw_value)}} oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="o_dropdown_kanban dropdown">
                                    <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                        <span class="fa fa-ellipsis-v"/>
                                    </a>
                                    <div class="dropdown-menu" role="menu">
                                        <a t-if="widget.editable" role="menuitem" type="edit" class="dropdown-item">Edit</a>
                                        <a t-if="widget.deletable" role="menuitem" type="delete" class="dropdown-item">Delete</a>
                                        <div role="separator" class="dropdown-divider"/>
                                        <div class="dropdown-item-text text-muted">
                                            <small>ID: <field name="id"/></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                        <div class="o_kanban_record_subtitle">
                            <field name="client_name"/>
                        </div>
                        <div class="o_kanban_record_subtitle">
                            <field name="client_email"/>
                        </div>
                            </div>

                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Complete Legal Services Application Action -->
    <record id="legal_services_complete_action" model="ir.actions.act_window">
        <field name="name">Complete Legal Services Application</field>
        <field name="res_model">ovakil_legal_services_complete.legal_services_complete</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
                                      (0, 0, {'view_mode': 'kanban', 'view_id': ref('legal_services_complete_kanban_view')}),
                                      (0, 0, {'view_mode': 'tree', 'view_id': ref('legal_services_complete_tree_view')}),
                                      (0, 0, {'view_mode': 'form', 'view_id': ref('legal_services_complete_form_view')})]"/>
        <field name="context">{'group_by': 'state'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first complete legal services application!
            </p>
            <p>
                Click the "New" button to create a new complete legal services application.
            </p>
        </field>
    </record>

    <!-- Complete Legal Services Application Menu -->
    <menuitem id="legal_services_complete_menu" name="Complete Legal Services Application" action="legal_services_complete_action" sequence="10"/>
</odoo>