<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Cron job to update payment status for Complete Legal Services Application -->
        <record id="ir_cron_update_payment_status_legal_services_complete" model="ir.cron">
            <field name="name">Complete Legal Services Application: Update Payment Status</field>
            <field name="model_id" ref="model_ovakil_legal_services_complete_legal_services_complete"/>
            <field name="state">code</field>
            <field name="code">model._cron_update_payment_status()</field>
            <field name="interval_number">30</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>