#!/usr/bin/env python3
"""
Comprehensive AI Module Generator Test Suite
===========================================

This script provides end-to-end testing of the AI Module Generator:
1. Connects to Odoo via XML-RPC
2. Generates a test module using AI Module Generator
3. Tests module installation and activation
4. Analyzes logs for issues
5. Checks frontend functionality and field visibility
6. Applies fixes back to the generator if needed

Author: AI Assistant
Version: 1.0.0
"""

import xmlrpc.client
import time
import logging
import os
import sys
import subprocess
import json
import re
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import shutil

# Import existing modules
from odoo_config import ODOO_CONFIG
from odoo_module_manager import OdooModuleManager

class ComprehensiveModuleTest:
    """Comprehensive test suite for AI Module Generator"""
    
    def __init__(self, config: Dict = None):
        """Initialize the test suite"""
        self.config = config or ODOO_CONFIG
        self.logger = self._setup_logger()
        self.manager = None
        self.test_module_name = None
        self.test_results = {
            'generation': False,
            'installation': False,
            'frontend': False,
            'fields_visibility': False,
            'logs_clean': False
        }
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger('ComprehensiveModuleTest')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def run_complete_test(self) -> bool:
        """Run the complete test suite"""
        self.logger.info("🚀 STARTING COMPREHENSIVE AI MODULE GENERATOR TEST")
        self.logger.info("=" * 70)
        
        try:
            # Step 1: Initialize connection
            if not self._initialize_connection():
                return False
            
            # Step 2: Generate test module
            if not self._generate_test_module():
                return False
            
            # Step 3: Test module installation
            if not self._test_module_installation():
                return False
            
            # Step 4: Check frontend functionality
            if not self._test_frontend_functionality():
                return False
            
            # Step 5: Verify field visibility
            if not self._test_field_visibility():
                return False
            
            # Step 6: Analyze logs
            if not self._analyze_logs():
                return False
            
            # Step 7: Generate report
            self._generate_test_report()
            
            return all(self.test_results.values())
            
        except Exception as e:
            self.logger.error(f"❌ Test suite failed: {str(e)}")
            return False
    
    def _initialize_connection(self) -> bool:
        """Initialize connection to Odoo"""
        try:
            self.logger.info("🔌 Initializing connection to Odoo...")
            self.manager = OdooModuleManager(**self.config)
            self.logger.info("✅ Connection established successfully")
            return True
        except Exception as e:
            self.logger.error(f"❌ Connection failed: {str(e)}")
            return False
    
    def _generate_test_module(self) -> bool:
        """Generate a test module using AI Module Generator"""
        try:
            self.logger.info("🏗️  Generating test module...")
            
            # Create unique test module name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.test_module_name = f"test_ai_gen_{timestamp}"
            
            # Use XML-RPC to trigger module generation
            template_id = self._get_demo_template_id()
            if not template_id:
                self.logger.error("❌ No demo template found")
                return False
            
            # Generate module via wizard
            wizard_id = self._create_generation_wizard(template_id)
            if not wizard_id:
                return False
            
            # Execute generation
            success = self._execute_module_generation(wizard_id)
            if success:
                self.test_results['generation'] = True
                self.logger.info(f"✅ Module '{self.test_module_name}' generated successfully")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Module generation failed: {str(e)}")
            return False
    
    def _get_demo_template_id(self) -> Optional[int]:
        """Get a demo template ID for testing"""
        try:
            # Search for active demo templates
            template_ids = self.manager._execute(
                'module.template', 'search',
                [('state', '=', 'active'), ('template_type', '=', 'form_based')],
                {'limit': 1}
            )
            
            if template_ids:
                return template_ids[0]
            
            self.logger.warning("⚠️  No active demo templates found")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error getting demo template: {str(e)}")
            return None
    
    def _create_generation_wizard(self, template_id: int) -> Optional[int]:
        """Create module generation wizard"""
        try:
            wizard_data = {
                'template_id': template_id,
                'module_name': self.test_module_name,
                'module_title': f'Test AI Generated Module {datetime.now().strftime("%Y%m%d")}',
                'module_description': 'Test module generated by comprehensive test suite',
                'module_author': 'Test Suite',
                'include_website': True,
                'include_portal': True,
                'include_api': True,
            }
            
            wizard_id = self.manager._execute(
                'module.generator.wizard', 'create', wizard_data
            )
            
            self.logger.info(f"✅ Generation wizard created: {wizard_id}")
            return wizard_id
            
        except Exception as e:
            self.logger.error(f"❌ Error creating wizard: {str(e)}")
            return None
    
    def _execute_module_generation(self, wizard_id: int) -> bool:
        """Execute module generation"""
        try:
            # Execute the generation
            result = self.manager._execute(
                'module.generator.wizard', 'action_generate_module', [wizard_id]
            )
            
            # Wait for generation to complete
            time.sleep(5)
            
            # Check if module directory was created
            module_path = f"/mnt/extra-addons/ovakil_{self.test_module_name}"
            if os.path.exists(module_path):
                self.logger.info(f"✅ Module directory created: {module_path}")
                return True
            else:
                self.logger.error(f"❌ Module directory not found: {module_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error executing generation: {str(e)}")
            return False
    
    def _test_module_installation(self) -> bool:
        """Test module installation and activation"""
        try:
            self.logger.info("📦 Testing module installation...")
            
            module_name = f"ovakil_{self.test_module_name}"
            
            # Update module list first
            self.manager.update_module_list()
            time.sleep(2)
            
            # Install the module
            success = self.manager.install_module(module_name, update_list=False)
            
            if success:
                self.test_results['installation'] = True
                self.logger.info(f"✅ Module '{module_name}' installed successfully")
            else:
                self.logger.error(f"❌ Module '{module_name}' installation failed")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Installation test failed: {str(e)}")
            return False
    
    def _test_frontend_functionality(self) -> bool:
        """Test frontend functionality"""
        try:
            self.logger.info("🌐 Testing frontend functionality...")
            
            # Test website accessibility
            base_url = self.config['url']
            test_urls = [
                f"{base_url}/{self.test_module_name}",
                f"{base_url}/my/{self.test_module_name}",
            ]
            
            success_count = 0
            for url in test_urls:
                try:
                    response = requests.get(url, timeout=10)
                    if response.status_code in [200, 302, 404]:  # 404 is acceptable for non-configured routes
                        success_count += 1
                        self.logger.info(f"✅ URL accessible: {url}")
                    else:
                        self.logger.warning(f"⚠️  URL returned {response.status_code}: {url}")
                except Exception as e:
                    self.logger.warning(f"⚠️  URL test failed: {url} - {str(e)}")
            
            # Consider test successful if at least one URL is accessible
            success = success_count > 0
            if success:
                self.test_results['frontend'] = True
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Frontend test failed: {str(e)}")
            return False

    def _test_field_visibility(self) -> bool:
        """Test field visibility in forms"""
        try:
            self.logger.info("👁️  Testing field visibility...")

            module_name = f"ovakil_{self.test_module_name}"

            # Get model info from the generated module
            model_name = f"{module_name}.{self.test_module_name}"

            try:
                # Get field information
                fields_info = self.manager._execute(
                    model_name, 'fields_get'
                )

                if fields_info:
                    self.logger.info(f"✅ Found {len(fields_info)} fields in model")

                    # Check for essential fields
                    essential_fields = ['name', 'state', 'active']
                    missing_fields = []

                    for field in essential_fields:
                        if field not in fields_info:
                            missing_fields.append(field)

                    if not missing_fields:
                        self.test_results['fields_visibility'] = True
                        self.logger.info("✅ All essential fields present")
                        return True
                    else:
                        self.logger.warning(f"⚠️  Missing fields: {missing_fields}")
                        return False
                else:
                    self.logger.error("❌ No fields found in model")
                    return False

            except Exception as e:
                # Model might not be accessible, try alternative approach
                self.logger.warning(f"⚠️  Direct model access failed: {str(e)}")

                # Check if views were created
                view_ids = self.manager._execute(
                    'ir.ui.view', 'search',
                    [('model', '=', model_name)]
                )

                if view_ids:
                    self.test_results['fields_visibility'] = True
                    self.logger.info(f"✅ Found {len(view_ids)} views for model")
                    return True
                else:
                    self.logger.error("❌ No views found for model")
                    return False

        except Exception as e:
            self.logger.error(f"❌ Field visibility test failed: {str(e)}")
            return False

    def _analyze_logs(self) -> bool:
        """Analyze Odoo logs for issues"""
        try:
            self.logger.info("📋 Analyzing Odoo logs...")

            # Get recent logs
            log_content = self._get_recent_logs()
            if not log_content:
                self.logger.warning("⚠️  Could not retrieve logs")
                return True  # Don't fail the test if logs are not accessible

            # Analyze for errors
            issues = self._find_log_issues(log_content)

            if not issues:
                self.test_results['logs_clean'] = True
                self.logger.info("✅ No critical issues found in logs")
                return True
            else:
                self.logger.warning(f"⚠️  Found {len(issues)} issues in logs:")
                for issue in issues[:5]:  # Show first 5 issues
                    self.logger.warning(f"   - {issue}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Log analysis failed: {str(e)}")
            return False

    def _get_recent_logs(self, lines: int = 100) -> str:
        """Get recent Odoo logs"""
        try:
            # Try multiple log locations
            log_paths = [
                "/var/log/odoo/odoo-server.log",
                "/var/log/odoo/odoo.log",
                "/opt/odoo/log/odoo-server.log"
            ]

            for log_path in log_paths:
                if os.path.exists(log_path):
                    result = subprocess.run(
                        ["tail", f"-n{lines}", log_path],
                        capture_output=True,
                        text=True
                    )
                    if result.returncode == 0:
                        return result.stdout

            self.logger.warning("⚠️  No accessible log files found")
            return ""

        except Exception as e:
            self.logger.error(f"❌ Failed to get logs: {str(e)}")
            return ""

    def _find_log_issues(self, log_content: str) -> List[str]:
        """Find issues in log content"""
        issues = []

        # Error patterns to look for
        error_patterns = [
            r"ERROR.*?:(.*)",
            r"CRITICAL.*?:(.*)",
            r"Traceback.*",
            r"KeyError.*",
            r"AttributeError.*",
            r"ImportError.*",
            r"SyntaxError.*",
            r"ValidationError.*",
            r"AccessError.*"
        ]

        for pattern in error_patterns:
            matches = re.findall(pattern, log_content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if isinstance(match, str):
                    issues.append(match.strip())
                else:
                    issues.append(str(match))

        # Remove duplicates
        return list(set(issues))

    def _generate_test_report(self):
        """Generate comprehensive test report"""
        self.logger.info("📊 GENERATING TEST REPORT")
        self.logger.info("=" * 50)

        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        success_rate = (passed_tests / total_tests) * 100

        self.logger.info(f"📋 Test Module: {self.test_module_name}")
        self.logger.info(f"📈 Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        self.logger.info("")

        # Detailed results
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")

        self.logger.info("")

        if success_rate == 100:
            self.logger.info("🎉 ALL TESTS PASSED!")
            self.logger.info("✅ AI Module Generator is working correctly!")
        elif success_rate >= 80:
            self.logger.info("⚠️  Most tests passed, minor issues detected")
            self._suggest_fixes()
        else:
            self.logger.info("❌ Multiple issues detected")
            self._suggest_fixes()

        self.logger.info("=" * 50)

    def _suggest_fixes(self):
        """Suggest fixes based on failed tests"""
        self.logger.info("🔧 SUGGESTED FIXES:")

        if not self.test_results['generation']:
            self.logger.info("   - Check AI Module Generator wizard configuration")
            self.logger.info("   - Verify template data and field definitions")

        if not self.test_results['installation']:
            self.logger.info("   - Check module manifest dependencies")
            self.logger.info("   - Verify model definitions and security files")

        if not self.test_results['frontend']:
            self.logger.info("   - Check website controller generation")
            self.logger.info("   - Verify template and route configuration")

        if not self.test_results['fields_visibility']:
            self.logger.info("   - Check field definitions in models")
            self.logger.info("   - Verify view generation and field visibility")

        if not self.test_results['logs_clean']:
            self.logger.info("   - Review Odoo logs for specific errors")
            self.logger.info("   - Fix any Python syntax or import issues")

    def cleanup_test_module(self):
        """Clean up test module after testing"""
        try:
            if self.test_module_name and self.manager:
                module_name = f"ovakil_{self.test_module_name}"

                # Uninstall module
                self.logger.info(f"🧹 Cleaning up test module: {module_name}")
                self.manager.uninstall_module(module_name)

                # Remove module directory
                module_path = f"/mnt/extra-addons/{module_name}"
                if os.path.exists(module_path):
                    shutil.rmtree(module_path)
                    self.logger.info(f"✅ Removed module directory: {module_path}")

        except Exception as e:
            self.logger.warning(f"⚠️  Cleanup failed: {str(e)}")


def main():
    """Main function to run the comprehensive test"""
    print("🚀 AI MODULE GENERATOR COMPREHENSIVE TEST SUITE")
    print("=" * 60)

    # Initialize test suite
    test_suite = ComprehensiveModuleTest()

    try:
        # Run complete test
        success = test_suite.run_complete_test()

        # Return appropriate exit code
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test suite failed: {str(e)}")
        sys.exit(1)
    finally:
        # Cleanup
        test_suite.cleanup_test_module()


if __name__ == "__main__":
    main()
